import type { RequestParams } from '@/types/api/request.types'

/**
 * 用户特权搜索请求类型定义
 */
export interface PrivilegeSearchRequest extends RequestParams {
  /** 搜索关键词 */
  searchKey?: string
  /** 是否只搜索自己的内容 */
  owner?: boolean
  /** 是否包含互动内容 */
  interaction?: boolean
  /** 是否只搜索收藏的内容 */
  favorite?: boolean
  /** 标签筛选条件 */
  tag?: string
  /** 最后一条记录的ID，用于基于游标的分页 */
  id?: number
  /** 加载数量限制，默认20条 */
  loadSize?: number
}
