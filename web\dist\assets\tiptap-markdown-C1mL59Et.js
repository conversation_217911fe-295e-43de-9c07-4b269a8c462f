import{E as e,i as t,N as r,M as n,g as i}from"./@tiptap-BlbpWldy.js";import{M as o,d as s}from"./prosemirror-markdown-BKJFDS0M.js";import{M as a}from"./markdown-it-DV9r8h9J.js";import{b as l,F as d}from"./prosemirror-model-BwtArlLQ.js";import{t as c}from"./markdown-it-task-lists-Dj-XbLVy.js";import{a as m,P as u}from"./prosemirror-state-Dg8eoqF5.js";var p=Object.defineProperty,h=(e,t,r)=>(((e,t,r)=>{t in e?p(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r})(e,"symbol"!=typeof t?t+"":t,r),r);const g=e.create({name:"markdownTightLists",addOptions:()=>({tight:!0,tightClass:"tight",listTypes:["bulletList","orderedList"]}),addGlobalAttributes(){return[{types:this.options.listTypes,attributes:{tight:{default:this.options.tight,parseHTML:e=>"true"===e.getAttribute("data-tight")||!e.querySelector("p"),renderHTML:e=>({class:e.tight?this.options.tightClass:null,"data-tight":e.tight?"true":null})}}}]},addCommands(){var e=this;return{toggleTight:function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;return r=>{let{editor:n,commands:i}=r;return e.options.listTypes.some((e=>function(e){if(!n.isActive(e))return!1;const r=n.getAttributes(e);return i.updateAttributes(e,{tight:null!=t?t:!(null!=r&&r.tight)})}(e)))}}}}}),f=a();function k(e,t){f.inline.State.prototype.scanDelims.call({src:e,posMax:e.length});return new f.inline.State(e,null,null,[]).scanDelims(t,!0)}function w(e,t,r,n){let i=e.substring(0,r)+e.substring(r+t.length);return i=i.substring(0,r+n)+t+i.substring(r+n),i}function x(e,t,r,n){let i={text:e,from:r,to:n};return i=function(e,t,r,n){let i=r,o=e;for(;i<n&&!k(o,i).can_open;)o=w(o,t,i,1),i++;return{text:o,from:i,to:n}}(i.text,t,i.from,i.to),i=function(e,t,r,n){let i=n,o=e;for(;i>r&&!k(o,i).can_close;)o=w(o,t,i,-1),i--;return{text:o,from:r,to:i}}(i.text,t,i.from,i.to),i.to-i.from<t.length+1&&(i.text=i.text.substring(0,i.from)+i.text.substring(i.to+t.length)),i.text}class v extends o{constructor(e,t,r){super(e,t,null!=r?r:{}),h(this,"inTable",!1),this.inlines=[]}render(e,t,r){super.render(e,t,r);const n=this.inlines[this.inlines.length-1];if(null!=n&&n.start&&null!=n&&n.end){const{delimiter:e,start:t,end:r}=this.normalizeInline(n);this.out=x(this.out,e,t,r),this.inlines.pop()}}markString(e,t,r,n){const i=this.marks[e.type.name];if(i.expelEnclosingWhitespace)if(t)this.inlines.push({start:this.out.length,delimiter:i.open});else{const e=this.inlines.pop();this.inlines.push({...e,end:this.out.length})}return super.markString(e,t,r,n)}normalizeInline(e){let{start:t,end:r}=e;for(;this.out.charAt(t).match(/\s/);)t++;return{...e,start:t}}}const b=n.create({name:"markdownHTMLMark",addStorage:()=>({markdown:{serialize:{open(e,t){var r,n;return this.editor.storage.markdown.options.html&&null!==(r=null===(n=z(t))||void 0===n?void 0:n[0])&&void 0!==r?r:""},close(e,t){var r,n;return this.editor.storage.markdown.options.html&&null!==(r=null===(n=z(t))||void 0===n?void 0:n[1])&&void 0!==r?r:""}},parse:{}}})});function z(e){const t=e.type.schema,r=t.text(" ",[e]),n=i(d.from(r),t).match(/^(<.*?>) (<\/.*?>)$/);return n?[n[1],n[2]]:null}function C(e){const t=`<body>${e}</body>`;return(new window.DOMParser).parseFromString(t,"text/html").body}const y=r.create({name:"markdownHTMLNode",addStorage:()=>({markdown:{serialize(e,t,r){this.editor.storage.markdown.options.html?e.write(function(e,t){const r=e.type.schema,n=i(d.from(e),r);if(e.isBlock&&(t instanceof d||t.type.name===r.topNodeType.name))return function(e){const t=C(e),r=t.firstElementChild;return r.innerHTML=r.innerHTML.trim()?`\n${r.innerHTML}\n`:"\n",r.outerHTML}(n);return n}(t,r)):e.write(`[${t.type.name}]`),t.isBlock&&e.closeBlock(t)},parse:{}}})});const S=r.create({name:"blockquote"}).extend({addStorage:()=>({markdown:{serialize:s.nodes.blockquote,parse:{}}})}),T=r.create({name:"bulletList"}).extend({addStorage:()=>({markdown:{serialize(e,t){return e.renderList(t,"  ",(()=>(this.editor.storage.markdown.options.bulletListMarker||"-")+" "))},parse:{}}})}),M=r.create({name:"codeBlock"}).extend({addStorage:()=>({markdown:{serialize(e,t){e.write("```"+(t.attrs.language||"")+"\n"),e.text(t.textContent,!1),e.ensureNewLine(),e.write("```"),e.closeBlock(t)},parse:{setup(e){var t;e.set({langPrefix:null!==(t=this.options.languageClassPrefix)&&void 0!==t?t:"language-"})},updateDOM(e){e.innerHTML=e.innerHTML.replace(/\n<\/code><\/pre>/g,"</code></pre>")}}}})}),L=r.create({name:"hardBreak"}).extend({addStorage:()=>({markdown:{serialize(e,t,r,n){for(let i=n+1;i<r.childCount;i++)if(r.child(i).type!=t.type)return void e.write(e.inTable?y.storage.markdown.serialize.call(this,e,t,r):"\\\n")},parse:{}}})}),E=r.create({name:"heading"}).extend({addStorage:()=>({markdown:{serialize:s.nodes.heading,parse:{}}})}),O=r.create({name:"horizontalRule"}).extend({addStorage:()=>({markdown:{serialize:s.nodes.horizontal_rule,parse:{}}})}),N=r.create({name:"image"}).extend({addStorage:()=>({markdown:{serialize:s.nodes.image,parse:{}}})}),B=r.create({name:"listItem"}).extend({addStorage:()=>({markdown:{serialize:s.nodes.list_item,parse:{}}})});const H=r.create({name:"orderedList"}).extend({addStorage:()=>({markdown:{serialize(e,t,r,n){const i=t.attrs.start||1,o=String(i+t.childCount-1).length,s=e.repeat(" ",o+2),a=function(e,t,r){let n=0;for(;r-n>0&&t.child(r-n-1).type.name===e.type.name;n++);return n}(t,r,n),l=a%2?") ":". ";e.renderList(t,s,(t=>{const r=String(i+t);return e.repeat(" ",o-r.length)+r+l}))},parse:{}}})}),j=r.create({name:"paragraph"}).extend({addStorage:()=>({markdown:{serialize:s.nodes.paragraph,parse:{}}})});function A(e){var t,r;return null!==(t=null==e||null===(r=e.content)||void 0===r?void 0:r.content)&&void 0!==t?t:[]}const P=r.create({name:"table"}).extend({addStorage:()=>({markdown:{serialize(e,t,r){!function(e){const t=A(e),r=t[0],n=t.slice(1);if(A(r).some((e=>"tableHeader"!==e.type.name||$(e)||e.childCount>1)))return!1;if(n.some((e=>A(e).some((e=>"tableHeader"===e.type.name||$(e)||e.childCount>1)))))return!1;return!0}(t)?y.storage.markdown.serialize.call(this,e,t,r):(e.inTable=!0,t.forEach(((t,r,n)=>{if(e.write("| "),t.forEach(((t,r,n)=>{n&&e.write(" | ");const i=t.firstChild;i.textContent.trim()&&e.renderInline(i)})),e.write(" |"),e.ensureNewLine(),!n){const r=Array.from({length:t.childCount}).map((()=>"---")).join(" | ");e.write(`| ${r} |`),e.ensureNewLine()}})),e.closeBlock(t),e.inTable=!1)},parse:{}}})});function $(e){return e.attrs.colspan>1||e.attrs.rowspan>1}const D=r.create({name:"taskItem"}).extend({addStorage:()=>({markdown:{serialize(e,t){const r=t.attrs.checked?"[x]":"[ ]";e.write(`${r} `),e.renderContent(t)},parse:{updateDOM(e){[...e.querySelectorAll(".task-list-item")].forEach((e=>{const t=e.querySelector("input");e.setAttribute("data-type","taskItem"),t&&(e.setAttribute("data-checked",t.checked),t.remove())}))}}}})}),q=r.create({name:"taskList"}).extend({addStorage:()=>({markdown:{serialize:T.storage.markdown.serialize,parse:{setup(e){e.use(c)},updateDOM(e){[...e.querySelectorAll(".contains-task-list")].forEach((e=>{e.setAttribute("data-type","taskList")}))}}}})}),I=r.create({name:"text"}).extend({addStorage:()=>({markdown:{serialize(e,t){var r;e.text(null==(r=t.text)?void 0:r.replace(/</g,"&lt;").replace(/>/g,"&gt;"))},parse:{}}})}),_=n.create({name:"bold"}).extend({addStorage:()=>({markdown:{serialize:s.marks.strong,parse:{}}})}),R=n.create({name:"code"}).extend({addStorage:()=>({markdown:{serialize:s.marks.code,parse:{}}})}),W=n.create({name:"italic"}).extend({addStorage:()=>({markdown:{serialize:s.marks.em,parse:{}}})}),F=n.create({name:"link"}).extend({addStorage:()=>({markdown:{serialize:s.marks.link,parse:{}}})}),G=n.create({name:"strike"}).extend({addStorage:()=>({markdown:{serialize:{open:"~~",close:"~~",expelEnclosingWhitespace:!0},parse:{}}})}),X=[S,T,M,L,E,O,y,N,B,H,j,P,D,q,I,_,R,b,W,F,G];function J(e){var t,r;const n=null===(t=e.storage)||void 0===t?void 0:t.markdown,i=null===(r=X.find((t=>t.name===e.name)))||void 0===r?void 0:r.storage.markdown;return n||i?{...i,...n}:null}class K{constructor(e){h(this,"editor",null),this.editor=e}serialize(e){const t=new v(this.nodes,this.marks,{hardBreakNodeName:L.name});return t.renderContent(e),t.out}get nodes(){var e;return{...Object.fromEntries(Object.keys(this.editor.schema.nodes).map((e=>[e,this.serializeNode(y)]))),...Object.fromEntries(null!==(e=this.editor.extensionManager.extensions.filter((e=>"node"===e.type&&this.serializeNode(e))).map((e=>[e.name,this.serializeNode(e)])))&&void 0!==e?e:[])}}get marks(){var e;return{...Object.fromEntries(Object.keys(this.editor.schema.marks).map((e=>[e,this.serializeMark(b)]))),...Object.fromEntries(null!==(e=this.editor.extensionManager.extensions.filter((e=>"mark"===e.type&&this.serializeMark(e))).map((e=>[e.name,this.serializeMark(e)])))&&void 0!==e?e:[])}}serializeNode(e){var t;return null===(t=J(e))||void 0===t||null===(t=t.serialize)||void 0===t?void 0:t.bind({editor:this.editor,options:e.options})}serializeMark(e){var t;const r=null===(t=J(e))||void 0===t?void 0:t.serialize;return r?{...r,open:"function"==typeof r.open?r.open.bind({editor:this.editor,options:e.options}):r.open,close:"function"==typeof r.close?r.close.bind({editor:this.editor,options:e.options}):r.close}:null}}class Q{constructor(e,t){h(this,"editor",null),h(this,"md",null);let{html:r,linkify:n,breaks:i}=t;this.editor=e,this.md=this.withPatchedRenderer(a({html:r,linkify:n,breaks:i}))}parse(e){let{inline:t}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if("string"==typeof e){this.editor.extensionManager.extensions.forEach((e=>{var t;return null===(t=J(e))||void 0===t||null===(t=t.parse)||void 0===t||null===(t=t.setup)||void 0===t?void 0:t.call({editor:this.editor,options:e.options},this.md)}));const r=C(this.md.render(e));return this.editor.extensionManager.extensions.forEach((e=>{var t;return null===(t=J(e))||void 0===t||null===(t=t.parse)||void 0===t||null===(t=t.updateDOM)||void 0===t?void 0:t.call({editor:this.editor,options:e.options},r)})),this.normalizeDOM(r,{inline:t,content:e}),r.innerHTML}return e}normalizeDOM(e,t){let{inline:r,content:n}=t;return this.normalizeBlocks(e),e.querySelectorAll("*").forEach((e=>{var t;(null===(t=e.nextSibling)||void 0===t?void 0:t.nodeType)!==Node.TEXT_NODE||e.closest("pre")||(e.nextSibling.textContent=e.nextSibling.textContent.replace(/^\n/,""))})),r&&this.normalizeInline(e,n),e}normalizeBlocks(e){const t=Object.values(this.editor.schema.nodes).filter((e=>e.isBlock)).map((e=>{var t;return null===(t=e.spec.parseDOM)||void 0===t?void 0:t.map((e=>e.tag))})).flat().filter(Boolean).join(",");t&&[...e.querySelectorAll(t)].forEach((e=>{e.parentElement.matches("p")&&function(e){const t=e.parentElement,r=t.cloneNode();for(;t.firstChild&&t.firstChild!==e;)r.appendChild(t.firstChild);r.childNodes.length>0&&t.parentElement.insertBefore(r,t),t.parentElement.insertBefore(e,t),0===t.childNodes.length&&t.remove()}(e)}))}normalizeInline(e,t){var r;if(null!==(r=e.firstElementChild)&&void 0!==r&&r.matches("p")){var n,i,o,s;const r=e.firstElementChild,{nextElementSibling:a}=r,l=null!==(n=null===(i=t.match(/^\s+/))||void 0===i?void 0:i[0])&&void 0!==n?n:"",d=a?"":null!==(o=null===(s=t.match(/\s+$/))||void 0===s?void 0:s[0])&&void 0!==o?o:"";if(t.match(/^\n\n/))return void(r.innerHTML=`${r.innerHTML}${d}`);!function(e){const t=e.parentNode;for(;e.firstChild;)t.insertBefore(e.firstChild,e);t.removeChild(e)}(r),e.innerHTML=`${l}${e.innerHTML}${d}`}}withPatchedRenderer(e){const t=e=>function(){const t=e(...arguments);return"\n"===t?t:"\n"===t[t.length-1]?t.slice(0,-1):t};return e.renderer.rules.hardbreak=t(e.renderer.rules.hardbreak),e.renderer.rules.softbreak=t(e.renderer.rules.softbreak),e.renderer.rules.fence=t(e.renderer.rules.fence),e.renderer.rules.code_block=t(e.renderer.rules.code_block),e.renderer.renderToken=t(e.renderer.renderToken.bind(e.renderer)),e}}const U=e.create({name:"markdownClipboard",addOptions:()=>({transformPastedText:!1,transformCopiedText:!1}),addProseMirrorPlugins(){return[new m({key:new u("markdownClipboard"),props:{clipboardTextParser:(e,t,r)=>{if(r||!this.options.transformPastedText)return null;const n=this.editor.storage.markdown.parser.parse(e,{inline:!0});return l.fromSchema(this.editor.schema).parseSlice(C(n),{preserveWhitespace:!0,context:t})},clipboardTextSerializer:e=>this.options.transformCopiedText?this.editor.storage.markdown.serializer.serialize(e.content):null}})]}}),V=e.create({name:"markdown",priority:50,addOptions:()=>({html:!0,tightLists:!0,tightListClass:"tight",bulletListMarker:"-",linkify:!1,breaks:!1,transformPastedText:!1,transformCopiedText:!1}),addCommands(){const e=t.Commands.config.addCommands();return{setContent:(t,r,n)=>i=>e.setContent(i.editor.storage.markdown.parser.parse(t),r,n)(i),insertContentAt:(t,r,n)=>i=>e.insertContentAt(t,i.editor.storage.markdown.parser.parse(r,{inline:!0}),n)(i)}},onBeforeCreate(){this.editor.storage.markdown={options:{...this.options},parser:new Q(this.editor,this.options),serializer:new K(this.editor),getMarkdown:()=>this.editor.storage.markdown.serializer.serialize(this.editor.state.doc)},this.editor.options.initialContent=this.editor.options.content,this.editor.options.content=this.editor.storage.markdown.parser.parse(this.editor.options.content)},onCreate(){this.editor.options.content=this.editor.options.initialContent,delete this.editor.options.initialContent},addStorage:()=>({}),addExtensions(){return[g.configure({tight:this.options.tightLists,tightClass:this.options.tightListClass}),U.configure({transformPastedText:this.options.transformPastedText,transformCopiedText:this.options.transformCopiedText})]}});export{V as M};
