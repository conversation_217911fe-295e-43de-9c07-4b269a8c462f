import api from '@/utils/api/api'
import type { ResponseData } from '@/types/api/response-data.types'
import type {
  VerificationResponse,
  VerificationTimeInfoResponse,
} from '@/types/verification/verification-response.types'
import type { VerificationStartRequest } from '@/types/verification/verification-start-request.types'

/**
 * 特权验证业务模块 API 接口
 * 处理特权验证相关的网络请求
 */
const verificationApi = {
  /** API基础路径 */
  URL: '/core/user-privilege-verifications',

  /**
   * 启动特权验证流程
   * 用户A发起特权验证申请
   * @param request 验证启动请求参数
   * @returns 返回验证流程信息
   */
  startVerification: async (
    request: VerificationStartRequest,
  ): Promise<ResponseData<VerificationResponse>> => {
    const response = await api.post<ResponseData<VerificationResponse>>(
      verificationApi.URL,
      request,
    )
    return response.data
  },

  /**
   * 查询验证流程状态
   * 获取当前验证流程的状态信息
   * @param verificationId 验证流程ID
   * @returns 返回验证流程状态信息
   */
  getVerificationStatus: async (
    verificationId: number,
  ): Promise<ResponseData<VerificationResponse>> => {
    const response = await api.get<ResponseData<VerificationResponse>>(
      `${verificationApi.URL}/${verificationId}/status`,
    )
    return response.data
  },

  /**
   * 提交验证内容（步骤二）
   * 用户B在验证页面提交验证内容
   * @param verificationId 验证流程ID
   * @param content 验证内容
   * @returns 返回提交结果
   */
  submitVerificationContent: async (
    verificationId: number,
    content: string,
  ): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(
      `${verificationApi.URL}/${verificationId}/content`,
      { content },
    )
    return response.data
  },

  /**
   * 触发步骤三
   * 用户B访问验证页面时调用
   * @param verificationId 验证流程ID
   * @returns 返回触发结果
   */
  triggerStepThree: async (verificationId: number): Promise<ResponseData<boolean>> => {
    const response = await api.put<ResponseData<boolean>>(
      `${verificationApi.URL}/${verificationId}/step-three`,
    )
    return response.data
  },

  /**
   * 启动验证计时器
   * 用户点击邮件链接访问页面时调用
   * @param verificationId 验证流程ID
   * @returns 返回时间信息
   */
  startVerificationTimer: async (
    verificationId: number,
  ): Promise<ResponseData<VerificationTimeInfoResponse>> => {
    const response = await api.post<ResponseData<VerificationTimeInfoResponse>>(
      `${verificationApi.URL}/${verificationId}/timer`,
    )
    return response.data
  },

  /**
   * 获取验证剩余时间
   * 查询当前验证流程的剩余时间
   * @param verificationId 验证流程ID
   * @returns 返回时间信息
   */
  getRemainingTime: async (
    verificationId: number,
  ): Promise<ResponseData<VerificationTimeInfoResponse>> => {
    const response = await api.get<ResponseData<VerificationTimeInfoResponse>>(
      `${verificationApi.URL}/${verificationId}/remaining-time`,
    )
    return response.data
  },
}

export default verificationApi
