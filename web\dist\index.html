<!doctype html>
<html lang="">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Shenmo</title>
    <!-- 移除预加载样式，因为它们已在main.ts中直接导入 -->
    <style>
      /* 确保初始渲染避免闪烁 */
      html,
      body {
        background-color: var(--white, #fff);
        color: var(--black, #2e2b29);
      }
    </style>
    <script type="module" crossorigin src="/assets/index-BfubwyDe.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/@vue-C18CzuYV.js">
    <link rel="modulepreload" crossorigin href="/assets/pinia-C1NaKXqp.js">
    <link rel="modulepreload" crossorigin href="/assets/seemly-Cdi3gWV3.js">
    <link rel="modulepreload" crossorigin href="/assets/evtd-hWw0KU7y.js">
    <link rel="modulepreload" crossorigin href="/assets/@css-render-DcoOFqSU.js">
    <link rel="modulepreload" crossorigin href="/assets/vooks-n2t0Q59N.js">
    <link rel="modulepreload" crossorigin href="/assets/vdirs-Bvq7nEML.js">
    <link rel="modulepreload" crossorigin href="/assets/@juggle-BnTvdTVm.js">
    <link rel="modulepreload" crossorigin href="/assets/@emotion-DFFAhID7.js">
    <link rel="modulepreload" crossorigin href="/assets/css-render-7x70jhNC.js">
    <link rel="modulepreload" crossorigin href="/assets/vueuc-BKIYztcR.js">
    <link rel="modulepreload" crossorigin href="/assets/lodash-es-BpE61GNB.js">
    <link rel="modulepreload" crossorigin href="/assets/treemate-D3ikBJ7G.js">
    <link rel="modulepreload" crossorigin href="/assets/date-fns-B2WPOLoy.js">
    <link rel="modulepreload" crossorigin href="/assets/date-fns-tz-DYDqAHgp.js">
    <link rel="modulepreload" crossorigin href="/assets/vite-plugin-node-polyfills-CgB-Lgxu.js">
    <link rel="modulepreload" crossorigin href="/assets/async-validator-Bed4cEOw.js">
    <link rel="modulepreload" crossorigin href="/assets/naive-ui-CyvD4caj.js">
    <link rel="modulepreload" crossorigin href="/assets/vue-router-CM3lNrYT.js">
    <link rel="modulepreload" crossorigin href="/assets/axios-CF6-kBsv.js">
    <link rel="modulepreload" crossorigin href="/assets/orderedmap-6uBVSRPO.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-model-BwtArlLQ.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-transform-2YHSeD6B.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-state-Dg8eoqF5.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-dropcursor-b1zulL1f.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-view-D1p6KQzm.js">
    <link rel="modulepreload" crossorigin href="/assets/w3c-keyname-DcELQ0J3.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-keymap-CxzETJ23.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-gapcursor-De4VkbqI.js">
    <link rel="modulepreload" crossorigin href="/assets/rope-sequence-DIBo4VXF.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-history-B0l72feN.js">
    <link rel="modulepreload" crossorigin href="/assets/linkifyjs-CgqKPF1I.js">
    <link rel="modulepreload" crossorigin href="/assets/@popperjs-B5AGR5A_.js">
    <link rel="modulepreload" crossorigin href="/assets/tippy.js-Cq-jft6S.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-commands-jNaZT4ky.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-schema-list-DJ0wlwD0.js">
    <link rel="modulepreload" crossorigin href="/assets/@tiptap-BlbpWldy.js">
    <link rel="modulepreload" crossorigin href="/assets/mdurl-DbZ9s47_.js">
    <link rel="modulepreload" crossorigin href="/assets/uc.micro-CRGj88R_.js">
    <link rel="modulepreload" crossorigin href="/assets/entities-D_unbCD7.js">
    <link rel="modulepreload" crossorigin href="/assets/linkify-it-DVBmImI5.js">
    <link rel="modulepreload" crossorigin href="/assets/punycode.js-H98b6B6Y.js">
    <link rel="modulepreload" crossorigin href="/assets/markdown-it-DV9r8h9J.js">
    <link rel="modulepreload" crossorigin href="/assets/prosemirror-markdown-BKJFDS0M.js">
    <link rel="modulepreload" crossorigin href="/assets/highlight.js-O_OqoeFy.js">
    <link rel="modulepreload" crossorigin href="/assets/markdown-it-task-lists-Dj-XbLVy.js">
    <link rel="modulepreload" crossorigin href="/assets/tiptap-markdown-C1mL59Et.js">
    <link rel="modulepreload" crossorigin href="/assets/compute-scroll-into-view-Cfyw3hb3.js">
    <link rel="modulepreload" crossorigin href="/assets/scroll-into-view-if-needed-CoacbxIo.js">
    <link rel="modulepreload" crossorigin href="/assets/smooth-scroll-into-view-if-needed-B9OyoO8F.js">
    <link rel="modulepreload" crossorigin href="/assets/lowlight-CRhSxQ06.js">
    <link rel="modulepreload" crossorigin href="/assets/requires-port-CgMabaHb.js">
    <link rel="modulepreload" crossorigin href="/assets/querystringify-B2QvdZsH.js">
    <link rel="modulepreload" crossorigin href="/assets/url-parse-BJt2elfP.js">
    <link rel="modulepreload" crossorigin href="/assets/inherits-BfZYsuMB.js">
    <link rel="modulepreload" crossorigin href="/assets/sockjs-client-CnUIS0PG.js">
    <link rel="modulepreload" crossorigin href="/assets/@stomp-ba8ZO4qr.js">
    <link rel="stylesheet" crossorigin href="/assets/index-C5PDbATQ.css">
  </head>

  <body>
    <div id="app"></div>

  </body>
</html>
