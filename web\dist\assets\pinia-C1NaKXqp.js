import{i as t,L as e,r as n,a as s,M as o,N as c,O as a,P as r,Q as i,R as u,f,n as p,S as l,m as h,t as y}from"./@vue-C18CzuYV.js";
/*!
 * pinia v2.3.1
 * (c) 2025 <PERSON>
 * @license MIT
 */let d;const b=t=>d=t,v=Symbol();function _(t){return t&&"object"==typeof t&&"[object Object]"===Object.prototype.toString.call(t)&&"function"!=typeof t.toJSON}var O,j;function m(){const t=o(!0),e=t.run((()=>n({})));let s=[],c=[];const a=h({install(t){b(a),a._a=t,t.provide(v,a),t.config.globalProperties.$pinia=a,c.forEach((t=>s.push(t))),c=[]},use(t){return this._a?s.push(t):c.push(t),this},_p:s,_a:null,_e:t,_s:new Map,state:e});return a}(j=O||(O={})).direct="direct",j.patchObject="patch object",j.patchFunction="patch function";const $=()=>{};function g(t,e,n,s=$){t.push(e);const o=()=>{const n=t.indexOf(e);n>-1&&(t.splice(n,1),s())};return!n&&i()&&u(o),o}function S(t,...e){t.slice().forEach((t=>{t(...e)}))}const P=t=>t(),E=Symbol(),w=Symbol();function M(t,e){t instanceof Map&&e instanceof Map?e.forEach(((e,n)=>t.set(n,e))):t instanceof Set&&e instanceof Set&&e.forEach(t.add,t);for(const n in e){if(!e.hasOwnProperty(n))continue;const s=e[n],o=t[n];_(o)&&_(s)&&t.hasOwnProperty(n)&&!c(s)&&!a(s)?t[n]=M(o,s):t[n]=s}return t}const x=Symbol();const{assign:I}=Object;function A(t,e,i={},u,l,h){let y;const d=I({actions:{}},i),v={deep:!0};let j,m,A,F=[],N=[];const k=u.state.value[t];let C;function J(e){let n;j=m=!1,"function"==typeof e?(e(u.state.value[t]),n={type:O.patchFunction,storeId:t,events:A}):(M(u.state.value[t],e),n={type:O.patchObject,payload:e,storeId:t,events:A});const s=C=Symbol();p().then((()=>{C===s&&(j=!0)})),m=!0,S(F,n,u.state.value[t])}h||k||(u.state.value[t]={}),n({});const L=h?function(){const{state:t}=i,e=t?t():{};this.$patch((t=>{I(t,e)}))}:$;const Q=(e,n="")=>{if(E in e)return e[w]=n,e;const s=function(){b(u);const n=Array.from(arguments),o=[],c=[];let a;S(N,{args:n,name:s[w],store:W,after:function(t){o.push(t)},onError:function(t){c.push(t)}});try{a=e.apply(this&&this.$id===t?this:W,n)}catch(r){throw S(c,r),r}return a instanceof Promise?a.then((t=>(S(o,t),t))).catch((t=>(S(c,t),Promise.reject(t)))):(S(o,a),a)};return s[E]=!0,s[w]=n,s},R={_p:u,$id:t,$onAction:g.bind(null,N),$patch:J,$reset:L,$subscribe(e,n={}){const s=g(F,e,n.detached,(()=>o())),o=y.run((()=>f((()=>u.state.value[t]),(s=>{("sync"===n.flush?m:j)&&e({storeId:t,type:O.direct,events:A},s)}),I({},v,n))));return s},$dispose:function(){y.stop(),F=[],N=[],u._s.delete(t)}},W=s(R);u._s.set(t,W);const q=(u._a&&u._a.runWithContext||P)((()=>u._e.run((()=>(y=o()).run((()=>e({action:Q})))))));for(const n in q){const e=q[n];if(c(e)&&(!c(B=e)||!B.effect)||a(e))h||(!k||_(z=e)&&z.hasOwnProperty(x)||(c(e)?e.value=k[n]:M(e,k[n])),u.state.value[t][n]=e);else if("function"==typeof e){const t=Q(e,n);q[n]=t,d.actions[n]=e}}var z,B;return I(W,q),I(r(W),q),Object.defineProperty(W,"$state",{get:()=>u.state.value[t],set:t=>{J((e=>{I(e,t)}))}}),u._p.forEach((t=>{I(W,y.run((()=>t({store:W,app:u._a,pinia:u,options:d}))))})),k&&h&&i.hydrate&&i.hydrate(W.$state,k),j=!0,m=!0,W}
/*! #__NO_SIDE_EFFECTS__ */function F(n,s,o){let c,a;const r="function"==typeof s;function i(n,o){const i=e();(n=n||(i?t(v,null):null))&&b(n),(n=d)._s.has(c)||(r?A(c,s,a,n):function(t,e,n){const{state:s,actions:o,getters:c}=e,a=n.state.value[t];let r;r=A(t,(function(){a||(n.state.value[t]=s?s():{});const e=l(n.state.value[t]);return I(e,o,Object.keys(c||{}).reduce(((e,s)=>(e[s]=h(y((()=>{b(n);const e=n._s.get(t);return c[s].call(e,e)}))),e)),{}))}),e,n,0,!0)}(c,a,n));return n._s.get(c)}return"string"==typeof n?(c=n,a=r?o:s):(a=n,c=n.id),i.$id=c,i}export{m as c,F as d};
