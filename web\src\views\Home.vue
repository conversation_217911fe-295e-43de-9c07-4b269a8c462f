<!--
  首页组件
  
  功能说明：
  - 提供文章列表和评论弹幕两种视图模式
  - 支持文章搜索和标签筛选功能
  - 提供文章创建入口
  - 集成用户信息和主题切换功能
  
  主要业务逻辑：
  1. 视图切换：在文章卡片视图和评论弹幕视图之间切换
  2. 搜索功能：支持关键词搜索和标签筛选
  3. 文章管理：提供创建文章的快捷入口
  4. 弹幕控制：在弹幕模式下提供循环和暂停控制
  5. 响应式布局：根据屏幕尺寸调整布局和控件显示
  
  交互特性：
  - 平滑的视图切换动画
  - 实时搜索结果更新
  - 无限滚动加载更多内容
  - 拖拽排序功能（文章卡片模式）
-->
<template>
  <div class="home-layout">
    <!-- 顶部区域 -->
    <div class="home-layout-top">
      <transition name="fade-slide">
        <div class="left-controls-container" v-show="!homeState.isCardVisible.value">
          <div class="control-item">
            <NGradientText type="info" class="control-label">循环：</NGradientText>
            <NSwitch v-model:value="danmaku.danmakuLoop.value" size="small" />
          </div>
          <div class="control-item">
            <NGradientText type="info" class="control-label">暂停：</NGradientText>
            <NSwitch
              v-model:value="danmaku.danmakuPause.value"
              @update:value="
                (val) => danmaku.handleDanmakuPauseChange(val, homeState.commentDanmakuRef.value)
              "
              size="small"
            />
          </div>
        </div>
      </transition>
      <div class="middle-controls-container">
        <!-- 文章按钮容器，包含用于切换显示状态的按钮 -->
        <ToggleButton
          v-model:value="homeState.isCardVisible.value"
          @toggle="handleToggleCardVisibility"
        />

        <!-- 搜索容器 -->
        <SearchBar
          v-model="search.searchCondition.value"
          :placeholder="search.getSearchPlaceholder(homeState.isCardVisible.value)"
          @search="handleSearch"
        />

        <!-- 创建按钮容器 -->
        <CreateButton @click="openCreatePrivilegeDialog" @long-press="handleLongPress" />
      </div>

      <!-- 创建特权弹框 -->
      <PrivilegeModal v-model="showPrivilegeModal" @success="handlePrivilegeSuccess" />

      <!-- 激活码输入弹框 -->
      <ActivationCodeModal v-model="showActivationModal" @success="handleActivationSuccess" />

      <UserInfoGroup />
    </div>

    <!-- 标签栏区域 -->
    <div class="tag-bar-wrapper">
      <TagBar
        v-model="search.searchCondition.value.tag"
        @tagSelected="
          (tagName) =>
            search.handleTagSelected(
              tagName,
              homeState.isCardVisible.value,
              homeState.articleListRef.value,
              homeState.commentDanmakuRef.value,
            )
        "
      />
    </div>

    <!-- 主内容区域：根据视图模式显示文章列表或评论弹幕 -->
    <div class="home-layout-content">
      <!-- 视图切换过渡动画：在文章列表和评论弹幕之间平滑切换 -->
      <transition
        @before-enter="transition.beforeEnter"
        @enter="transition.enter"
        @leave="transition.leave"
        :duration="{ enter: 200, leave: 200 }"
        mode="out-in"
      >
        <!-- 文章列表视图：显示文章卡片，支持搜索和筛选 -->
        <ArticleList
          v-if="homeState.isCardVisible.value"
          :key="'article'"
          :search-condition="search.searchCondition"
          ref="articleListRef"
          @reset="homeState.resetArticleList"
        />

        <!-- 评论弹幕视图：以弹幕形式展示评论内容 -->
        <CommentDanmaku
          v-else
          :key="'comment'"
          :search-condition="search.searchCondition.value"
          :loop="danmaku.danmakuLoop.value"
          :pause="danmaku.danmakuPause.value"
          ref="commentDanmakuRef"
        />
      </transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NSwitch, NGradientText } from 'naive-ui'
import { onMounted, onUnmounted, nextTick, defineAsyncComponent, ref, watch } from 'vue'

// 导入组件
import ArticleList from '@/components/home/<USER>'
import CommentDanmaku from '@/components/home/<USER>'
import CreateButton from '@/components/home/<USER>'
import SearchBar from '@/components/home/<USER>'
import TagBar from '@/components/home/<USER>'
import ToggleButton from '@/components/home/<USER>'
import PrivilegeModal from '@/components/privilege/PrivilegeModal.vue'
import UserInfoGroup from '@/components/user/UserInfoGroup.vue'
// 导入 composables
import { useHomeDanmaku } from '@/composables/home/<USER>'
import { useHomeSearch } from '@/composables/home/<USER>'
import { useHomeState } from '@/composables/home/<USER>'
// 导入工具
import type { ArticleListRef } from '@/types/component/article-list-ref.types'
import type { CommentDanmakuRef } from '@/types/component/comment-danmaku-ref.types'
import type { HomePageEmits } from '@/types/component/home-page-emits.types'
import type { HomePageExpose } from '@/types/component/home-page-expose.types'
import type { HomePageProps } from '@/types/component/home-page-props.types'
import logger from '@/utils/log/log'
import transition from '@/utils/ui/transition'

// 导入类型定义

// 导入异步组件
const ActivationCodeModal = defineAsyncComponent(
  () => import('@/components/privilege/ActivationCodeModal.vue'),
)

// 定义组件 Props
const props = withDefaults(defineProps<HomePageProps>(), {
  initialSearchCondition: undefined,
  autoLoad: true,
})

// 定义组件 Emits
const emit = defineEmits<HomePageEmits>()

// 使用 composables 进行状态管理
const search = useHomeSearch()
const danmaku = useHomeDanmaku()
const homeState = useHomeState()

// 定义组件引用
const articleListRef = ref<ArticleListRef | null>(null)
const commentDanmakuRef = ref<CommentDanmakuRef | null>(null)

// 特权弹框状态
const showPrivilegeModal = ref(false)
const showActivationModal = ref(false)

// 监听组件引用变化，同步到homeState
watch(
  articleListRef,
  (newVal) => {
    homeState.articleListRef.value = newVal
  },
  { immediate: true },
)

watch(
  commentDanmakuRef,
  (newVal) => {
    homeState.commentDanmakuRef.value = newVal
  },
  { immediate: true },
)

// 创建窗口大小调整回调函数
const resizeCallback = homeState.createResizeCallback(danmaku.handleDanmakuResize)

/**
 * 组件挂载时的初始化逻辑
 * 包括状态初始化、搜索条件加载和事件监听器设置
 */
onMounted(async (): Promise<void> => {
  // 初始化首页状态
  homeState.initializeState()

  // 加载保存的搜索条件或使用传入的初始搜索条件
  if (props.initialSearchCondition) {
    search.searchCondition.value = {
      ...search.searchCondition.value,
      ...props.initialSearchCondition,
    }
    search.saveSearchCondition()
  } else {
    search.loadSearchCondition()
  }

  // 等待DOM渲染完成后再进行搜索，确保组件refs已经正确绑定
  await nextTick()

  // 如果启用自动加载，则执行搜索
  if (props.autoLoad) {
    // 添加一个小延迟，确保组件完全初始化
    setTimeout(async () => {
      await handleSearch()
    }, 50)
  }

  // 添加窗口大小变化监听器
  window.addEventListener('resize', resizeCallback)
})

/**
 * 组件卸载时的清理逻辑
 * 清理搜索相关资源和状态相关资源
 */
onUnmounted((): void => {
  // 清理搜索相关资源
  search.cleanup()

  // 清理状态相关资源
  homeState.cleanup(resizeCallback)
})

/**
 * 处理搜索操作
 * @param loadMore - 是否为加载更多数据的搜索
 */
const handleSearch = async (loadMore: boolean = false): Promise<void> => {
  logger.debug('执行搜索操作:', {
    isCardVisible: homeState.isCardVisible.value,
    articleListRef: homeState.articleListRef.value,
    commentDanmakuRef: homeState.commentDanmakuRef.value,
    loadMore,
  })

  // 等待DOM更新完成
  await nextTick()

  // 如果是弹幕视图，等待弹幕组件准备就绪
  if (!homeState.isCardVisible.value) {
    let retryCount = 0
    const maxRetries = 20 // 最多等待2秒 (20 * 100ms)

    while (!homeState.commentDanmakuRef.value && retryCount < maxRetries) {
      await new Promise((resolve) => setTimeout(resolve, 100))
      retryCount++
      logger.debug(`等待弹幕组件准备就绪，重试次数: ${retryCount}`)
    }

    if (!homeState.commentDanmakuRef.value) {
      logger.warn('弹幕组件在等待时间内未能准备就绪，跳过搜索')
      return
    }
  }

  // 执行搜索并触发搜索条件变化事件
  search.search(
    homeState.isCardVisible.value,
    homeState.articleListRef.value,
    homeState.commentDanmakuRef.value,
    loadMore,
  )

  // 触发搜索条件变化事件
  emit('search-condition-change', search.searchCondition.value)
}

/**
 * 处理切换卡片显示状态
 * 在文章列表视图和评论弹幕视图之间切换
 */
const handleToggleCardVisibility = (): void => {
  homeState.toggleCardVisibility(
    homeState.resetArticleList,
    handleSearch,
    danmaku.handleDanmakuSubscription,
  )

  // 触发视图模式变化事件
  emit('view-mode-change', homeState.isCardVisible.value)
}

/**
 * 打开创建特权弹框
 */
const openCreatePrivilegeDialog = (): void => {
  showPrivilegeModal.value = true
}

/**
 * 处理特权创建成功
 * 重置文章列表并重新搜索
 */
const handlePrivilegeSuccess = async (): Promise<void> => {
  homeState.resetArticleList()
  await handleSearch()

  // 触发特权创建成功事件
  emit('privilege-create-success')
}

/**
 * 处理长按事件
 * 显示激活码输入弹框
 */
const handleLongPress = (): void => {
  showActivationModal.value = true
}

/**
 * 处理激活成功
 * 激活码输入成功后的回调
 */
const handleActivationSuccess = (): void => {
  // 激活成功后的处理逻辑可以在这里添加
  // 例如刷新用户信息、显示成功消息等
}

/**
 * 执行搜索操作
 * 暴露给父组件的方法
 */
const performSearch = async (loadMore: boolean = false): Promise<void> => {
  await handleSearch(loadMore)
}

/**
 * 重置文章列表
 * 暴露给父组件的方法
 */
const resetArticleList = (): void => {
  homeState.resetArticleList()
}

/**
 * 切换视图模式
 * 暴露给父组件的方法
 */
const toggleViewMode = (): void => {
  handleToggleCardVisibility()
}

/**
 * 获取当前搜索条件
 * 暴露给父组件的方法
 */
const getCurrentSearchCondition = () => {
  return search.searchCondition.value
}

// 暴露组件方法给父组件
defineExpose<HomePageExpose>({
  performSearch,
  resetArticleList,
  toggleViewMode,
  getCurrentSearchCondition,
})
</script>

<style lang="scss" scoped>
@use '@/styles/home';
</style>
