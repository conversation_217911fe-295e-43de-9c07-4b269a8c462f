import { useMessage } from 'naive-ui'
import { VERIFICATION_STATUS, VERIFICATION_STATUS_LABELS } from '@/constants/verification/verification-status.constants'

/**
 * 验证通知管理组合式函数返回值类型
 */
interface UseVerificationNotificationReturn {
  /** 通知状态变化 */
  notifyStatusChange: (oldStatus: number, newStatus: number, privilegeName: string) => void
  /** 通知验证开始 */
  notifyVerificationStart: (privilegeName: string) => void
  /** 通知验证错误 */
  notifyVerificationError: (message: string) => void
}

/**
 * 验证通知管理组合式函数
 * 提供验证流程的消息通知功能
 */
export function useVerificationNotification(): UseVerificationNotificationReturn {
  const message = useMessage()

  /**
   * 通知状态变化
   * 当验证状态发生变化时显示相应的通知消息
   * @param oldStatus 旧状态
   * @param newStatus 新状态
   * @param privilegeName 特权名称
   */
  const notifyStatusChange = (oldStatus: number, newStatus: number, privilegeName: string): void => {
    // 如果状态没有变化，不显示通知
    if (oldStatus === newStatus) return

    const statusLabel = VERIFICATION_STATUS_LABELS[newStatus] || '未知状态'

    switch (newStatus) {
      case VERIFICATION_STATUS.SUCCESS:
        message.success(`特权"${privilegeName}"验证成功！`)
        break
      case VERIFICATION_STATUS.FAILED:
        message.error(`特权"${privilegeName}"验证失败`)
        break
      case VERIFICATION_STATUS.TIMEOUT:
        message.warning(`特权"${privilegeName}"验证超时`)
        break
      case VERIFICATION_STATUS.IN_PROGRESS:
        // 进行中状态通常不需要特别通知，除非是从其他状态转回来
        if (oldStatus !== VERIFICATION_STATUS.IN_PROGRESS) {
          message.info(`特权"${privilegeName}"验证进行中...`)
        }
        break
      default:
        message.info(`特权"${privilegeName}"状态更新为：${statusLabel}`)
        break
    }
  }

  /**
   * 通知验证开始
   * 当用户发起验证申请时显示成功消息
   * @param privilegeName 特权名称
   */
  const notifyVerificationStart = (privilegeName: string): void => {
    message.success(`特权"${privilegeName}"验证申请已发送，请等待特权提供者确认`)
  }

  /**
   * 通知验证错误
   * 当验证过程中发生错误时显示错误消息
   * @param errorMessage 错误消息
   */
  const notifyVerificationError = (errorMessage: string): void => {
    message.error(errorMessage || '验证过程中发生错误，请稍后重试')
  }

  return {
    notifyStatusChange,
    notifyVerificationStart,
    notifyVerificationError
  }
}
