import{cb as t}from"./highlight.js-O_OqoeFy.js";import{r as e}from"./url-parse-BJt2elfP.js";import{r as n}from"./inherits-BfZYsuMB.js";var r,i,o,s,a,l,u={exports:{}},c={};function h(){if(o)return i;o=1;var t=(r||(r=1,globalThis.crypto&&globalThis.crypto.getRandomValues?c.randomBytes=function(t){var e=new Uint8Array(t);return globalThis.crypto.getRandomValues(e),e}:c.randomBytes=function(t){for(var e=new Array(t),n=0;n<t;n++)e[n]=Math.floor(256*Math.random());return e}),c),e="abcdefghijklmnopqrstuvwxyz012345";return i={string:function(n){for(var r=t.randomBytes(n),i=[],o=0;o<n;o++)i.push(e.substr(r[o]%32,1));return i.join("")},number:function(t){return Math.floor(Math.random()*t)},numberString:function(t){var e=(""+(t-1)).length;return(new Array(e+1).join("0")+this.number(t)).slice(-e)}}}function f(){return s||(s=1,function(t){var e=h(),n={},r=!1,i=globalThis.chrome&&globalThis.chrome.app&&globalThis.chrome.app.runtime;t.exports={attachEvent:function(t,e){void 0!==globalThis.addEventListener?globalThis.addEventListener(t,e,!1):globalThis.document&&globalThis.attachEvent&&(globalThis.document.attachEvent("on"+t,e),globalThis.attachEvent("on"+t,e))},detachEvent:function(t,e){void 0!==globalThis.addEventListener?globalThis.removeEventListener(t,e,!1):globalThis.document&&globalThis.detachEvent&&(globalThis.document.detachEvent("on"+t,e),globalThis.detachEvent("on"+t,e))},unloadAdd:function(t){if(i)return null;var o=e.string(8);return n[o]=t,r&&setTimeout(this.triggerUnloadCallbacks,0),o},unloadDel:function(t){t in n&&delete n[t]},triggerUnloadCallbacks:function(){for(var t in n)n[t](),delete n[t]}};i||t.exports.attachEvent("unload",(function(){r||(r=!0,t.exports.triggerUnloadCallbacks())}))}(u)),u.exports}function d(){if(l)return a;l=1;var t=e();return a={getOrigin:function(e){if(!e)return null;var n=new t(e);if("file:"===n.protocol)return null;var r=n.port;return r||(r="https:"===n.protocol?"443":"80"),n.protocol+"//"+n.hostname+":"+r},isOriginEqual:function(t,e){return this.getOrigin(t)===this.getOrigin(e)},isSchemeEqual:function(t,e){return t.split(":")[0]===e.split(":")[0]},addPath:function(t,e){var n=t.split("?");return n[0]+e+(n[1]?"?"+n[1]:"")},addQuery:function(t,e){return t+(-1===t.indexOf("?")?"?"+e:"&"+e)},isLoopbackAddr:function(t){return/^127\.([0-9]{1,3})\.([0-9]{1,3})\.([0-9]{1,3})$/i.test(t)||/^\[::1\]$/.test(t)}}}var p,m,b,v={};function g(){if(m)return p;function t(){this._listeners={}}return m=1,t.prototype.addEventListener=function(t,e){t in this._listeners||(this._listeners[t]=[]);var n=this._listeners[t];-1===n.indexOf(e)&&(n=n.concat([e])),this._listeners[t]=n},t.prototype.removeEventListener=function(t,e){var n=this._listeners[t];if(n){var r=n.indexOf(e);-1===r||(n.length>1?this._listeners[t]=n.slice(0,r).concat(n.slice(r+1)):delete this._listeners[t])}},t.prototype.dispatchEvent=function(){var t=arguments[0],e=t.type,n=1===arguments.length?[t]:Array.apply(null,arguments);if(this["on"+e]&&this["on"+e].apply(this,n),e in this._listeners)for(var r=this._listeners[e],i=0;i<r.length;i++)r[i].apply(this,n)},p=t}function y(){if(b)return v;b=1;var t=n(),e=g();function r(){e.call(this)}return t(r,e),r.prototype.removeAllListeners=function(t){t?delete this._listeners[t]:this._listeners={}},r.prototype.once=function(t,e){var n=this,r=!1;this.on(t,(function i(){n.removeListener(t,i),r||(r=!0,e.apply(this,arguments))}))},r.prototype.emit=function(){var t=arguments[0],e=this._listeners[t];if(e){for(var n=arguments.length,r=new Array(n-1),i=1;i<n;i++)r[i-1]=arguments[i];for(var o=0;o<e.length;o++)e[o].apply(this,r)}},r.prototype.on=r.prototype.addListener=e.prototype.addEventListener,r.prototype.removeListener=e.prototype.removeEventListener,v.EventEmitter=r,v}var w,T,_,E,x,S,O,N,C,A,I,j,k,L,P,R,M,W,U,D,J,B,q,G,H,F,X,Q,$,V,z,K,Y,Z,tt,et={exports:{}};function nt(){if(_)return T;_=1;var t=f(),e=d(),r=n(),i=y().EventEmitter,o=function(){if(w)return et.exports;w=1;var t=globalThis.WebSocket||globalThis.MozWebSocket;return et.exports=t?function(e){return new t(e)}:void 0,et.exports}();function s(n,r,a){if(!s.enabled())throw new Error("Transport created when disabled");i.call(this);var l=this,u=e.addPath(n,"/websocket");u="https"===u.slice(0,5)?"wss"+u.slice(5):"ws"+u.slice(4),this.url=u,this.ws=new o(this.url,[],a),this.ws.onmessage=function(t){t.data,l.emit("message",t.data)},this.unloadRef=t.unloadAdd((function(){l.ws.close()})),this.ws.onclose=function(t){t.code,t.reason,l.emit("close",t.code,t.reason),l._cleanup()},this.ws.onerror=function(t){l.emit("close",1006,"WebSocket connection broken"),l._cleanup()}}return r(s,i),s.prototype.send=function(t){var e="["+t+"]";this.ws.send(e)},s.prototype.close=function(){var t=this.ws;this._cleanup(),t&&t.close()},s.prototype._cleanup=function(){var e=this.ws;e&&(e.onmessage=e.onclose=e.onerror=null),t.unloadDel(this.unloadRef),this.unloadRef=this.ws=null,this.removeAllListeners()},s.enabled=function(){return!!o},s.transportName="websocket",s.roundTrips=2,T=s}function rt(){if(C)return N;C=1;var t=n(),e=d(),r=function(){if(x)return E;x=1;var t=n(),e=y().EventEmitter;function r(t,n){e.call(this),this.sendBuffer=[],this.sender=n,this.url=t}return t(r,e),r.prototype.send=function(t){this.sendBuffer.push(t),this.sendStop||this.sendSchedule()},r.prototype.sendScheduleWait=function(){var t,e=this;this.sendStop=function(){e.sendStop=null,clearTimeout(t)},t=setTimeout((function(){e.sendStop=null,e.sendSchedule()}),25)},r.prototype.sendSchedule=function(){this.sendBuffer.length;var t=this;if(this.sendBuffer.length>0){var e="["+this.sendBuffer.join(",")+"]";this.sendStop=this.sender(this.url,e,(function(e){t.sendStop=null,e?(t.emit("close",e.code||1006,"Sending error: "+e),t.close()):t.sendScheduleWait()})),this.sendBuffer=[]}},r.prototype._cleanup=function(){this.removeAllListeners()},r.prototype.close=function(){this._cleanup(),this.sendStop&&(this.sendStop(),this.sendStop=null)},E=r}(),i=function(){if(O)return S;O=1;var t=n(),e=y().EventEmitter;function r(t,n,r){e.call(this),this.Receiver=t,this.receiveUrl=n,this.AjaxObject=r,this._scheduleReceiver()}return t(r,e),r.prototype._scheduleReceiver=function(){var t=this,e=this.poll=new this.Receiver(this.receiveUrl,this.AjaxObject);e.on("message",(function(e){t.emit("message",e)})),e.once("close",(function(n,r){t.pollIsClosing,t.poll=e=null,t.pollIsClosing||("network"===r?t._scheduleReceiver():(t.emit("close",n||1006,r),t.removeAllListeners()))}))},r.prototype.abort=function(){this.removeAllListeners(),this.pollIsClosing=!0,this.poll&&this.poll.abort()},S=r}();function o(t,n,o,s,a){var l=e.addPath(t,n),u=this;r.call(this,t,o),this.poll=new i(s,l,a),this.poll.on("message",(function(t){u.emit("message",t)})),this.poll.once("close",(function(t,e){u.poll=null,u.emit("close",t,e),u.close()}))}return t(o,r),o.prototype.close=function(){r.prototype.close.call(this),this.removeAllListeners(),this.poll&&(this.poll.abort(),this.poll=null)},N=o}function it(){if(I)return A;I=1;var t=n(),e=d(),r=rt();function i(t,n,i,o){r.call(this,t,n,function(t){return function(n,r,i){var o={};"string"==typeof r&&(o.headers={"Content-type":"text/plain"});var s=e.addPath(n,"/xhr_send"),a=new t("POST",s,r,o);return a.once("finish",(function(t){if(a=null,200!==t&&204!==t)return i(new Error("http status "+t));i()})),function(){a.close(),a=null;var t=new Error("Aborted");t.code=1e3,i(t)}}}(o),i,o)}return t(i,r),A=i}function ot(){if(k)return j;k=1;var t=n(),e=y().EventEmitter;function r(t,n){e.call(this);var r=this;this.bufferPosition=0,this.xo=new n("POST",t,null),this.xo.on("chunk",this._chunkHandler.bind(this)),this.xo.once("finish",(function(t,e){r._chunkHandler(t,e),r.xo=null;var n=200===t?"network":"permanent";r.emit("close",null,n),r._cleanup()}))}return t(r,e),r.prototype._chunkHandler=function(t,e){if(200===t&&e)for(var n=-1;;this.bufferPosition+=n+1){var r=e.slice(this.bufferPosition);if(-1===(n=r.indexOf("\n")))break;var i=r.slice(0,n);i&&this.emit("message",i)}},r.prototype._cleanup=function(){this.removeAllListeners()},r.prototype.abort=function(){this.xo&&(this.xo.close(),this.emit("close",null,"user"),this.xo=null),this._cleanup()},j=r}function st(){if(P)return L;P=1;var t=y().EventEmitter,e=n(),r=f(),i=d(),o=globalThis.XMLHttpRequest;function s(e,n,r,i){var o=this;t.call(this),setTimeout((function(){o._start(e,n,r,i)}),0)}e(s,t),s.prototype._start=function(t,e,n,a){var l=this;try{this.xhr=new o}catch(c){}if(!this.xhr)return this.emit("finish",0,"no xhr support"),void this._cleanup();e=i.addQuery(e,"t="+ +new Date),this.unloadRef=r.unloadAdd((function(){l._cleanup(!0)}));try{this.xhr.open(t,e,!0),this.timeout&&"timeout"in this.xhr&&(this.xhr.timeout=this.timeout,this.xhr.ontimeout=function(){l.emit("finish",0,""),l._cleanup(!1)})}catch(h){return this.emit("finish",0,""),void this._cleanup(!1)}if(a&&a.noCredentials||!s.supportsCORS||(this.xhr.withCredentials=!0),a&&a.headers)for(var u in a.headers)this.xhr.setRequestHeader(u,a.headers[u]);this.xhr.onreadystatechange=function(){if(l.xhr){var t,e,n=l.xhr;switch(n.readyState,n.readyState){case 3:try{e=n.status,t=n.responseText}catch(h){}1223===e&&(e=204),200===e&&t&&t.length>0&&l.emit("chunk",e,t);break;case 4:1223===(e=n.status)&&(e=204),12005!==e&&12029!==e||(e=0),n.responseText,l.emit("finish",e,n.responseText),l._cleanup(!1)}}};try{l.xhr.send(n)}catch(h){l.emit("finish",0,""),l._cleanup(!1)}},s.prototype._cleanup=function(t){if(this.xhr){if(this.removeAllListeners(),r.unloadDel(this.unloadRef),this.xhr.onreadystatechange=function(){},this.xhr.ontimeout&&(this.xhr.ontimeout=null),t)try{this.xhr.abort()}catch(e){}this.unloadRef=this.xhr=null}},s.prototype.close=function(){this._cleanup(!0)},s.enabled=!!o;var a=["Active"].concat("Object").join("X");!s.enabled&&a in globalThis&&(o=function(){try{return new globalThis[a]("Microsoft.XMLHTTP")}catch(t){return null}},s.enabled=!!new o);var l=!1;try{l="withCredentials"in new o}catch(u){}return s.supportsCORS=l,L=s}function at(){if(M)return R;M=1;var t=n(),e=st();function r(t,n,r,i){e.call(this,t,n,r,i)}return t(r,e),r.enabled=e.enabled&&e.supportsCORS,R=r}function lt(){if(U)return W;U=1;var t=n(),e=st();function r(t,n,r){e.call(this,t,n,r,{noCredentials:!0})}return t(r,e),r.enabled=e.enabled,W=r}function ut(){return J?D:(J=1,D={isOpera:function(){return globalThis.navigator&&/opera/i.test(globalThis.navigator.userAgent)},isKonqueror:function(){return globalThis.navigator&&/konqueror/i.test(globalThis.navigator.userAgent)},hasDomain:function(){if(!globalThis.document)return!0;try{return!!globalThis.document.domain}catch(t){return!1}}})}function ct(){if(q)return B;q=1;var t=n(),e=it(),r=ot(),i=at(),o=lt(),s=ut();function a(t){if(!o.enabled&&!i.enabled)throw new Error("Transport created when disabled");e.call(this,t,"/xhr_streaming",r,i)}return t(a,e),a.enabled=function(t){return!t.nullOrigin&&(!s.isOpera()&&i.enabled)},a.transportName="xhr-streaming",a.roundTrips=2,a.needBody=!!globalThis.document,B=a}function ht(){if(H)return G;H=1;var t=y().EventEmitter,e=n(),r=f(),i=ut(),o=d();function s(e,n,r){var i=this;t.call(this),setTimeout((function(){i._start(e,n,r)}),0)}return e(s,t),s.prototype._start=function(t,e,n){var i=this,s=new globalThis.XDomainRequest;e=o.addQuery(e,"t="+ +new Date),s.onerror=function(){i._error()},s.ontimeout=function(){i._error()},s.onprogress=function(){s.responseText,i.emit("chunk",200,s.responseText)},s.onload=function(){i.emit("finish",200,s.responseText),i._cleanup(!1)},this.xdr=s,this.unloadRef=r.unloadAdd((function(){i._cleanup(!0)}));try{this.xdr.open(t,e),this.timeout&&(this.xdr.timeout=this.timeout),this.xdr.send(n)}catch(a){this._error()}},s.prototype._error=function(){this.emit("finish",0,""),this._cleanup(!1)},s.prototype._cleanup=function(t){if(this.xdr){if(this.removeAllListeners(),r.unloadDel(this.unloadRef),this.xdr.ontimeout=this.xdr.onerror=this.xdr.onprogress=this.xdr.onload=null,t)try{this.xdr.abort()}catch(e){}this.unloadRef=this.xdr=null}},s.prototype.close=function(){this._cleanup(!0)},s.enabled=!(!globalThis.XDomainRequest||!i.hasDomain()),G=s}function ft(){if(X)return F;X=1;var t=n(),e=it(),r=ot(),i=ht();function o(t){if(!i.enabled)throw new Error("Transport created when disabled");e.call(this,t,"/xhr_streaming",r,i)}return t(o,e),o.enabled=function(t){return!t.cookie_needed&&!t.nullOrigin&&(i.enabled&&t.sameScheme)},o.transportName="xdr-streaming",o.roundTrips=2,F=o}function dt(){return $?Q:($=1,Q=globalThis.EventSource)}function pt(){if(Y)return K;Y=1;var t=n(),e=it(),r=function(){if(z)return V;z=1;var t=n(),e=y().EventEmitter,r=dt();function i(t){e.call(this);var n=this,i=this.es=new r(t);i.onmessage=function(t){t.data,n.emit("message",decodeURI(t.data))},i.onerror=function(t){i.readyState;var e=2!==i.readyState?"network":"permanent";n._cleanup(),n._close(e)}}return t(i,e),i.prototype.abort=function(){this._cleanup(),this._close("user")},i.prototype._cleanup=function(){var t=this.es;t&&(t.onmessage=t.onerror=null,t.close(),this.es=null)},i.prototype._close=function(t){var e=this;setTimeout((function(){e.emit("close",null,t),e.removeAllListeners()}),200)},V=i}(),i=at(),o=dt();function s(t){if(!s.enabled())throw new Error("Transport created when disabled");e.call(this,t,"/eventsource",r,i)}return t(s,e),s.enabled=function(){return!!o},s.transportName="eventsource",s.roundTrips=2,K=s}function mt(){return tt?Z:(tt=1,Z="1.6.1")}var bt,vt,gt,yt,wt,Tt,_t,Et,xt,St,Ot,Nt,Ct,At,It,jt,kt,Lt,Pt,Rt,Mt,Wt,Ut,Dt={exports:{}};function Jt(){return bt||(bt=1,t=Dt,e=f(),n=ut(),t.exports={WPrefix:"_jp",currentWindowId:null,polluteGlobalNamespace:function(){t.exports.WPrefix in globalThis||(globalThis[t.exports.WPrefix]={})},postMessage:function(e,n){globalThis.parent!==globalThis&&globalThis.parent.postMessage(JSON.stringify({windowId:t.exports.currentWindowId,type:e,data:n||""}),"*")},createIframe:function(t,n){var r,i,o=globalThis.document.createElement("iframe"),s=function(){clearTimeout(r);try{o.onload=null}catch(t){}o.onerror=null},a=function(){o&&(s(),setTimeout((function(){o&&o.parentNode.removeChild(o),o=null}),0),e.unloadDel(i))},l=function(t){o&&(a(),n(t))};return o.src=t,o.style.display="none",o.style.position="absolute",o.onerror=function(){l("onerror")},o.onload=function(){clearTimeout(r),r=setTimeout((function(){l("onload timeout")}),2e3)},globalThis.document.body.appendChild(o),r=setTimeout((function(){l("timeout")}),15e3),i=e.unloadAdd(a),{post:function(t,e){setTimeout((function(){try{o&&o.contentWindow&&o.contentWindow.postMessage(t,e)}catch(n){}}),0)},cleanup:a,loaded:s}},createHtmlfile:function(n,r){var i,o,s,a=["Active"].concat("Object").join("X"),l=new globalThis[a]("htmlfile"),u=function(){clearTimeout(i),s.onerror=null},c=function(){l&&(u(),e.unloadDel(o),s.parentNode.removeChild(s),s=l=null,CollectGarbage())},h=function(t){l&&(c(),r(t))};l.open(),l.write('<html><script>document.domain="'+globalThis.document.domain+'";<\/script></html>'),l.close(),l.parentWindow[t.exports.WPrefix]=globalThis[t.exports.WPrefix];var f=l.createElement("div");return l.body.appendChild(f),s=l.createElement("iframe"),f.appendChild(s),s.src=n,s.onerror=function(){h("onerror")},i=setTimeout((function(){h("timeout")}),15e3),o=e.unloadAdd(c),{post:function(t,e){try{setTimeout((function(){s&&s.contentWindow&&s.contentWindow.postMessage(t,e)}),0)}catch(n){}},cleanup:c,loaded:u}}},t.exports.iframeEnabled=!1,globalThis.document&&(t.exports.iframeEnabled=("function"==typeof globalThis.postMessage||"object"==typeof globalThis.postMessage)&&!n.isKonqueror())),Dt.exports;var t,e,n}function Bt(){if(gt)return vt;gt=1;var t=n(),e=y().EventEmitter,r=mt(),i=d(),o=Jt(),s=f(),a=h();function l(t,n,r){if(!l.enabled())throw new Error("Transport created when disabled");e.call(this);var u=this;this.origin=i.getOrigin(r),this.baseUrl=r,this.transUrl=n,this.transport=t,this.windowId=a.string(8);var c=i.addPath(r,"/iframe.html")+"#"+this.windowId;this.iframeObj=o.createIframe(c,(function(t){u.emit("close",1006,"Unable to load an iframe ("+t+")"),u.close()})),this.onmessageCallback=this._message.bind(this),s.attachEvent("message",this.onmessageCallback)}return t(l,e),l.prototype.close=function(){if(this.removeAllListeners(),this.iframeObj){s.detachEvent("message",this.onmessageCallback);try{this.postMessage("c")}catch(t){}this.iframeObj.cleanup(),this.iframeObj=null,this.onmessageCallback=this.iframeObj=null}},l.prototype._message=function(t){if(t.data,!i.isOriginEqual(t.origin,this.origin))return t.origin,void this.origin;var e;try{e=JSON.parse(t.data)}catch(o){return void t.data}if(e.windowId!==this.windowId)return e.windowId,void this.windowId;switch(e.type){case"s":this.iframeObj.loaded(),this.postMessage("s",JSON.stringify([r,this.transport,this.transUrl,this.baseUrl]));break;case"t":this.emit("message",e.data);break;case"c":var n;try{n=JSON.parse(e.data)}catch(o){return void e.data}this.emit("close",n[0],n[1]),this.close()}},l.prototype.postMessage=function(t,e){this.iframeObj.post(JSON.stringify({windowId:this.windowId,type:t,data:e||""}),this.origin)},l.prototype.send=function(t){this.postMessage("m",t)},l.enabled=function(){return o.iframeEnabled},l.transportName="iframe",l.roundTrips=2,vt=l}function qt(){return wt||(wt=1,yt={isObject:function(t){var e=typeof t;return"function"===e||"object"===e&&!!t},extend:function(t){if(!this.isObject(t))return t;for(var e,n,r=1,i=arguments.length;r<i;r++)for(n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t}}),yt}function Gt(){if(_t)return Tt;_t=1;var t=n(),e=Bt(),r=qt();return Tt=function(n){function i(t,r){e.call(this,n.transportName,t,r)}return t(i,e),i.enabled=function(t,i){if(!globalThis.document)return!1;var o=r.extend({},i);return o.sameOrigin=!0,n.enabled(o)&&e.enabled()},i.transportName="iframe-"+n.transportName,i.needBody=!0,i.roundTrips=e.roundTrips+n.roundTrips-1,i.facadeTransport=n,i}}function Ht(){if(Ot)return St;Ot=1;var t=n(),e=function(){if(xt)return Et;xt=1;var t=n(),e=Jt(),r=d(),i=y().EventEmitter,o=h();function s(t){i.call(this);var n=this;e.polluteGlobalNamespace(),this.id="a"+o.string(6),t=r.addQuery(t,"c="+decodeURIComponent(e.WPrefix+"."+this.id)),s.htmlfileEnabled;var a=s.htmlfileEnabled?e.createHtmlfile:e.createIframe;globalThis[e.WPrefix][this.id]={start:function(){n.iframeObj.loaded()},message:function(t){n.emit("message",t)},stop:function(){n._cleanup(),n._close("network")}},this.iframeObj=a(t,(function(){n._cleanup(),n._close("permanent")}))}t(s,i),s.prototype.abort=function(){this._cleanup(),this._close("user")},s.prototype._cleanup=function(){this.iframeObj&&(this.iframeObj.cleanup(),this.iframeObj=null),delete globalThis[e.WPrefix][this.id]},s.prototype._close=function(t){this.emit("close",null,t),this.removeAllListeners()},s.htmlfileEnabled=!1;var a=["Active"].concat("Object").join("X");if(a in globalThis)try{s.htmlfileEnabled=!!new globalThis[a]("htmlfile")}catch(l){}return s.enabled=s.htmlfileEnabled||e.iframeEnabled,Et=s}(),r=lt(),i=it();function o(t){if(!e.enabled)throw new Error("Transport created when disabled");i.call(this,t,"/htmlfile",e,r)}return t(o,i),o.enabled=function(t){return e.enabled&&t.sameOrigin},o.transportName="htmlfile",o.roundTrips=2,St=o}function Ft(){if(Ct)return Nt;Ct=1;var t=n(),e=it(),r=ot(),i=at(),o=lt();function s(t){if(!o.enabled&&!i.enabled)throw new Error("Transport created when disabled");e.call(this,t,"/xhr",r,i)}return t(s,e),s.enabled=function(t){return!t.nullOrigin&&(!(!o.enabled||!t.sameOrigin)||i.enabled)},s.transportName="xhr-polling",s.roundTrips=2,Nt=s}function Xt(){if(It)return At;It=1;var t=n(),e=it(),r=ft(),i=ot(),o=ht();function s(t){if(!o.enabled)throw new Error("Transport created when disabled");e.call(this,t,"/xhr",i,o)}return t(s,e),s.enabled=r.enabled,s.transportName="xdr-polling",s.roundTrips=2,At=s}function Qt(){if(Pt)return Lt;Pt=1;var t,e,n=h(),r=d();return Lt=function(i,o,s){t||((t=globalThis.document.createElement("form")).style.display="none",t.style.position="absolute",t.method="POST",t.enctype="application/x-www-form-urlencoded",t.acceptCharset="UTF-8",(e=globalThis.document.createElement("textarea")).name="d",t.appendChild(e),globalThis.document.body.appendChild(t));var a="a"+n.string(8);t.target=a,t.action=r.addQuery(r.addPath(i,"/jsonp_send"),"i="+a);var l=function(t){try{return globalThis.document.createElement('<iframe name="'+t+'">')}catch(n){var e=globalThis.document.createElement("iframe");return e.name=t,e}}(a);l.id=a,l.style.display="none",t.appendChild(l);try{e.value=o}catch(c){}t.submit();var u=function(t){l.onerror&&(l.onreadystatechange=l.onerror=l.onload=null,setTimeout((function(){l.parentNode.removeChild(l),l=null}),500),e.value="",s(t))};return l.onerror=function(){u()},l.onload=function(){u()},l.onreadystatechange=function(t){l.readyState,"complete"===l.readyState&&u()},function(){u(new Error("Aborted"))}}}function $t(){if(Mt)return Rt;Mt=1;var t=n(),e=rt(),r=function(){if(kt)return jt;kt=1;var t=Jt(),e=h(),r=ut(),i=d(),o=n(),s=y().EventEmitter;function a(n){var r=this;s.call(this),t.polluteGlobalNamespace(),this.id="a"+e.string(6);var o=i.addQuery(n,"c="+encodeURIComponent(t.WPrefix+"."+this.id));globalThis[t.WPrefix][this.id]=this._callback.bind(this),this._createScript(o),this.timeoutId=setTimeout((function(){r._abort(new Error("JSONP script loaded abnormally (timeout)"))}),a.timeout)}return o(a,s),a.prototype.abort=function(){if(globalThis[t.WPrefix][this.id]){var e=new Error("JSONP user aborted read");e.code=1e3,this._abort(e)}},a.timeout=35e3,a.scriptErrorTimeout=1e3,a.prototype._callback=function(t){this._cleanup(),this.aborting||(t&&this.emit("message",t),this.emit("close",null,"network"),this.removeAllListeners())},a.prototype._abort=function(t){this._cleanup(),this.aborting=!0,this.emit("close",t.code,t.message),this.removeAllListeners()},a.prototype._cleanup=function(){if(clearTimeout(this.timeoutId),this.script2&&(this.script2.parentNode.removeChild(this.script2),this.script2=null),this.script){var e=this.script;e.parentNode.removeChild(e),e.onreadystatechange=e.onerror=e.onload=e.onclick=null,this.script=null}delete globalThis[t.WPrefix][this.id]},a.prototype._scriptError=function(){var t=this;this.errorTimer||(this.errorTimer=setTimeout((function(){t.loadedOkay||t._abort(new Error("JSONP script loaded abnormally (onerror)"))}),a.scriptErrorTimeout))},a.prototype._createScript=function(t){var n,i=this,o=this.script=globalThis.document.createElement("script");if(o.id="a"+e.string(8),o.src=t,o.type="text/javascript",o.charset="UTF-8",o.onerror=this._scriptError.bind(this),o.onload=function(){i._abort(new Error("JSONP script loaded abnormally (onload)"))},o.onreadystatechange=function(){if(o.readyState,/loaded|closed/.test(o.readyState)){if(o&&o.htmlFor&&o.onclick){i.loadedOkay=!0;try{o.onclick()}catch(t){}}o&&i._abort(new Error("JSONP script loaded abnormally (onreadystatechange)"))}},void 0===o.async&&globalThis.document.attachEvent)if(r.isOpera())(n=this.script2=globalThis.document.createElement("script")).text="try{var a = document.getElementById('"+o.id+"'); if(a)a.onerror();}catch(x){};",o.async=n.async=!1;else{try{o.htmlFor=o.id,o.event="onclick"}catch(a){}o.async=!0}void 0!==o.async&&(o.async=!0);var s=globalThis.document.getElementsByTagName("head")[0];s.insertBefore(o,s.firstChild),n&&s.insertBefore(n,s.firstChild)},jt=a}(),i=Qt();function o(t){if(!o.enabled())throw new Error("Transport created when disabled");e.call(this,t,"/jsonp",i,r)}return t(o,e),o.enabled=function(){return!!globalThis.document},o.transportName="jsonp-polling",o.roundTrips=1,o.needBody=!0,Rt=o}var Vt,zt,Kt,Yt,Zt,te,ee,ne,re,ie,oe,se,ae,le,ue,ce,he,fe,de,pe,me,be,ve,ge,ye,we,Te,_e,Ee,xe,Se,Oe,Ne,Ce={};function Ae(){if(Vt)return Ce;Vt=1;var t,e=Array.prototype,n=Object.prototype,r=Function.prototype,i=String.prototype,o=e.slice,s=n.toString,a=function(t){return"[object Function]"===n.toString.call(t)},l=function(t){return"[object String]"===s.call(t)},u=Object.defineProperty&&function(){try{return Object.defineProperty({},"x",{}),!0}catch(t){return!1}}();t=u?function(t,e,n,r){!r&&e in t||Object.defineProperty(t,e,{configurable:!0,enumerable:!1,writable:!0,value:n})}:function(t,e,n,r){!r&&e in t||(t[e]=n)};var c=function(e,r,i){for(var o in r)n.hasOwnProperty.call(r,o)&&t(e,o,r[o],i)},h=function(t){if(null==t)throw new TypeError("can't convert "+t+" to object");return Object(t)};function f(){}c(r,{bind:function(t){var e=this;if(!a(e))throw new TypeError("Function.prototype.bind called on incompatible "+e);for(var n=o.call(arguments,1),r=Math.max(0,e.length-n.length),i=[],s=0;s<r;s++)i.push("$"+s);var l=Function("binder","return function ("+i.join(",")+"){ return binder.apply(this, arguments); }")((function(){if(this instanceof l){var r=e.apply(this,n.concat(o.call(arguments)));return Object(r)===r?r:this}return e.apply(t,n.concat(o.call(arguments)))}));return e.prototype&&(f.prototype=e.prototype,l.prototype=new f,f.prototype=null),l}}),c(Array,{isArray:function(t){return"[object Array]"===s.call(t)}});var d,p,m,b=Object("a"),v="a"!==b[0]||!(0 in b);c(e,{forEach:function(t){var e=h(this),n=v&&l(this)?this.split(""):e,r=arguments[1],i=-1,o=n.length>>>0;if(!a(t))throw new TypeError;for(;++i<o;)i in n&&t.call(r,n[i],i,e)}},(d=e.forEach,p=!0,m=!0,d&&(d.call("foo",(function(t,e,n){"object"!=typeof n&&(p=!1)})),d.call([1],(function(){m="string"==typeof this}),"x")),!(d&&p&&m)));var g=Array.prototype.indexOf&&-1!==[0,1].indexOf(1,2);c(e,{indexOf:function(t){var e=v&&l(this)?this.split(""):h(this),n=e.length>>>0;if(!n)return-1;var r,i=0;for(arguments.length>1&&((r=+arguments[1])!=r?r=0:0!==r&&r!==1/0&&r!==-1/0&&(r=(r>0||-1)*Math.floor(Math.abs(r))),i=r),i=i>=0?i:Math.max(0,n+i);i<n;i++)if(i in e&&e[i]===t)return i;return-1}},g);var y,w=i.split;2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||".".split(/()()/).length>1?(y=void 0===/()??/.exec("")[1],i.split=function(t,n){var r=this;if(void 0===t&&0===n)return[];if("[object RegExp]"!==s.call(t))return w.call(this,t,n);var i,o,a,l,u=[],c=(t.ignoreCase?"i":"")+(t.multiline?"m":"")+(t.extended?"x":"")+(t.sticky?"y":""),h=0;for(t=new RegExp(t.source,c+"g"),r+="",y||(i=new RegExp("^"+t.source+"$(?!\\s)",c)),n=void 0===n?-1>>>0:n>>>0;(o=t.exec(r))&&!((a=o.index+o[0].length)>h&&(u.push(r.slice(h,o.index)),!y&&o.length>1&&o[0].replace(i,(function(){for(var t=1;t<arguments.length-2;t++)void 0===arguments[t]&&(o[t]=void 0)})),o.length>1&&o.index<r.length&&e.push.apply(u,o.slice(1)),l=o[0].length,h=a,u.length>=n));)t.lastIndex===o.index&&t.lastIndex++;return h===r.length?!l&&t.test("")||u.push(""):u.push(r.slice(h)),u.length>n?u.slice(0,n):u}):"0".split(void 0,0).length&&(i.split=function(t,e){return void 0===t&&0===e?[]:w.call(this,t,e)});var T=i.substr,_="".substr&&"b"!=="0b".substr(-1);return c(i,{substr:function(t,e){return T.call(this,t<0&&(t=this.length+t)<0?0:t,e)}},_),Ce}function Ie(){if(Kt)return zt;Kt=1;var t,e=/[\x00-\x1f\ud800-\udfff\ufffe\uffff\u0300-\u0333\u033d-\u0346\u034a-\u034c\u0350-\u0352\u0357-\u0358\u035c-\u0362\u0374\u037e\u0387\u0591-\u05af\u05c4\u0610-\u0617\u0653-\u0654\u0657-\u065b\u065d-\u065e\u06df-\u06e2\u06eb-\u06ec\u0730\u0732-\u0733\u0735-\u0736\u073a\u073d\u073f-\u0741\u0743\u0745\u0747\u07eb-\u07f1\u0951\u0958-\u095f\u09dc-\u09dd\u09df\u0a33\u0a36\u0a59-\u0a5b\u0a5e\u0b5c-\u0b5d\u0e38-\u0e39\u0f43\u0f4d\u0f52\u0f57\u0f5c\u0f69\u0f72-\u0f76\u0f78\u0f80-\u0f83\u0f93\u0f9d\u0fa2\u0fa7\u0fac\u0fb9\u1939-\u193a\u1a17\u1b6b\u1cda-\u1cdb\u1dc0-\u1dcf\u1dfc\u1dfe\u1f71\u1f73\u1f75\u1f77\u1f79\u1f7b\u1f7d\u1fbb\u1fbe\u1fc9\u1fcb\u1fd3\u1fdb\u1fe3\u1feb\u1fee-\u1fef\u1ff9\u1ffb\u1ffd\u2000-\u2001\u20d0-\u20d1\u20d4-\u20d7\u20e7-\u20e9\u2126\u212a-\u212b\u2329-\u232a\u2adc\u302b-\u302c\uaab2-\uaab3\uf900-\ufa0d\ufa10\ufa12\ufa15-\ufa1e\ufa20\ufa22\ufa25-\ufa26\ufa2a-\ufa2d\ufa30-\ufa6d\ufa70-\ufad9\ufb1d\ufb1f\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufb4e\ufff0-\uffff]/g;return zt={quote:function(n){var r=JSON.stringify(n);return e.lastIndex=0,e.test(r)?(t||(t=function(t){var e,n={},r=[];for(e=0;e<65536;e++)r.push(String.fromCharCode(e));return t.lastIndex=0,r.join("").replace(t,(function(t){return n[t]="\\u"+("0000"+t.charCodeAt(0).toString(16)).slice(-4),""})),t.lastIndex=0,n}(e)),r.replace(e,(function(e){return t[e]}))):r}}}function je(){if(re)return ne;function t(t){this.type=t}return re=1,t.prototype.initEvent=function(t,e,n){return this.type=t,this.bubbles=e,this.cancelable=n,this.timeStamp=+new Date,this},t.prototype.stopPropagation=function(){},t.prototype.preventDefault=function(){},t.CAPTURING_PHASE=1,t.AT_TARGET=2,t.BUBBLING_PHASE=3,ne=t}function ke(){return oe?ie:(oe=1,ie=globalThis.location||{origin:"http://localhost:80",protocol:"http:",host:"localhost",port:80,href:"http://localhost/",hash:""})}function Le(){if(de)return fe;de=1;var t=y().EventEmitter,e=n(),r=qt();function i(e,n){t.call(this);var i=this,o=+new Date;this.xo=new n("GET",e),this.xo.once("finish",(function(t,e){var n,s;if(200===t){if(s=+new Date-o,e)try{n=JSON.parse(e)}catch(a){}r.isObject(n)||(n={})}i.emit("finish",n,s),i.removeAllListeners()}))}return e(i,t),i.prototype.close=function(){this.removeAllListeners(),this.xo.close()},fe=i}function Pe(){if(me)return pe;me=1;var t=n(),e=y().EventEmitter,r=lt(),i=Le();function o(t){var n=this;e.call(this),this.ir=new i(t,r),this.ir.once("finish",(function(t,e){n.ir=null,n.emit("message",JSON.stringify([t,e]))}))}return t(o,e),o.transportName="iframe-info-receiver",o.prototype.close=function(){this.ir&&(this.ir.close(),this.ir=null),this.removeAllListeners()},pe=o}function Re(){if(ye)return ge;ye=1;var t=y().EventEmitter,e=n(),r=d(),i=ht(),o=at(),s=lt(),a=function(){if(he)return ce;he=1;var t=y().EventEmitter;function e(){var n=this;t.call(this),this.to=setTimeout((function(){n.emit("finish",200,"{}")}),e.timeout)}return n()(e,t),e.prototype.close=function(){clearTimeout(this.to)},e.timeout=2e3,ce=e}(),l=function(){if(ve)return be;ve=1;var t=y().EventEmitter,e=n(),r=f(),i=Bt(),o=Pe();function s(e,n){var s=this;t.call(this);var a=function(){var t=s.ifr=new i(o.transportName,n,e);t.once("message",(function(t){if(t){var e;try{e=JSON.parse(t)}catch(i){return s.emit("finish"),void s.close()}var n=e[0],r=e[1];s.emit("finish",n,r)}s.close()})),t.once("close",(function(){s.emit("finish"),s.close()}))};globalThis.document.body?a():r.attachEvent("load",a)}return e(s,t),s.enabled=function(){return i.enabled()},s.prototype.close=function(){this.ifr&&this.ifr.close(),this.removeAllListeners(),this.ifr=null},be=s}(),u=Le();function c(e,n){var r=this;t.call(this),setTimeout((function(){r.doXhr(e,n)}),0)}return e(c,t),c._getReceiver=function(t,e,n){return n.sameOrigin?new u(e,s):o.enabled?new u(e,o):i.enabled&&n.sameScheme?new u(e,i):l.enabled()?new l(t,e):new u(e,a)},c.prototype.doXhr=function(t,e){var n=this,i=r.addPath(t,"/info");this.xo=c._getReceiver(t,i,e),this.timeoutRef=setTimeout((function(){n._cleanup(!1),n.emit("finish")}),c.timeout),this.xo.once("finish",(function(t,e){n._cleanup(!0),n.emit("finish",t,e)}))},c.prototype._cleanup=function(t){clearTimeout(this.timeoutRef),this.timeoutRef=null,!t&&this.xo&&this.xo.close(),this.xo=null},c.prototype.close=function(){this.removeAllListeners(),this._cleanup(!1)},c.timeout=8e3,ge=c}function Me(){if(Ee)return _e;Ee=1;var t=d(),e=f(),n=function(){if(Te)return we;Te=1;var t=Jt();function e(t){this._transport=t,t.on("message",this._transportMessage.bind(this)),t.on("close",this._transportClose.bind(this))}return e.prototype._transportClose=function(e,n){t.postMessage("c",JSON.stringify([e,n]))},e.prototype._transportMessage=function(e){t.postMessage("t",e)},e.prototype._send=function(t){this._transport.send(t)},e.prototype._close=function(){this._transport.close(),this._transport.removeAllListeners()},we=e}(),r=Pe(),i=Jt(),o=ke();return _e=function(s,a){var l,u={};a.forEach((function(t){t.facadeTransport&&(u[t.facadeTransport.transportName]=t.facadeTransport)})),u[r.transportName]=r,s.bootstrap_iframe=function(){var r;i.currentWindowId=o.hash.slice(1);e.attachEvent("message",(function(e){if(e.source===parent&&(void 0===l&&(l=e.origin),e.origin===l)){var a;try{a=JSON.parse(e.data)}catch(m){return void e.data}if(a.windowId===i.currentWindowId)switch(a.type){case"s":var c;try{c=JSON.parse(a.data)}catch(m){a.data;break}var h=c[0],f=c[1],d=c[2],p=c[3];if(h!==s.version)throw new Error('Incompatible SockJS! Main site uses: "'+h+'", the iframe: "'+s.version+'".');if(!t.isOriginEqual(d,o.href)||!t.isOriginEqual(p,o.href))throw new Error("Can't connect to different domain from within an iframe. ("+o.href+", "+d+", "+p+")");r=new n(new u[f](d,p));break;case"m":r._send(a.data);break;case"c":r&&r._close(),r=null}}})),i.postMessage("s")}}}function We(){if(Se)return xe;Se=1,Ae();var t,r=e(),i=n(),o=h(),s=Ie(),a=d(),l=f(),u=Zt?Yt:(Zt=1,Yt=function(t){return{filterToEnabled:function(e,n){var r={main:[],facade:[]};return e?"string"==typeof e&&(e=[e]):e=[],t.forEach((function(t){t&&("websocket"===t.transportName&&!1===n.websocket||(e.length&&-1===e.indexOf(t.transportName)?t.transportName:t.enabled(n)?(t.transportName,r.main.push(t),t.facadeTransport&&r.facade.push(t.facadeTransport)):t.transportName))})),r}}}),c=qt(),p=ut(),m=function(){if(ee)return te;ee=1;var t={};return["log","debug","warn"].forEach((function(e){var n;try{n=globalThis.console&&globalThis.console[e]&&globalThis.console[e].apply}catch(r){}t[e]=n?function(){return globalThis.console[e].apply(globalThis.console,arguments)}:"log"===e?function(){}:t.log})),te=t}(),b=je(),v=g(),y=ke(),w=function(){if(ae)return se;ae=1;var t=n(),e=je();function r(){e.call(this),this.initEvent("close",!1,!1),this.wasClean=!1,this.code=0,this.reason=""}return t(r,e),se=r}(),T=function(){if(ue)return le;ue=1;var t=n(),e=je();function r(t){e.call(this),this.initEvent("message",!1,!1),this.data=t}return t(r,e),le=r}(),_=Re();function E(t,e,n){if(!(this instanceof E))return new E(t,e,n);if(arguments.length<1)throw new TypeError("Failed to construct 'SockJS: 1 argument required, but only 0 present");v.call(this),this.readyState=E.CONNECTING,this.extensions="",this.protocol="",(n=n||{}).protocols_whitelist&&m.warn("'protocols_whitelist' is DEPRECATED. Use 'transports' instead."),this._transportsWhitelist=n.transports,this._transportOptions=n.transportOptions||{},this._timeout=n.timeout||0;var i=n.sessionId||8;if("function"==typeof i)this._generateSessionId=i;else{if("number"!=typeof i)throw new TypeError("If sessionId is used in the options, it needs to be a number or a function.");this._generateSessionId=function(){return o.string(i)}}this._server=n.server||o.numberString(1e3);var s=new r(t);if(!s.host||!s.protocol)throw new SyntaxError("The URL '"+t+"' is invalid");if(s.hash)throw new SyntaxError("The URL must not contain a fragment");if("http:"!==s.protocol&&"https:"!==s.protocol)throw new SyntaxError("The URL's scheme must be either 'http:' or 'https:'. '"+s.protocol+"' is not allowed.");var l="https:"===s.protocol;if("https:"===y.protocol&&!l&&!a.isLoopbackAddr(s.hostname))throw new Error("SecurityError: An insecure SockJS connection may not be initiated from a page loaded over HTTPS");e?Array.isArray(e)||(e=[e]):e=[];var u=e.sort();u.forEach((function(t,e){if(!t)throw new SyntaxError("The protocols entry '"+t+"' is invalid.");if(e<u.length-1&&t===u[e+1])throw new SyntaxError("The protocols entry '"+t+"' is duplicated.")}));var c=a.getOrigin(y.href);this._origin=c?c.toLowerCase():null,s.set("pathname",s.pathname.replace(/\/+$/,"")),this.url=s.href,this.url,this._urlInfo={nullOrigin:!p.hasDomain(),sameOrigin:a.isOriginEqual(this.url,y.href),sameScheme:a.isSchemeEqual(this.url,y.href)},this._ir=new _(this.url,this._urlInfo),this._ir.once("finish",this._receiveInfo.bind(this))}function x(t){return 1e3===t||t>=3e3&&t<=4999}return i(E,v),E.prototype.close=function(t,e){if(t&&!x(t))throw new Error("InvalidAccessError: Invalid code");if(e&&e.length>123)throw new SyntaxError("reason argument has an invalid length");if(this.readyState!==E.CLOSING&&this.readyState!==E.CLOSED){this._close(t||1e3,e||"Normal closure",!0)}},E.prototype.send=function(t){if("string"!=typeof t&&(t=""+t),this.readyState===E.CONNECTING)throw new Error("InvalidStateError: The connection has not been established yet");this.readyState===E.OPEN&&this._transport.send(s.quote(t))},E.version=mt(),E.CONNECTING=0,E.OPEN=1,E.CLOSING=2,E.CLOSED=3,E.prototype._receiveInfo=function(e,n){if(this._ir=null,e){this._rto=this.countRTO(n),this._transUrl=e.base_url?e.base_url:this.url,e=c.extend(e,this._urlInfo);var r=t.filterToEnabled(this._transportsWhitelist,e);this._transports=r.main,this._transports.length,this._connect()}else this._close(1002,"Cannot connect to server")},E.prototype._connect=function(){for(var t=this._transports.shift();t;t=this._transports.shift()){if(t.transportName,t.needBody&&(!globalThis.document.body||void 0!==globalThis.document.readyState&&"complete"!==globalThis.document.readyState&&"interactive"!==globalThis.document.readyState))return this._transports.unshift(t),void l.attachEvent("load",this._connect.bind(this));var e=Math.max(this._timeout,this._rto*t.roundTrips||5e3);this._transportTimeoutId=setTimeout(this._transportTimeout.bind(this),e);var n=a.addPath(this._transUrl,"/"+this._server+"/"+this._generateSessionId()),r=this._transportOptions[t.transportName],i=new t(n,this._transUrl,r);return i.on("message",this._transportMessage.bind(this)),i.once("close",this._transportClose.bind(this)),i.transportName=t.transportName,void(this._transport=i)}this._close(2e3,"All transports failed",!1)},E.prototype._transportTimeout=function(){this.readyState===E.CONNECTING&&(this._transport&&this._transport.close(),this._transportClose(2007,"Transport timed out"))},E.prototype._transportMessage=function(t){var e,n=this,r=t.slice(0,1),i=t.slice(1);switch(r){case"o":return void this._open();case"h":return this.dispatchEvent(new b("heartbeat")),void this.transport}if(i)try{e=JSON.parse(i)}catch(o){}if(void 0!==e)switch(r){case"a":Array.isArray(e)&&e.forEach((function(t){n.transport,n.dispatchEvent(new T(t))}));break;case"m":this.transport,this.dispatchEvent(new T(e));break;case"c":Array.isArray(e)&&2===e.length&&this._close(e[0],e[1],!0)}},E.prototype._transportClose=function(t,e){this.transport,this._transport&&(this._transport.removeAllListeners(),this._transport=null,this.transport=null),x(t)||2e3===t||this.readyState!==E.CONNECTING?this._close(t,e):this._connect()},E.prototype._open=function(){this._transport&&this._transport.transportName,this.readyState,this.readyState===E.CONNECTING?(this._transportTimeoutId&&(clearTimeout(this._transportTimeoutId),this._transportTimeoutId=null),this.readyState=E.OPEN,this.transport=this._transport.transportName,this.dispatchEvent(new b("open")),this.transport):this._close(1006,"Server lost session")},E.prototype._close=function(t,e,n){this.transport,this.readyState;var r=!1;if(this._ir&&(r=!0,this._ir.close(),this._ir=null),this._transport&&(this._transport.close(),this._transport=null,this.transport=null),this.readyState===E.CLOSED)throw new Error("InvalidStateError: SockJS has already been closed");this.readyState=E.CLOSING,setTimeout(function(){this.readyState=E.CLOSED,r&&this.dispatchEvent(new b("error"));var i=new w("close");i.wasClean=n||!1,i.code=t||1e3,i.reason=e,this.dispatchEvent(i),this.onmessage=this.onclose=this.onerror=null}.bind(this),0)},E.prototype.countRTO=function(t){return t>100?4*t:300+t},xe=function(e){return t=u(e),Me()(E,e),E}}const Ue=t(function(){if(Ne)return Oe;Ne=1;var t=Ut?Wt:(Ut=1,Wt=[nt(),ct(),ft(),pt(),Gt()(pt()),Ht(),Gt()(Ht()),Ft(),Xt(),Gt()(Ft()),$t()]);return Oe=We()(t),"_sockjs_onload"in globalThis&&setTimeout(globalThis._sockjs_onload,1),Oe}());export{Ue as S};
