/**
 * 特权验证状态常量
 */

/** 验证状态枚举 */
export const VERIFICATION_STATUS = {
  /** 进行中 */
  IN_PROGRESS: 0,
  /** 成功 */
  SUCCESS: 1,
  /** 失败 */
  FAILED: 2,
  /** 超时 */
  TIMEOUT: 3,
} as const

/** 验证状态标签映射 */
export const VERIFICATION_STATUS_LABELS = {
  [VERIFICATION_STATUS.IN_PROGRESS]: '进行中',
  [VERIFICATION_STATUS.SUCCESS]: '成功',
  [VERIFICATION_STATUS.FAILED]: '失败',
  [VERIFICATION_STATUS.TIMEOUT]: '超时',
} as const

/** 验证步骤枚举 */
export const VERIFICATION_STEP = {
  /** 第一步 */
  STEP_ONE: 1,
  /** 第二步 */
  STEP_TWO: 2,
  /** 第三步 */
  STEP_THREE: 3,
} as const

/** 验证步骤标签映射 */
export const VERIFICATION_STEP_LABELS = {
  [VERIFICATION_STEP.STEP_ONE]: '发起申请',
  [VERIFICATION_STEP.STEP_TWO]: '等待确认',
  [VERIFICATION_STEP.STEP_THREE]: '验证完成',
} as const

/** 验证步骤描述映射 */
export const VERIFICATION_STEP_DESCRIPTIONS = {
  [VERIFICATION_STEP.STEP_ONE]: '生成验证页面并发送邮件',
  [VERIFICATION_STEP.STEP_TWO]: '特权提供者访问验证页面',
  [VERIFICATION_STEP.STEP_THREE]: '验证流程完成',
} as const
