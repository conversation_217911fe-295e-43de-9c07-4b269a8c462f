import { onUnmounted } from 'vue'

import verificationApi from '@/api/verification'
import { VERIFICATION_STATUS } from '@/constants/verification/verification-status.constants'
import type { VerificationResponse } from '@/types/verification/verification-response.types'

/**
 * 验证轮询管理组合式函数返回值类型
 */
interface UseVerificationPollingReturn {
  /** 开始轮询 */
  startPolling: (
    verificationId: number,
    onUpdate: (verification: VerificationResponse) => void,
    interval?: number,
  ) => void
  /** 停止指定验证的轮询 */
  stopPolling: (verificationId: number) => void
  /** 停止所有轮询 */
  stopAllPolling: () => void
}

/**
 * 验证轮询管理组合式函数
 * 提供验证状态的实时轮询功能
 */
export function useVerificationPolling(): UseVerificationPollingReturn {
  // 存储所有轮询定时器
  const pollingTimers = new Map<number, NodeJS.Timeout>()

  /**
   * 开始轮询验证状态
   * @param verificationId 验证流程ID
   * @param onUpdate 状态更新回调函数
   * @param interval 轮询间隔（毫秒），默认3秒
   */
  const startPolling = (
    verificationId: number,
    onUpdate: (verification: VerificationResponse) => void,
    interval = 3000,
  ): void => {
    // 清除已存在的定时器
    stopPolling(verificationId)

    const timer = setInterval(async () => {
      try {
        const response = await verificationApi.getVerificationStatus(verificationId)
        if (response.success && response.data) {
          onUpdate(response.data)

          // 如果验证完成（成功、失败或超时），停止轮询
          const completedStatuses = [
            VERIFICATION_STATUS.SUCCESS,
            VERIFICATION_STATUS.FAILED,
            VERIFICATION_STATUS.TIMEOUT,
          ]

          if (completedStatuses.includes(response.data.status)) {
            stopPolling(verificationId)
          }
        }
      } catch (error) {
        console.error('轮询验证状态失败:', error)
        // 发生错误时也停止轮询，避免持续报错
        stopPolling(verificationId)
      }
    }, interval)

    pollingTimers.set(verificationId, timer)
  }

  /**
   * 停止指定验证的轮询
   * @param verificationId 验证流程ID
   */
  const stopPolling = (verificationId: number): void => {
    const timer = pollingTimers.get(verificationId)
    if (timer) {
      clearInterval(timer)
      pollingTimers.delete(verificationId)
    }
  }

  /**
   * 停止所有轮询
   * 清理所有正在进行的轮询定时器
   */
  const stopAllPolling = (): void => {
    pollingTimers.forEach((timer) => clearInterval(timer))
    pollingTimers.clear()
  }

  // 组件卸载时自动清理所有定时器
  onUnmounted(() => {
    stopAllPolling()
  })

  return {
    startPolling,
    stopPolling,
    stopAllPolling,
  }
}
