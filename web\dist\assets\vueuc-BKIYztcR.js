import{q as t,F as e,C as n,d as o,p as r,g as l,i,r as s,c as a,B as u,h as d,I as f,t as c,x as h,o as p,f as m,n as v,W as g,z as b,k as y,l as w}from"./@vue-C18CzuYV.js";import{a as x,o as R}from"./evtd-hWw0KU7y.js";import{b as S,d as T,B as $,f as M,a as z}from"./seemly-Cdi3gWV3.js";import{u as I}from"./@css-render-DcoOFqSU.js";import{g as E,u as C,o as B,i as W}from"./vooks-n2t0Q59N.js";import{z as L}from"./vdirs-Bvq7nEML.js";import{R as F}from"./@juggle-BnTvdTVm.js";import{C as A}from"./css-render-7x70jhNC.js";function k(t,e,n="default"){const o=e[n];if(void 0===o)throw new Error(`[vueuc/${t}]: slot[${n}] is empty.`);return o()}function N(o,r=!0,l=[]){return o.forEach((o=>{if(null!==o)if("object"==typeof o)if(Array.isArray(o))N(o,r,l);else if(o.type===e){if(null===o.children)return;Array.isArray(o.children)&&N(o.children,r,l)}else o.type!==n&&l.push(o);else"string"!=typeof o&&"number"!=typeof o||l.push(t(String(o)))})),l}function X(t,e,n="default"){const o=e[n];if(void 0===o)throw new Error(`[vueuc/${t}]: slot[${n}] is empty.`);const r=N(o());if(1===r.length)return r[0];throw new Error(`[vueuc/${t}]: slot[${n}] should have exactly one child.`)}let Y=null;function j(){if(null===Y&&(Y=document.getElementById("v-binder-view-measurer"),null===Y)){Y=document.createElement("div"),Y.id="v-binder-view-measurer";const{style:t}=Y;t.position="fixed",t.left="0",t.right="0",t.top="0",t.bottom="0",t.pointerEvents="none",t.visibility="hidden",document.body.appendChild(Y)}return Y.getBoundingClientRect()}function H(t){const e=t.getBoundingClientRect(),n=j();return{left:e.left-n.left,top:e.top-n.top,bottom:n.height+n.top-e.bottom,right:n.width+n.left-e.right,width:e.width,height:e.height}}function O(t){if(null===t)return null;const e=function(t){return 9===t.nodeType?null:t.parentNode}(t);if(null===e)return null;if(9===e.nodeType)return document;if(1===e.nodeType){const{overflow:t,overflowX:n,overflowY:o}=getComputedStyle(e);if(/(auto|scroll|overlay)/.test(t+o+n))return e}return O(e)}const P=o({name:"Binder",props:{syncTargetWithParent:Boolean,syncTarget:{type:Boolean,default:!0}},setup(t){var e;r("VBinder",null===(e=l())||void 0===e?void 0:e.proxy);const n=i("VBinder",null),o=s(null);let u=[];const d=()=>{for(const t of u)x("scroll",t,c,!0);u=[]},f=new Set,c=()=>{S(h)},h=()=>{f.forEach((t=>t()))},p=new Set,m=()=>{p.forEach((t=>t()))};return a((()=>{x("resize",window,m),d()})),{targetRef:o,setTargetRef:e=>{o.value=e,n&&t.syncTargetWithParent&&n.setTargetRef(e)},addScrollListener:t=>{0===f.size&&(()=>{let t=o.value;for(;t=O(t),null!==t;)u.push(t);for(const e of u)R("scroll",e,c,!0)})(),f.has(t)||f.add(t)},removeScrollListener:t=>{f.has(t)&&f.delete(t),0===f.size&&d()},addResizeListener:t=>{0===p.size&&R("resize",window,m),p.has(t)||p.add(t)},removeResizeListener:t=>{p.has(t)&&p.delete(t),0===p.size&&x("resize",window,m)}}},render(){return k("binder",this.$slots)}}),V=o({name:"Target",setup(){const{setTargetRef:t,syncTarget:e}=i("VBinder");return{syncTarget:e,setTargetDirective:{mounted:t,updated:t}}},render(){const{syncTarget:t,setTargetDirective:e}=this;return t?u(X("follower",this.$slots),[[e]]):X("follower",this.$slots)}});const{c:D}=A(),q="vueuc-style";function U(t){return t&-t}class G{constructor(t,e){this.l=t,this.min=e;const n=new Array(t+1);for(let o=0;o<t+1;++o)n[o]=0;this.ft=n}add(t,e){if(0===e)return;const{l:n,ft:o}=this;for(t+=1;t<=n;)o[t]+=e,t+=U(t)}get(t){return this.sum(t+1)-this.sum(t)}sum(t){if(void 0===t&&(t=this.l),t<=0)return 0;const{ft:e,min:n,l:o}=this;if(t>o)throw new Error("[FinweckTree.sum]: `i` is larger than length.");let r=t*n;for(;t>0;)r+=e[t],t-=U(t);return r}getBound(t){let e=0,n=this.l;for(;n>e;){const o=Math.floor((e+n)/2),r=this.sum(o);if(r>t)n=o;else{if(!(r<t))return o;if(e===o)return this.sum(e+1)<=t?e+1:o;e=o}}return e}}function K(t){return"string"==typeof t?document.querySelector(t):t()}const _=o({name:"LazyTeleport",props:{to:{type:[String,Object],default:void 0},disabled:Boolean,show:{type:Boolean,required:!0}},setup:t=>({showTeleport:E(h(t,"show")),mergedTo:c((()=>{const{to:e}=t;return null!=e?e:"body"}))}),render(){return this.showTeleport?this.disabled?k("lazy-teleport",this.$slots):d(f,{disabled:this.disabled,to:this.mergedTo},k("lazy-teleport",this.$slots)):null}}),J={top:"bottom",bottom:"top",left:"right",right:"left"},Q={start:"end",center:"center",end:"start"},Z={top:"height",bottom:"height",left:"width",right:"width"},tt={"bottom-start":"top left",bottom:"top center","bottom-end":"top right","top-start":"bottom left",top:"bottom center","top-end":"bottom right","right-start":"top left",right:"center left","right-end":"bottom left","left-start":"top right",left:"center right","left-end":"bottom right"},et={"bottom-start":"bottom left",bottom:"bottom center","bottom-end":"bottom right","top-start":"top left",top:"top center","top-end":"top right","right-start":"top right",right:"center right","right-end":"bottom right","left-start":"top left",left:"center left","left-end":"bottom left"},nt={"bottom-start":"right","bottom-end":"left","top-start":"right","top-end":"left","right-start":"bottom","right-end":"top","left-start":"bottom","left-end":"top"},ot={top:!0,bottom:!1,left:!0,right:!1},rt={top:"end",bottom:"start",left:"end",right:"start"};const lt=D([D(".v-binder-follower-container",{position:"absolute",left:"0",right:"0",top:"0",height:"0",pointerEvents:"none",zIndex:"auto"}),D(".v-binder-follower-content",{position:"absolute",zIndex:"auto"},[D("> *",{pointerEvents:"all"})])]),it=o({name:"Follower",inheritAttrs:!1,props:{show:Boolean,enabled:{type:Boolean,default:void 0},placement:{type:String,default:"bottom"},syncTrigger:{type:Array,default:["resize","scroll"]},to:[String,Object],flip:{type:Boolean,default:!0},internalShift:Boolean,x:Number,y:Number,width:String,minWidth:String,containerClass:String,teleportDisabled:Boolean,zindexable:{type:Boolean,default:!0},zIndex:Number,overlap:Boolean},setup(t){const e=i("VBinder"),n=C((()=>void 0!==t.enabled?t.enabled:t.show)),o=s(null),r=s(null),l=()=>{const{syncTrigger:n}=t;n.includes("scroll")&&e.addScrollListener(f),n.includes("resize")&&e.addResizeListener(f)},u=()=>{e.removeScrollListener(f),e.removeResizeListener(f)};p((()=>{n.value&&(f(),l())}));const d=I();lt.mount({id:"vueuc/binder",head:!0,anchorMetaName:q,ssr:d}),a((()=>{u()})),B((()=>{n.value&&f()}));const f=()=>{if(!n.value)return;const l=o.value;if(null===l)return;const i=e.targetRef,{x:s,y:a,overlap:u}=t,d=void 0!==s&&void 0!==a?function(t,e){const n=j();return{top:e,left:t,height:0,width:0,right:n.width-t,bottom:n.height-e}}(s,a):H(i);l.style.setProperty("--v-target-width",`${Math.round(d.width)}px`),l.style.setProperty("--v-target-height",`${Math.round(d.height)}px`);const{width:f,minWidth:c,placement:h,internalShift:p,flip:m}=t;l.setAttribute("v-placement",h),u?l.setAttribute("v-overlap",""):l.removeAttribute("v-overlap");const{style:v}=l;v.width="target"===f?`${d.width}px`:void 0!==f?f:"",v.minWidth="target"===c?`${d.width}px`:void 0!==c?c:"";const g=H(l),b=H(r.value),{left:y,top:w,placement:x}=function(t,e,n,o,r,l){if(!r||l)return{placement:t,top:0,left:0};const[i,s]=t.split("-");let a=null!=s?s:"center",u={top:0,left:0};const d=(t,r,l)=>{let i=0,s=0;const a=n[t]-e[r]-e[t];return a>0&&o&&(l?s=ot[r]?a:-a:i=ot[r]?a:-a),{left:i,top:s}},f="left"===i||"right"===i;if("center"!==a){const o=nt[t],r=J[o],l=Z[o];if(n[l]>e[l]){if(e[o]+e[l]<n[l]){const t=(n[l]-e[l])/2;e[o]<t||e[r]<t?e[o]<e[r]?(a=Q[s],u=d(l,r,f)):u=d(l,o,f):a="center"}}else n[l]<e[l]&&e[r]<0&&e[o]>e[r]&&(a=Q[s])}else{const t="bottom"===i||"top"===i?"left":"top",o=J[t],r=Z[t],l=(n[r]-e[r])/2;(e[t]<l||e[o]<l)&&(e[t]>e[o]?(a=rt[t],u=d(r,t,f)):(a=rt[o],u=d(r,o,f)))}let c=i;return e[i]<n[Z[i]]&&e[i]<e[J[i]]&&(c=J[i]),{placement:"center"!==a?`${c}-${a}`:c,left:u.left,top:u.top}}(h,d,g,p,m,u),R=function(t,e){return e?et[t]:tt[t]}(x,u),{left:S,top:T,transform:$}=function(t,e,n,o,r,l){if(l)switch(t){case"bottom-start":case"left-end":return{top:`${Math.round(n.top-e.top+n.height)}px`,left:`${Math.round(n.left-e.left)}px`,transform:"translateY(-100%)"};case"bottom-end":case"right-end":return{top:`${Math.round(n.top-e.top+n.height)}px`,left:`${Math.round(n.left-e.left+n.width)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top-start":case"left-start":return{top:`${Math.round(n.top-e.top)}px`,left:`${Math.round(n.left-e.left)}px`,transform:""};case"top-end":case"right-start":return{top:`${Math.round(n.top-e.top)}px`,left:`${Math.round(n.left-e.left+n.width)}px`,transform:"translateX(-100%)"};case"top":return{top:`${Math.round(n.top-e.top)}px`,left:`${Math.round(n.left-e.left+n.width/2)}px`,transform:"translateX(-50%)"};case"right":return{top:`${Math.round(n.top-e.top+n.height/2)}px`,left:`${Math.round(n.left-e.left+n.width)}px`,transform:"translateX(-100%) translateY(-50%)"};case"left":return{top:`${Math.round(n.top-e.top+n.height/2)}px`,left:`${Math.round(n.left-e.left)}px`,transform:"translateY(-50%)"};default:return{top:`${Math.round(n.top-e.top+n.height)}px`,left:`${Math.round(n.left-e.left+n.width/2)}px`,transform:"translateX(-50%) translateY(-100%)"}}switch(t){case"bottom-start":return{top:`${Math.round(n.top-e.top+n.height+o)}px`,left:`${Math.round(n.left-e.left+r)}px`,transform:""};case"bottom-end":return{top:`${Math.round(n.top-e.top+n.height+o)}px`,left:`${Math.round(n.left-e.left+n.width+r)}px`,transform:"translateX(-100%)"};case"top-start":return{top:`${Math.round(n.top-e.top+o)}px`,left:`${Math.round(n.left-e.left+r)}px`,transform:"translateY(-100%)"};case"top-end":return{top:`${Math.round(n.top-e.top+o)}px`,left:`${Math.round(n.left-e.left+n.width+r)}px`,transform:"translateX(-100%) translateY(-100%)"};case"right-start":return{top:`${Math.round(n.top-e.top+o)}px`,left:`${Math.round(n.left-e.left+n.width+r)}px`,transform:""};case"right-end":return{top:`${Math.round(n.top-e.top+n.height+o)}px`,left:`${Math.round(n.left-e.left+n.width+r)}px`,transform:"translateY(-100%)"};case"left-start":return{top:`${Math.round(n.top-e.top+o)}px`,left:`${Math.round(n.left-e.left+r)}px`,transform:"translateX(-100%)"};case"left-end":return{top:`${Math.round(n.top-e.top+n.height+o)}px`,left:`${Math.round(n.left-e.left+r)}px`,transform:"translateX(-100%) translateY(-100%)"};case"top":return{top:`${Math.round(n.top-e.top+o)}px`,left:`${Math.round(n.left-e.left+n.width/2+r)}px`,transform:"translateY(-100%) translateX(-50%)"};case"right":return{top:`${Math.round(n.top-e.top+n.height/2+o)}px`,left:`${Math.round(n.left-e.left+n.width+r)}px`,transform:"translateY(-50%)"};case"left":return{top:`${Math.round(n.top-e.top+n.height/2+o)}px`,left:`${Math.round(n.left-e.left+r)}px`,transform:"translateY(-50%) translateX(-100%)"};default:return{top:`${Math.round(n.top-e.top+n.height+o)}px`,left:`${Math.round(n.left-e.left+n.width/2+r)}px`,transform:"translateX(-50%)"}}}(x,b,d,w,y,u);l.setAttribute("v-placement",x),l.style.setProperty("--v-offset-left",`${Math.round(y)}px`),l.style.setProperty("--v-offset-top",`${Math.round(w)}px`),l.style.transform=`translateX(${S}) translateY(${T}) ${$}`,l.style.setProperty("--v-transform-origin",R),l.style.transformOrigin=R};m(n,(t=>{t?(l(),c()):u()}));const c=()=>{v().then(f).catch((t=>{}))};["placement","x","y","internalShift","flip","width","overlap","minWidth"].forEach((e=>{m(h(t,e),f)})),["teleportDisabled"].forEach((e=>{m(h(t,e),c)})),m(h(t,"syncTrigger"),(t=>{t.includes("resize")?e.addResizeListener(f):e.removeResizeListener(f),t.includes("scroll")?e.addScrollListener(f):e.removeScrollListener(f)}));const g=W(),b=C((()=>{const{to:e}=t;if(void 0!==e)return e;g.value}));return{VBinder:e,mergedEnabled:n,offsetContainerRef:r,followerRef:o,mergedTo:b,syncPosition:f}},render(){return d(_,{show:this.show,to:this.mergedTo,disabled:this.teleportDisabled},{default:()=>{var t,e;const n=d("div",{class:["v-binder-follower-container",this.containerClass],ref:"offsetContainerRef"},[d("div",{class:"v-binder-follower-content",ref:"followerRef"},null===(e=(t=this.$slots).default)||void 0===e?void 0:e.call(t))]);return this.zindexable?u(n,[[L,{enabled:this.mergedEnabled,zIndex:this.zIndex}]]):n}})}});const st=new class{constructor(){this.handleResize=this.handleResize.bind(this),this.observer=new("undefined"!=typeof window&&window.ResizeObserver||F)(this.handleResize),this.elHandlersMap=new Map}handleResize(t){for(const e of t){const t=this.elHandlersMap.get(e.target);void 0!==t&&t(e)}}registerHandler(t,e){this.elHandlersMap.set(t,e),this.observer.observe(t)}unregisterHandler(t){this.elHandlersMap.has(t)&&(this.elHandlersMap.delete(t),this.observer.unobserve(t))}},at=o({name:"ResizeObserver",props:{onResize:Function},setup(t){let e=!1;const n=l().proxy;function o(e){const{onResize:n}=t;void 0!==n&&n(e)}p((()=>{const t=n.$el;void 0!==t&&(t.nextElementSibling!==t.nextSibling&&3===t.nodeType&&""!==t.nodeValue||null!==t.nextElementSibling&&(st.registerHandler(t.nextElementSibling,o),e=!0))})),a((()=>{e&&st.unregisterHandler(n.$el.nextElementSibling)}))},render(){return g(this.$slots,"default")}});let ut,dt;function ft(){return"undefined"==typeof document?1:(void 0===dt&&(dt="chrome"in window?window.devicePixelRatio:1),dt)}const ct="VVirtualListXScroll";const ht=o({name:"VirtualListRow",props:{index:{type:Number,required:!0},item:{type:Object,required:!0}},setup(){const{startIndexRef:t,endIndexRef:e,columnsRef:n,getLeft:o,renderColRef:r,renderItemWithColsRef:l}=i(ct);return{startIndex:t,endIndex:e,columns:n,renderCol:r,renderItemWithCols:l,getLeft:o}},render(){const{startIndex:t,endIndex:e,columns:n,renderCol:o,renderItemWithCols:r,getLeft:l,item:i}=this;if(null!=r)return r({itemIndex:this.index,startColIndex:t,endColIndex:e,allColumns:n,item:i,getLeft:l});if(null!=o){const r=[];for(let s=t;s<=e;++s){const t=n[s];r.push(o({column:t,left:l(s),item:i}))}return r}return null}}),pt=D(".v-vl",{maxHeight:"inherit",height:"100%",overflow:"auto",minWidth:"1px"},[D("&:not(.v-vl--show-scrollbar)",{scrollbarWidth:"none"},[D("&::-webkit-scrollbar, &::-webkit-scrollbar-track-piece, &::-webkit-scrollbar-thumb",{width:0,height:0,display:"none"})])]),mt=o({name:"VirtualList",inheritAttrs:!1,props:{showScrollbar:{type:Boolean,default:!0},columns:{type:Array,default:()=>[]},renderCol:Function,renderItemWithCols:Function,items:{type:Array,default:()=>[]},itemSize:{type:Number,required:!0},itemResizable:Boolean,itemsStyle:[String,Object],visibleItemsTag:{type:[String,Object],default:"div"},visibleItemsProps:Object,ignoreItemResize:Boolean,onScroll:Function,onWheel:Function,onResize:Function,defaultScrollKey:[Number,String],defaultScrollIndex:Number,keyField:{type:String,default:"key"},paddingTop:{type:[Number,String],default:0},paddingBottom:{type:[Number,String],default:0}},setup(t){const e=I();pt.mount({id:"vueuc/virtual-list",head:!0,anchorMetaName:q,ssr:e}),p((()=>{const{defaultScrollIndex:e,defaultScrollKey:n}=t;null!=e?M({index:e}):null!=n&&M({key:n})}));let n=!1,o=!1;y((()=>{n=!1,o?M({top:b.value,left:a.value}):o=!0})),w((()=>{n=!0,o||(o=!0)}));const l=C((()=>{if(null==t.renderCol&&null==t.renderItemWithCols)return;if(0===t.columns.length)return;let e=0;return t.columns.forEach((t=>{e+=t.width})),e})),i=c((()=>{const e=new Map,{keyField:n}=t;return t.items.forEach(((t,o)=>{e.set(t[n],o)})),e})),{scrollLeftRef:a,listWidthRef:u}=function({columnsRef:t,renderColRef:e,renderItemWithColsRef:n}){const o=s(0),l=s(0),i=c((()=>{const e=t.value;if(0===e.length)return null;const n=new G(e.length,0);return e.forEach(((t,e)=>{n.add(e,t.width)})),n})),a=C((()=>{const t=i.value;return null!==t?Math.max(t.getBound(l.value)-1,0):0})),u=C((()=>{const e=i.value;return null!==e?Math.min(e.getBound(l.value+o.value)+1,t.value.length-1):0}));return r(ct,{startIndexRef:a,endIndexRef:u,columnsRef:t,renderColRef:e,renderItemWithColsRef:n,getLeft:t=>{const e=i.value;return null!==e?e.sum(t):0}}),{listWidthRef:o,scrollLeftRef:l}}({columnsRef:h(t,"columns"),renderColRef:h(t,"renderCol"),renderItemWithColsRef:h(t,"renderItemWithCols")}),d=s(null),f=s(void 0),m=new Map,v=c((()=>{const{items:e,itemSize:n,keyField:o}=t,r=new G(e.length,n);return e.forEach(((t,e)=>{const n=t[o],l=m.get(n);void 0!==l&&r.add(e,l)})),r})),g=s(0),b=s(0),x=C((()=>Math.max(v.value.getBound(b.value-T(t.paddingTop))-1,0))),R=c((()=>{const{value:e}=f;if(void 0===e)return[];const{items:n,itemSize:o}=t,r=x.value,l=Math.min(r+Math.ceil(e/o+1),n.length-1),i=[];for(let t=r;t<=l;++t)i.push(n[t]);return i})),M=(t,e)=>{if("number"==typeof t)return void W(t,e,"auto");const{left:n,top:o,index:r,key:l,position:s,behavior:a,debounce:u=!0}=t;if(void 0!==n||void 0!==o)W(n,o,a);else if(void 0!==r)B(r,a,u);else if(void 0!==l){const t=i.value.get(l);void 0!==t&&B(t,a,u)}else"bottom"===s?W(0,Number.MAX_SAFE_INTEGER,a):"top"===s&&W(0,0,a)};let z,E=null;function B(e,n,o){const{value:r}=v,l=r.sum(e)+T(t.paddingTop);if(o){z=e,null!==E&&window.clearTimeout(E),E=window.setTimeout((()=>{z=void 0,E=null}),16);const{scrollTop:t,offsetHeight:o}=d.value;if(l>t){const i=r.get(e);l+i<=t+o||d.value.scrollTo({left:0,top:l+i-o,behavior:n})}else d.value.scrollTo({left:0,top:l,behavior:n})}else d.value.scrollTo({left:0,top:l,behavior:n})}function W(t,e,n){d.value.scrollTo({left:t,top:e,behavior:n})}const L=!("undefined"!=typeof document&&(void 0===ut&&(ut="matchMedia"in window&&window.matchMedia("(pointer:coarse)").matches),ut));let F=!1;function A(){const{value:t}=d;null!=t&&(b.value=t.scrollTop,a.value=t.scrollLeft)}function k(t){let e=t;for(;null!==e;){if("none"===e.style.display)return!0;e=e.parentElement}return!1}return{listHeight:f,listStyle:{overflow:"auto"},keyToIndex:i,itemsStyle:c((()=>{const{itemResizable:e}=t,n=$(v.value.sum());return g.value,[t.itemsStyle,{boxSizing:"content-box",width:$(l.value),height:e?"":n,minHeight:e?n:"",paddingTop:$(t.paddingTop),paddingBottom:$(t.paddingBottom)}]})),visibleItemsStyle:c((()=>(g.value,{transform:`translateY(${$(v.value.sum(x.value))})`}))),viewportItems:R,listElRef:d,itemsElRef:s(null),scrollTo:M,handleListResize:function(e){if(n)return;if(k(e.target))return;if(null==t.renderCol&&null==t.renderItemWithCols){if(e.contentRect.height===f.value)return}else if(e.contentRect.height===f.value&&e.contentRect.width===u.value)return;f.value=e.contentRect.height,u.value=e.contentRect.width;const{onResize:o}=t;void 0!==o&&o(e)},handleListScroll:function(e){var n;null===(n=t.onScroll)||void 0===n||n.call(t,e),L&&F||A()},handleListWheel:function(e){var n;if(null===(n=t.onWheel)||void 0===n||n.call(t,e),L){const t=d.value;if(null!=t){if(0===e.deltaX){if(0===t.scrollTop&&e.deltaY<=0)return;if(t.scrollTop+t.offsetHeight>=t.scrollHeight&&e.deltaY>=0)return}e.preventDefault(),t.scrollTop+=e.deltaY/ft(),t.scrollLeft+=e.deltaX/ft(),A(),F=!0,S((()=>{F=!1}))}}},handleItemResize:function(e,o){var r,l,s;if(n)return;if(t.ignoreItemResize)return;if(k(o.target))return;const{value:a}=v,u=i.value.get(e),f=a.get(u),c=null!==(s=null===(l=null===(r=o.borderBoxSize)||void 0===r?void 0:r[0])||void 0===l?void 0:l.blockSize)&&void 0!==s?s:o.contentRect.height;if(c===f)return;0===c-t.itemSize?m.delete(e):m.set(e,c-t.itemSize);const h=c-f;if(0===h)return;a.add(u,h);const p=d.value;if(null!=p){if(void 0===z){const t=a.sum(u);p.scrollTop>t&&p.scrollBy(0,h)}else if(u<z)p.scrollBy(0,h);else if(u===z){c+a.sum(u)>p.scrollTop+p.offsetHeight&&p.scrollBy(0,h)}A()}g.value++}}},render(){const{itemResizable:t,keyField:e,keyToIndex:n,visibleItemsTag:o}=this;return d(at,{onResize:this.handleListResize},{default:()=>{var r,l;return d("div",b(this.$attrs,{class:["v-vl",this.showScrollbar&&"v-vl--show-scrollbar"],onScroll:this.handleListScroll,onWheel:this.handleListWheel,ref:"listElRef"}),[0!==this.items.length?d("div",{ref:"itemsElRef",class:"v-vl-items",style:this.itemsStyle},[d(o,Object.assign({class:"v-vl-visible-items",style:this.visibleItemsStyle},this.visibleItemsProps),{default:()=>{const{renderCol:o,renderItemWithCols:r}=this;return this.viewportItems.map((l=>{const i=l[e],s=n.get(i),a=null!=o?d(ht,{index:s,item:l}):void 0,u=null!=r?d(ht,{index:s,item:l}):void 0,f=this.$slots.default({item:l,renderedCols:a,renderedItemWithCols:u,index:s})[0];return t?d(at,{key:i,onResize:t=>this.handleItemResize(i,t)},{default:()=>f}):(f.key=i,f)}))}})]):null===(l=(r=this.$slots).empty)||void 0===l?void 0:l.call(r)])}})}}),vt=D(".v-x-scroll",{overflow:"auto",scrollbarWidth:"none"},[D("&::-webkit-scrollbar",{width:0,height:0})]),gt=o({name:"XScroll",props:{disabled:Boolean,onScroll:Function},setup(){const t=s(null);const e=I();vt.mount({id:"vueuc/x-scroll",head:!0,anchorMetaName:q,ssr:e});const n={scrollTo(...e){var n;null===(n=t.value)||void 0===n||n.scrollTo(...e)}};return Object.assign({selfRef:t,handleWheel:function(t){t.currentTarget.offsetWidth<t.currentTarget.scrollWidth&&0!==t.deltaY&&(t.currentTarget.scrollLeft+=t.deltaY+t.deltaX,t.preventDefault())}},n)},render(){return d("div",{ref:"selfRef",onScroll:this.onScroll,onWheel:this.disabled?void 0:this.handleWheel,class:"v-x-scroll"},this.$slots)}}),bt="v-hidden",yt=D("[v-hidden]",{display:"none!important"}),wt=o({name:"Overflow",props:{getCounter:Function,getTail:Function,updateCounter:Function,onUpdateCount:Function,onUpdateOverflow:Function},setup(t,{slots:e}){const n=s(null),o=s(null);function r(r){const{value:l}=n,{getCounter:i,getTail:s}=t;let a;if(a=void 0!==i?i():o.value,!l||!a)return;a.hasAttribute(bt)&&a.removeAttribute(bt);const{children:u}=l;if(r.showAllItemsBeforeCalculate)for(const t of u)t.hasAttribute(bt)&&t.removeAttribute(bt);const d=l.offsetWidth,f=[],c=e.tail?null==s?void 0:s():null;let h=c?c.offsetWidth:0,p=!1;const m=l.children.length-(e.tail?1:0);for(let e=0;e<m-1;++e){if(e<0)continue;const n=u[e];if(p){n.hasAttribute(bt)||n.setAttribute(bt,"");continue}n.hasAttribute(bt)&&n.removeAttribute(bt);const o=n.offsetWidth;if(h+=o,f[e]=o,h>d){const{updateCounter:n}=t;for(let o=e;o>=0;--o){const r=m-1-o;void 0!==n?n(r):a.textContent=`${r}`;const l=a.offsetWidth;if(h-=f[o],h+l<=d||0===o){p=!0,e=o-1,c&&(-1===e?(c.style.maxWidth=d-l+"px",c.style.boxSizing="border-box"):c.style.maxWidth="");const{onUpdateCount:n}=t;n&&n(r);break}}}}const{onUpdateOverflow:v}=t;p?void 0!==v&&v(!0):(void 0!==v&&v(!1),a.setAttribute(bt,""))}const l=I();return yt.mount({id:"vueuc/overflow",head:!0,anchorMetaName:q,ssr:l}),p((()=>r({showAllItemsBeforeCalculate:!1}))),{selfRef:n,counterRef:o,sync:r}},render(){const{$slots:t}=this;return v((()=>this.sync({showAllItemsBeforeCalculate:!1}))),d("div",{class:"v-overflow",ref:"selfRef"},[g(t,"default"),t.counter?t.counter():d("span",{style:{display:"inline-block"},ref:"counterRef"}),t.tail?t.tail():null])}});function xt(t){return t instanceof HTMLElement}function Rt(t){for(let e=0;e<t.childNodes.length;e++){const n=t.childNodes[e];if(xt(n)&&(Tt(n)||Rt(n)))return!0}return!1}function St(t){for(let e=t.childNodes.length-1;e>=0;e--){const n=t.childNodes[e];if(xt(n)&&(Tt(n)||St(n)))return!0}return!1}function Tt(t){if(!function(t){if(t.tabIndex>0||0===t.tabIndex&&null!==t.getAttribute("tabIndex"))return!0;if(t.getAttribute("disabled"))return!1;switch(t.nodeName){case"A":return!!t.href&&"ignore"!==t.rel;case"INPUT":return"hidden"!==t.type&&"file"!==t.type;case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}}(t))return!1;try{t.focus({preventScroll:!0})}catch(e){}return document.activeElement===t}let $t=[];const Mt=o({name:"FocusTrap",props:{disabled:Boolean,active:Boolean,autoFocus:{type:Boolean,default:!0},onEsc:Function,initialFocusTo:String,finalFocusTo:String,returnFocusOnDeactivated:{type:Boolean,default:!0}},setup(t){const e=M(),n=s(null),o=s(null);let r=!1,l=!1;const i="undefined"==typeof document?null:document.activeElement;function u(){return $t[$t.length-1]===e}function d(e){var n;"Escape"===e.code&&u()&&(null===(n=t.onEsc)||void 0===n||n.call(t,e))}function f(t){if(!l&&u()){const e=c();if(null===e)return;if(e.contains(z(t)))return;v("first")}}function c(){const t=n.value;if(null===t)return null;let e=t;for(;!(e=e.nextSibling,null===e||e instanceof Element&&"DIV"===e.tagName););return e}function h(){var n;if(t.disabled)return;if(document.removeEventListener("focus",f,!0),$t=$t.filter((t=>t!==e)),u())return;const{finalFocusTo:o}=t;void 0!==o?null===(n=K(o))||void 0===n||n.focus({preventScroll:!0}):t.returnFocusOnDeactivated&&i instanceof HTMLElement&&(l=!0,i.focus({preventScroll:!0}),l=!1)}function v(e){if(u()&&t.active){const t=n.value,r=o.value;if(null!==t&&null!==r){const n=c();if(null==n||n===r)return l=!0,t.focus({preventScroll:!0}),void(l=!1);l=!0;const o="first"===e?Rt(n):St(n);l=!1,o||(l=!0,t.focus({preventScroll:!0}),l=!1)}}}return p((()=>{m((()=>t.active),(n=>{n?(!function(){var n;if(t.disabled)return;if($t.push(e),t.autoFocus){const{initialFocusTo:e}=t;void 0===e?v("first"):null===(n=K(e))||void 0===n||n.focus({preventScroll:!0})}r=!0,document.addEventListener("focus",f,!0)}(),R("keydown",document,d)):(x("keydown",document,d),r&&h())}),{immediate:!0})})),a((()=>{x("keydown",document,d),r&&h()})),{focusableStartRef:n,focusableEndRef:o,focusableStyle:"position: absolute; height: 0; width: 0;",handleStartFocus:function(t){if(l)return;const e=c();null!==e&&(null!==t.relatedTarget&&e.contains(t.relatedTarget)?v("last"):v("first"))},handleEndFocus:function(t){l||(null!==t.relatedTarget&&t.relatedTarget===n.value?v("last"):v("first"))}}},render(){const{default:t}=this.$slots;if(void 0===t)return null;if(this.disabled)return t();const{active:n,focusableStyle:o}=this;return d(e,null,[d("div",{"aria-hidden":"true",tabindex:n?"0":"-1",ref:"focusableStartRef",style:o,onFocus:this.handleStartFocus}),t(),d("div",{"aria-hidden":"true",style:o,ref:"focusableEndRef",tabindex:n?"0":"-1",onFocus:this.handleEndFocus})])}});export{P as B,Mt as F,_ as L,at as V,mt as a,it as b,V as c,wt as d,gt as e,st as r};
