import{r as e,U as t,f as n,t as o,g as a,o as i,c as u,j as c,a as l}from"./@vue-C18CzuYV.js";import{o as r,a as s}from"./evtd-hWw0KU7y.js";function f(o){const a=e(!!o.value);if(a.value)return t(a);const i=n(o,(e=>{e&&(a.value=!0,i())}));return t(a)}function d(t){const a=o(t),i=e(a.value);return n(a,(e=>{i.value=e})),"function"==typeof t?i:{__v_isRef:!0,get value(){return i.value},set value(e){t.set(e)}}}function v(){return null!==a()}const w="undefined"!=typeof window;let k,m;var p,y;function h(e){if(m)return;let t=!1;i((()=>{m||null==k||k.then((()=>{t||e()}))})),u((()=>{t=!0}))}k=w?null===(y=null===(p=document)||void 0===p?void 0:p.fonts)||void 0===y?void 0:y.ready:void 0,m=!1,void 0!==k?k.then((()=>{m=!0})):m=!0;const b=e(null);function g(e){if(e.clientX>0||e.clientY>0)b.value={x:e.clientX,y:e.clientY};else{const{target:t}=e;if(t instanceof Element){const{left:e,top:n,width:o,height:a}=t.getBoundingClientRect();b.value=e>0||n>0?{x:e+o/2,y:n+a/2}:{x:0,y:0}}else b.value=null}}let P=0,j=!0;function x(){if(!w)return t(e(null));0===P&&r("click",document,g,!0);const n=()=>{P+=1};return j&&(j=v())?(c(n),u((()=>{P-=1,0===P&&s("click",document,g,!0)}))):n(),t(b)}const T=e(void 0);let M=0;function S(){T.value=Date.now()}let C=!0;function D(n){if(!w)return t(e(!1));const o=e(!1);let a=null;function i(){null!==a&&window.clearTimeout(a)}function l(){i(),o.value=!0,a=window.setTimeout((()=>{o.value=!1}),n)}0===M&&r("click",window,S,!0);const f=()=>{M+=1,r("click",window,l,!0)};return C&&(C=v())?(c(f),u((()=>{M-=1,0===M&&s("click",window,S,!0),s("click",window,l,!0),i()}))):f(),t(o)}function E(e,t){return n(e,(e=>{void 0!==e&&(t.value=e)})),o((()=>void 0===e.value?t.value:e.value))}function _(){const n=e(!1);return i((()=>{n.value=!0})),t(n)}function O(e,t){return o((()=>{for(const n of t)if(void 0!==e[n])return e[n];return e[t[t.length-1]]}))}const R="undefined"!=typeof window&&(/iPad|iPhone|iPod/.test(navigator.platform)||"MacIntel"===navigator.platform&&navigator.maxTouchPoints>1)&&!window.MSStream;function X(){return R}function Y(e={},o){const a=l({ctrl:!1,command:!1,win:!1,shift:!1,tab:!1}),{keydown:i,keyup:f}=e,d=e=>{switch(e.key){case"Control":a.ctrl=!0;break;case"Meta":a.command=!0,a.win=!0;break;case"Shift":a.shift=!0;break;case"Tab":a.tab=!0}void 0!==i&&Object.keys(i).forEach((t=>{if(t!==e.key)return;const n=i[t];if("function"==typeof n)n(e);else{const{stop:t=!1,prevent:o=!1}=n;t&&e.stopPropagation(),o&&e.preventDefault(),n.handler(e)}}))},w=e=>{switch(e.key){case"Control":a.ctrl=!1;break;case"Meta":a.command=!1,a.win=!1;break;case"Shift":a.shift=!1;break;case"Tab":a.tab=!1}void 0!==f&&Object.keys(f).forEach((t=>{if(t!==e.key)return;const n=f[t];if("function"==typeof n)n(e);else{const{stop:t=!1,prevent:o=!1}=n;t&&e.stopPropagation(),o&&e.preventDefault(),n.handler(e)}}))},k=()=>{(void 0===o||o.value)&&(r("keydown",document,d),r("keyup",document,w)),void 0!==o&&n(o,(e=>{e?(r("keydown",document,d),r("keyup",document,w)):(s("keydown",document,d),s("keyup",document,w))}))};return v()?(c(k),u((()=>{(void 0===o||o.value)&&(s("keydown",document,d),s("keyup",document,w))}))):k(),t(a)}export{X as a,E as b,O as c,Y as d,D as e,x as f,f as g,_ as i,h as o,d as u};
