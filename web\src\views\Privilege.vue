<!--
  特权管理页面
  
  功能说明：
  - 显示用户特权列表
  - 提供特权搜索和标签筛选功能
  - 提供特权创建入口
  - 集成用户信息和主题切换功能
  
  主要业务逻辑：
  1. 特权展示：显示特权卡片列表
  2. 搜索功能：支持关键词搜索和标签筛选
  3. 特权管理：提供创建特权的快捷入口
  4. 响应式布局：根据屏幕尺寸调整布局和控件显示
  
  交互特性：
  - 实时搜索结果更新
  - 无限滚动加载更多内容
  - 特权过期时间切换显示
-->
<template>
  <div class="home-layout">
    <!-- 顶部区域 -->
    <div class="home-layout-top">
      <div class="middle-controls-container">
        <!-- 搜索容器 -->
        <SearchBar
          v-model="search.searchCondition.value"
          placeholder="搜索特权内容"
          @search="handleSearch"
        />

        <!-- 创建按钮容器 -->
        <CreatePrivilegeButton @click="privilegeState.openCreatePrivilegeDialog" />
      </div>

      <!-- 创建特权弹框 -->
      <PrivilegeModal
        ref="privilegeModalRef"
        v-model="showPrivilegeModal"
        @success="handlePrivilegeSuccess"
      />

      <!-- 特权验证弹框 -->
      <PrivilegeVerificationModal
        v-model="showVerificationModal"
        :privilege="selectedPrivilege"
        @success="handleVerificationSuccess"
      />

      <UserInfoGroup />
    </div>

    <!-- 标签栏区域 -->
    <div class="tag-bar-wrapper">
      <TagBar
        v-model="search.searchCondition.value.tag"
        @tagSelected="
          (tagName) => search.handleTagSelected(tagName, privilegeState.privilegeListRef.value)
        "
      />
    </div>

    <!-- 主内容区域：显示特权列表 -->
    <div class="home-layout-content">
      <PrivilegeList
        :search-condition="search.searchCondition"
        ref="privilegeListRef"
        @reset="privilegeState.resetPrivilegeList"
        @start-verification="handleStartVerification"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, nextTick, defineAsyncComponent, ref, watch } from 'vue'

// 导入组件
import SearchBar from '@/components/home/<USER>'
import TagBar from '@/components/home/<USER>'
import CreatePrivilegeButton from '@/components/privilege/CreatePrivilegeButton.vue'
import PrivilegeList from '@/components/privilege/PrivilegeList.vue'
import UserInfoGroup from '@/components/user/UserInfoGroup.vue'
import PrivilegeVerificationModal from '@/components/verification/PrivilegeVerificationModal.vue'
import { usePrivilegeSearch } from '@/composables/privilege/usePrivilegeSearch'
import { usePrivilegeState } from '@/composables/privilege/usePrivilegeState'
import { useVerificationState } from '@/composables/verification/useVerificationState'
import type { PrivilegeListRef } from '@/types/component/privilege-list-ref.types'
import type { PrivilegeModalExpose } from '@/types/component/privilege-modal-expose.types'
import type { PrivilegePageEmits } from '@/types/component/privilege-page-emits.types'
import type { PrivilegePageExpose } from '@/types/component/privilege-page-expose.types'
import type { PrivilegePageProps } from '@/types/component/privilege-page-props.types'
import type { PrivilegeResponse } from '@/types/privilege/privilege-response.types'
import type { VerificationResponse } from '@/types/verification/verification-response.types'
import logger from '@/utils/log/log'

// 导入异步组件
const PrivilegeModal = defineAsyncComponent(
  () => import('@/components/privilege/PrivilegeModal.vue'),
)

// 定义组件 Props
const props = withDefaults(defineProps<PrivilegePageProps>(), {
  initialSearchCondition: undefined,
  autoLoad: true,
})

// 定义组件 Emits
const emit = defineEmits<PrivilegePageEmits>()

// 使用 composables 进行状态管理
const search = usePrivilegeSearch()
const privilegeState = usePrivilegeState()
const verificationState = useVerificationState()

// 定义组件引用
const privilegeModalRef = ref<PrivilegeModalExpose | null>(null)
const privilegeListRef = ref<PrivilegeListRef | null>(null)

// 弹框显示状态
const showPrivilegeModal = ref(false)
const showVerificationModal = ref(false)

// 验证相关状态
const selectedPrivilege = ref<PrivilegeResponse | null>(null)

// 监听组件引用变化，同步到privilegeState
watch(
  privilegeModalRef,
  (newVal) => {
    privilegeState.privilegeModalRef.value = newVal
  },
  { immediate: true },
)

watch(
  privilegeListRef,
  (newVal) => {
    privilegeState.privilegeListRef.value = newVal
  },
  { immediate: true },
)

/**
 * 组件挂载时的初始化逻辑
 */
onMounted(async (): Promise<void> => {
  // 初始化特权页面状态
  privilegeState.initializeState()

  // 加载保存的搜索条件或使用传入的初始搜索条件
  if (props.initialSearchCondition) {
    search.searchCondition.value = {
      ...search.searchCondition.value,
      ...props.initialSearchCondition,
    }
    search.saveSearchCondition()
  } else {
    search.loadSearchCondition()
  }

  // 等待DOM渲染完成后再进行搜索
  await nextTick()

  // 如果启用自动加载，则执行搜索
  if (props.autoLoad) {
    setTimeout(async () => {
      await handleSearch()
    }, 50)
  }
})

/**
 * 组件卸载时的清理逻辑
 */
onUnmounted((): void => {
  // 清理搜索相关资源
  search.cleanup()

  // 清理状态相关资源
  privilegeState.cleanup()
})

/**
 * 处理搜索操作
 */
const handleSearch = async (loadMore: boolean = false): Promise<void> => {
  logger.debug('执行特权搜索操作:', {
    privilegeListRef: privilegeState.privilegeListRef.value,
    loadMore,
  })

  // 等待DOM更新完成
  await nextTick()

  // 执行搜索并触发搜索条件变化事件
  search.search(privilegeState.privilegeListRef.value, loadMore)

  // 触发搜索条件变化事件
  emit('search-condition-change', search.searchCondition.value)
}

/**
 * 处理特权创建成功
 */
const handlePrivilegeSuccess = async (): Promise<void> => {
  privilegeState.resetPrivilegeList()
  await handleSearch()

  // 触发特权创建成功事件
  emit('privilege-create-success')
}

/**
 * 执行搜索操作
 * 暴露给父组件的方法
 */
const performSearch = async (loadMore: boolean = false): Promise<void> => {
  await handleSearch(loadMore)
}

/**
 * 重置特权列表
 * 暴露给父组件的方法
 */
const resetPrivilegeList = (): void => {
  privilegeState.resetPrivilegeList()
}

/**
 * 获取当前搜索条件
 * 暴露给父组件的方法
 */
const getCurrentSearchCondition = () => {
  return search.searchCondition.value
}

// 暴露组件方法给父组件
defineExpose<PrivilegePageExpose>({
  performSearch,
  resetPrivilegeList,
  getCurrentSearchCondition,
})
</script>

<style lang="scss" scoped>
@use '@/styles/home';
</style>
