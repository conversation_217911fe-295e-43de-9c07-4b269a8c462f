// 特权列表样式
.privilege-list-container {
  height: 100%;
  overflow-y: auto;

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    gap: 16px;

    .loading-text {
      color: var(--text-color-2);
      font-size: 14px;
    }
  }

  .privilege-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
    padding-bottom: 20px;

    @media (width <= 768px) {
      grid-template-columns: 1fr;
      gap: 15px;
    }

    .privilege-card {
      border-radius: 0.5rem;
      margin-top: 1.25rem;
      box-sizing: border-box;
      max-width: 100vw;

      &:hover {
        transform: translateY(-0.6rem);
        box-shadow: var(--shadow);
      }

      .privilege-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        overflow: hidden;

        .privilege-icon {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .privilege-description {
        color: var(--text-color-2);
        font-size: 14px;
        line-height: 1.5;
        padding: 0.5rem 0;
      }

      .privilege-expire-time {
        .time-clickable {
          cursor: pointer;
          transition: opacity 0.2s ease;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 300px;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      margin-bottom: 16px;
    }

    .empty-text {
      font-size: 18px;
      color: var(--text-color-1);
      margin-bottom: 8px;
    }

    .empty-hint {
      font-size: 14px;
      color: var(--text-color-3);
    }
  }

  .loading-more {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 20px;
    color: var(--text-color-2);
    font-size: 14px;
  }

  .load-more-trigger {
    height: 1px;
  }
}
