import{r as o}from"./compute-scroll-into-view-Cfyw3hb3.js";function t(t,e){if(!t.isConnected||!(o=>{let t=o;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(t))return;const r=(o=>{const t=window.getComputedStyle(o);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(t);if("object"==typeof(n=e)&&"function"==typeof n.behavior)return e.behavior(o(t,e));var n;const l="boolean"==typeof e||null==e?void 0:e.behavior;for(const{el:a,top:i,left:s}of o(t,(o=>{return!1===o?{block:"end",inline:"nearest"}:(t=o)===Object(t)&&0!==Object.keys(t).length?o:{block:"start",inline:"nearest"};var t})(e))){const o=i-r.top+r.bottom,t=s-r.left+r.right;a.scroll({top:o,left:t,behavior:l})}}export{t as e};
