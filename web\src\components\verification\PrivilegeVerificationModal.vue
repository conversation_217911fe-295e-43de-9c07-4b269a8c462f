<!--
  特权验证弹框组件
  
  功能说明：
  - 显示特权验证的三步流程
  - 实时更新验证状态
  - 提供验证申请和状态查询功能
-->
<template>
  <NModal v-model:show="visible" preset="dialog" title="特权验证" :mask-closable="false" style="width: 600px">
    <div class="verification-modal">
      <!-- 特权信息展示 -->
      <div class="privilege-info">
        <NAvatar :src="privilege?.icon || '/default-privilege-icon.png'" :size="48" />
        <div class="privilege-details">
          <h3 class="privilege-name">{{ privilege?.name }}</h3>
          <NTag
            :type="privilege?.verificationType === 0 ? 'info' : 'success'"
            size="small"
            class="verification-type-tag"
          >
            {{ getVerificationTypeLabel(privilege?.verificationType) }}
          </NTag>
        </div>
      </div>

      <!-- 验证流程步骤 -->
      <div v-if="verificationData" class="verification-steps">
        <NSteps :current="verificationData.currentStep - 1" size="small">
          <NStep
            v-for="(step, index) in verificationSteps"
            :key="index"
            :title="step.title"
            :description="step.description"
          />
        </NSteps>
      </div>

      <!-- 验证状态信息 -->
      <div v-if="verificationData" class="verification-status">
        <NAlert :type="getStatusAlertType(verificationData.status)" class="status-alert">
          <template #header>
            <span class="status-title">{{ getStatusTitle(verificationData.status) }}</span>
          </template>
          {{ getStatusDescription(verificationData.status) }}
        </NAlert>

        <!-- 验证详细信息 -->
        <div class="verification-details">
          <div class="detail-item">
            <span class="detail-label">验证ID：</span>
            <span class="detail-value">{{ verificationData.id }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">创建时间：</span>
            <span class="detail-value">{{ formatTime(verificationData.ctTm) }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">过期时间：</span>
            <span class="detail-value">{{ formatTime(verificationData.expireTime) }}</span>
          </div>
        </div>

        <!-- 开发模式下的验证页面链接 -->
        <div v-if="verificationData.pageUrl && isDev" class="dev-tools">
          <NDivider />
          <div class="dev-link">
            <NButton text type="primary" @click="openVerificationPage">
              🔗 打开验证页面（开发模式）
            </NButton>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-actions">
        <NButton
          v-if="!verificationData"
          type="primary"
          @click="startVerification"
          :loading="isStarting"
          :disabled="!privilege"
        >
          发起验证申请
        </NButton>
        <NButton v-else @click="refreshStatus" :loading="isRefreshing" type="info">
          刷新状态
        </NButton>
        <NButton @click="closeModal">关闭</NButton>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { useMessage } from 'naive-ui'
import verificationApi from '@/api/verification'
import { useVerificationPolling } from '@/composables/verification/useVerificationPolling'
import { useVerificationNotification } from '@/composables/verification/useVerificationNotification'
import type { PrivilegeResponse } from '@/types/privilege/privilege-response.types'
import type { VerificationResponse } from '@/types/verification/verification-response.types'
import {
  VERIFICATION_STATUS,
  VERIFICATION_STATUS_LABELS,
  VERIFICATION_STEP_LABELS,
  VERIFICATION_STEP_DESCRIPTIONS
} from '@/constants/verification/verification-status.constants'
import { VERIFICATION_TYPE_LABELS } from '@/constants/privilege/verification-type.constants'
import { formatExactTime } from '@/utils/date/format'

// 组件属性定义
const props = defineProps<{
  modelValue: boolean
  privilege: PrivilegeResponse | null
}>()

// 组件事件定义
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [verification: VerificationResponse]
}>()

// 响应式状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const verificationData = ref<VerificationResponse | null>(null)
const isStarting = ref(false)
const isRefreshing = ref(false)
const isDev = process.env.NODE_ENV === 'development'

// 使用组合式函数
const message = useMessage()
const polling = useVerificationPolling()
const notification = useVerificationNotification()

// 验证步骤配置
const verificationSteps = [
  {
    title: VERIFICATION_STEP_LABELS[1],
    description: VERIFICATION_STEP_DESCRIPTIONS[1]
  },
  {
    title: VERIFICATION_STEP_LABELS[2],
    description: VERIFICATION_STEP_DESCRIPTIONS[2]
  },
  {
    title: VERIFICATION_STEP_LABELS[3],
    description: VERIFICATION_STEP_DESCRIPTIONS[3]
  }
]

/**
 * 启动验证流程
 */
const startVerification = async (): Promise<void> => {
  if (!props.privilege) return

  isStarting.value = true
  try {
    const response = await verificationApi.startVerification({
      privilegeId: props.privilege.id,
      verificationType: props.privilege.verificationType
    })

    if (response.success && response.data) {
      verificationData.value = response.data
      notification.notifyVerificationStart(props.privilege.name)

      // 开始轮询状态
      startStatusPolling()

      emit('success', response.data)
    } else {
      notification.notifyVerificationError('发起验证申请失败')
    }
  } catch (error) {
    console.error('启动验证失败:', error)
    notification.notifyVerificationError('发起验证申请失败，请稍后重试')
  } finally {
    isStarting.value = false
  }
}

/**
 * 刷新验证状态
 */
const refreshStatus = async (): Promise<void> => {
  if (!verificationData.value) return

  isRefreshing.value = true
  try {
    const response = await verificationApi.getVerificationStatus(verificationData.value.id)
    if (response.success && response.data) {
      const oldStatus = verificationData.value.status
      verificationData.value = response.data

      // 通知状态变化
      if (props.privilege) {
        notification.notifyStatusChange(oldStatus, response.data.status, props.privilege.name)
      }
    }
  } catch (error) {
    console.error('刷新状态失败:', error)
    notification.notifyVerificationError('刷新状态失败')
  } finally {
    isRefreshing.value = false
  }
}

/**
 * 开始状态轮询
 */
const startStatusPolling = (): void => {
  if (!verificationData.value || !props.privilege) return

  polling.startPolling(verificationData.value.id, (updatedVerification) => {
    const oldStatus = verificationData.value?.status
    verificationData.value = updatedVerification

    // 通知状态变化
    if (oldStatus !== undefined && props.privilege) {
      notification.notifyStatusChange(oldStatus, updatedVerification.status, props.privilege.name)
    }
  })
}

/**
 * 停止状态轮询
 */
const stopStatusPolling = (): void => {
  if (verificationData.value) {
    polling.stopPolling(verificationData.value.id)
  }
}

/**
 * 工具函数：获取验证类型标签
 */
const getVerificationTypeLabel = (type: number | undefined): string => {
  if (type === undefined) return '未知类型'
  return VERIFICATION_TYPE_LABELS[type] || '未知类型'
}

/**
 * 工具函数：获取状态警告框类型
 */
const getStatusAlertType = (status: number): 'info' | 'success' | 'warning' | 'error' => {
  switch (status) {
    case VERIFICATION_STATUS.IN_PROGRESS:
      return 'info'
    case VERIFICATION_STATUS.SUCCESS:
      return 'success'
    case VERIFICATION_STATUS.FAILED:
      return 'error'
    case VERIFICATION_STATUS.TIMEOUT:
      return 'warning'
    default:
      return 'info'
  }
}

/**
 * 工具函数：获取状态标题
 */
const getStatusTitle = (status: number): string => {
  return VERIFICATION_STATUS_LABELS[status] || '未知状态'
}

/**
 * 工具函数：获取状态描述
 */
const getStatusDescription = (status: number): string => {
  switch (status) {
    case VERIFICATION_STATUS.IN_PROGRESS:
      return '验证申请已发送，请等待特权提供者确认。邮件已发送至特权提供者邮箱。'
    case VERIFICATION_STATUS.SUCCESS:
      return '验证已成功完成！您现在可以使用该特权功能。'
    case VERIFICATION_STATUS.FAILED:
      return '验证失败，可能是验证内容不正确或其他原因导致。'
    case VERIFICATION_STATUS.TIMEOUT:
      return '验证超时，特权提供者未在规定时间内完成验证。'
    default:
      return '未知状态'
  }
}

/**
 * 工具函数：格式化时间
 */
const formatTime = (timestamp: number): string => {
  return formatExactTime(timestamp)
}

/**
 * 打开验证页面（开发模式）
 */
const openVerificationPage = (): void => {
  if (verificationData.value?.pageUrl) {
    window.open(verificationData.value.pageUrl, '_blank')
  }
}

/**
 * 关闭弹框
 */
const closeModal = (): void => {
  visible.value = false
  stopStatusPolling()
  verificationData.value = null
}

// 组件卸载时清理资源
onUnmounted(() => {
  stopStatusPolling()
})
</script>

<style lang="scss" scoped>
.verification-modal {
  .privilege-info {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 16px;
    background: var(--n-color-target);
    border-radius: 8px;
    margin-bottom: 24px;

    .privilege-details {
      flex: 1;

      .privilege-name {
        margin: 0 0 8px 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--n-text-color);
      }

      .verification-type-tag {
        font-size: 12px;
      }
    }
  }

  .verification-steps {
    margin-bottom: 24px;
  }

  .verification-status {
    .status-alert {
      margin-bottom: 16px;

      .status-title {
        font-weight: 600;
      }
    }

    .verification-details {
      background: var(--n-color-target);
      border-radius: 6px;
      padding: 12px;
      margin-bottom: 16px;

      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 4px 0;

        &:not(:last-child) {
          border-bottom: 1px solid var(--n-divider-color);
        }

        .detail-label {
          font-size: 14px;
          color: var(--n-text-color-2);
        }

        .detail-value {
          font-size: 14px;
          color: var(--n-text-color);
          font-weight: 500;
        }
      }
    }

    .dev-tools {
      .dev-link {
        text-align: center;
        padding: 8px;
        background: var(--n-warning-color-suppl);
        border-radius: 4px;
        border: 1px dashed var(--n-warning-color);
      }
    }
  }

  .modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid var(--n-divider-color);
  }
}
</style>
