import{g as e,f as t}from"./date-fns-B2WPOLoy.js";function n(t,n,r){const i=e(),a=function(e,t,n){return new Intl.DateTimeFormat(n?[n.code,"en-US"]:void 0,{timeZone:t,timeZoneName:e})}(t,r.timeZone,r.locale??i.locale);return"formatToParts"in a?function(e,t){const n=e.formatToParts(t);for(let r=n.length-1;r>=0;--r)if("timeZoneName"===n[r].type)return n[r].value;return}(a,n):function(e,t){const n=e.format(t).replace(/\u200E/g,""),r=/ [\w-+ ]+$/.exec(n);return r?r[0].substr(1):""}(a,n)}function r(e,t){const n=function(e){a[e]||(a[e]=u?new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:e,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}):new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"numeric",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}));return a[e]}(t);return"formatToParts"in n?function(e,t){try{const n=e.formatToParts(t),r=[];for(let e=0;e<n.length;e++){const t=i[n[e].type];void 0!==t&&(r[t]=parseInt(n[e].value,10))}return r}catch(n){if(n instanceof RangeError)return[NaN];throw n}}(n,e):function(e,t){const n=e.format(t),r=/(\d+)\/(\d+)\/(\d+),? (\d+):(\d+):(\d+)/.exec(n);return[parseInt(r[3],10),parseInt(r[1],10),parseInt(r[2],10),parseInt(r[4],10),parseInt(r[5],10),parseInt(r[6],10)]}(n,e)}const i={year:0,month:1,day:2,hour:3,minute:4,second:5};const a={},o=new Intl.DateTimeFormat("en-US",{hourCycle:"h23",timeZone:"America/New_York",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(new Date("2014-06-25T04:00:00.123Z")),u="06/25/2014, 00:00:00"===o||"‎06‎/‎25‎/‎2014‎ ‎00‎:‎00‎:‎00"===o;function c(e,t,n,r,i,a,o){const u=new Date(0);return u.setUTCFullYear(e,t,n),u.setUTCHours(r,i,a,o),u}const s=36e5,l={timezoneZ:/^(Z)$/,timezoneHH:/^([+-]\d{2})$/,timezoneHHMM:/^([+-])(\d{2}):?(\d{2})$/};function f(e,t,n){if(!e)return 0;let r,i,a=l.timezoneZ.exec(e);if(a)return 0;if(a=l.timezoneHH.exec(e),a)return r=parseInt(a[1],10),m(r)?-r*s:NaN;if(a=l.timezoneHHMM.exec(e),a){r=parseInt(a[2],10);const e=parseInt(a[3],10);return m(r,e)?(i=Math.abs(r)*s+6e4*e,"+"===a[1]?-i:i):NaN}if(function(e){if(g[e])return!0;try{return new Intl.DateTimeFormat(void 0,{timeZone:e}),g[e]=!0,!0}catch(t){return!1}}(e)){t=new Date(t||Date.now());const r=n?t:function(e){return c(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds())}(t),i=d(r,e),a=n?i:function(e,t,n){const r=e.getTime();let i=r-t;const a=d(new Date(i),n);if(t===a)return t;i-=a-t;const o=d(new Date(i),n);if(a===o)return a;return Math.max(a,o)}(t,i,e);return-a}return NaN}function d(e,t){const n=r(e,t),i=c(n[0],n[1]-1,n[2],n[3]%24,n[4],n[5],0).getTime();let a=e.getTime();const o=a%1e3;return a-=o>=0?o:1e3+o,i-a}function m(e,t){return-23<=e&&e<=23&&(null==t||0<=t&&t<=59)}const g={};const D={X:function(e,t,n){const r=w(n.timeZone,e);if(0===r)return"Z";switch(t){case"X":return h(r);case"XXXX":case"XX":return p(r);default:return p(r,":")}},x:function(e,t,n){const r=w(n.timeZone,e);switch(t){case"x":return h(r);case"xxxx":case"xx":return p(r);default:return p(r,":")}},O:function(e,t,n){const r=w(n.timeZone,e);switch(t){case"O":case"OO":case"OOO":return"GMT"+function(e,t=""){const n=e>0?"-":"+",r=Math.abs(e),i=Math.floor(r/60),a=r%60;if(0===a)return n+String(i);return n+String(i)+t+N(a,2)}(r,":");default:return"GMT"+p(r,":")}},z:function(e,t,r){switch(t){case"z":case"zz":case"zzz":return n("short",e,r);default:return n("long",e,r)}}};function w(e,t){const n=e?f(e,t,!0)/6e4:(null==t?void 0:t.getTimezoneOffset())??0;if(Number.isNaN(n))throw new RangeError("Invalid time zone specified: "+e);return n}function N(e,t){const n=e<0?"-":"";let r=Math.abs(e).toString();for(;r.length<t;)r="0"+r;return n+r}function p(e,t=""){const n=e>0?"-":"+",r=Math.abs(e);return n+N(Math.floor(r/60),2)+t+N(Math.floor(r%60),2)}function h(e,t){if(e%60==0){return(e>0?"-":"+")+N(Math.abs(e)/60,2)}return p(e,t)}function T(e){const t=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return t.setUTCFullYear(e.getFullYear()),+e-+t}const Y=36e5,M=6e4,x={dateTimePattern:/^([0-9W+-]+)(T| )(.*)/,datePattern:/^([0-9W+-]+)(.*)/,YY:/^(\d{2})$/,YYY:[/^([+-]\d{2})$/,/^([+-]\d{3})$/,/^([+-]\d{4})$/],YYYY:/^(\d{4})/,YYYYY:[/^([+-]\d{4})/,/^([+-]\d{5})/,/^([+-]\d{6})/],MM:/^-(\d{2})$/,DDD:/^-?(\d{3})$/,MMDD:/^-?(\d{2})-?(\d{2})$/,Www:/^-?W(\d{2})$/,WwwD:/^-?W(\d{2})-?(\d{1})$/,HH:/^(\d{2}([.,]\d*)?)$/,HHMM:/^(\d{2}):?(\d{2}([.,]\d*)?)$/,HHMMSS:/^(\d{2}):?(\d{2}):?(\d{2}([.,]\d*)?)$/,timeZone:/(Z|[+-]\d{2}(?::?\d{2})?| UTC| [a-zA-Z]+\/[a-zA-Z_]+(?:\/[a-zA-Z_]+)?)$/};function Z(e,t={}){if(arguments.length<1)throw new TypeError("1 argument required, but only "+arguments.length+" present");if(null===e)return new Date(NaN);const n=null==t.additionalDigits?2:Number(t.additionalDigits);if(2!==n&&1!==n&&0!==n)throw new RangeError("additionalDigits must be 0, 1 or 2");if(e instanceof Date||"object"==typeof e&&"[object Date]"===Object.prototype.toString.call(e))return new Date(e.getTime());if("number"==typeof e||"[object Number]"===Object.prototype.toString.call(e))return new Date(e);if("[object String]"!==Object.prototype.toString.call(e))return new Date(NaN);const r=function(e){const t={};let n,r=x.dateTimePattern.exec(e);r?(t.date=r[1],n=r[3]):(r=x.datePattern.exec(e),r?(t.date=r[1],n=r[2]):(t.date=null,n=e));if(n){const e=x.timeZone.exec(n);e?(t.time=n.replace(e[1],""),t.timeZone=e[1].trim()):t.time=n}return t}(e),{year:i,restDateString:a}=function(e,t){if(e){const n=x.YYY[t],r=x.YYYYY[t];let i=x.YYYY.exec(e)||r.exec(e);if(i){const t=i[1];return{year:parseInt(t,10),restDateString:e.slice(t.length)}}if(i=x.YY.exec(e)||n.exec(e),i){const t=i[1];return{year:100*parseInt(t,10),restDateString:e.slice(t.length)}}}return{year:null}}(r.date,n),o=function(e,t){if(null===t)return null;let n,r,i;if(!e||!e.length)return n=new Date(0),n.setUTCFullYear(t),n;let a=x.MM.exec(e);if(a)return n=new Date(0),r=parseInt(a[1],10)-1,C(t,r)?(n.setUTCFullYear(t,r),n):new Date(NaN);if(a=x.DDD.exec(e),a){n=new Date(0);const e=parseInt(a[1],10);return function(e,t){if(t<1)return!1;const n=U(e);if(n&&t>366)return!1;if(!n&&t>365)return!1;return!0}(t,e)?(n.setUTCFullYear(t,0,e),n):new Date(NaN)}if(a=x.MMDD.exec(e),a){n=new Date(0),r=parseInt(a[1],10)-1;const e=parseInt(a[2],10);return C(t,r,e)?(n.setUTCFullYear(t,r,e),n):new Date(NaN)}if(a=x.Www.exec(e),a)return i=parseInt(a[1],10)-1,S(i)?I(t,i):new Date(NaN);if(a=x.WwwD.exec(e),a){i=parseInt(a[1],10)-1;const e=parseInt(a[2],10)-1;return S(i,e)?I(t,i,e):new Date(NaN)}return null}(a,i);if(null===o||isNaN(o.getTime()))return new Date(NaN);if(o){const e=o.getTime();let n,i=0;if(r.time&&(i=function(e){let t,n,r=x.HH.exec(e);if(r)return t=parseFloat(r[1].replace(",",".")),b(t)?t%24*Y:NaN;if(r=x.HHMM.exec(e),r)return t=parseInt(r[1],10),n=parseFloat(r[2].replace(",",".")),b(t,n)?t%24*Y+n*M:NaN;if(r=x.HHMMSS.exec(e),r){t=parseInt(r[1],10),n=parseInt(r[2],10);const e=parseFloat(r[3].replace(",","."));return b(t,n,e)?t%24*Y+n*M+1e3*e:NaN}return null}(r.time),null===i||isNaN(i)))return new Date(NaN);if(r.timeZone||t.timeZone){if(n=f(r.timeZone||t.timeZone,new Date(e+i)),isNaN(n))return new Date(NaN)}else n=T(new Date(e+i)),n=T(new Date(e+i+n));return new Date(e+i+n)}return new Date(NaN)}function I(e,t,n){t=t||0,n=n||0;const r=new Date(0);r.setUTCFullYear(e,0,4);const i=7*t+n+1-(r.getUTCDay()||7);return r.setUTCDate(r.getUTCDate()+i),r}const y=[31,28,31,30,31,30,31,31,30,31,30,31],H=[31,29,31,30,31,30,31,31,30,31,30,31];function U(e){return e%400==0||e%4==0&&e%100!=0}function C(e,t,n){if(t<0||t>11)return!1;if(null!=n){if(n<1)return!1;const r=U(e);if(r&&n>H[t])return!1;if(!r&&n>y[t])return!1}return!0}function S(e,t){return!(e<0||e>52)&&(null==t||!(t<0||t>6))}function b(e,t,n){return!(e<0||e>=25)&&((null==t||!(t<0||t>=60))&&(null==n||!(n<0||n>=60)))}const F=/([xXOz]+)|''|'(''|[^'])+('|$)/g;function z(e,n,r,i){return function(e,n,r={}){const i=(n=String(n)).match(F);if(i){const t=Z(r.originalDate||e,r);n=i.reduce((function(e,n){if("'"===n[0])return e;const i=e.indexOf(n),a="'"===e[i-1],o=e.replace(n,"'"+D[n[0]](t,n,r)+"'");return a?o.substring(0,i-1)+o.substring(i+1):o}),n)}return t(e,n,r)}(function(e,t,n){const r=f(t,e=Z(e,n),!0),i=new Date(e.getTime()-r),a=new Date(0);return a.setFullYear(i.getUTCFullYear(),i.getUTCMonth(),i.getUTCDate()),a.setHours(i.getUTCHours(),i.getUTCMinutes(),i.getUTCSeconds(),i.getUTCMilliseconds()),a}(e,n,{timeZone:(i={...i,timeZone:n,originalDate:e}).timeZone}),r,i)}export{z as f};
