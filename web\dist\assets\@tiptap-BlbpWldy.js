import{d as t}from"./prosemirror-dropcursor-b1zulL1f.js";import{N as e,P as n,a as o,S as s,T as r,A as i,E as a}from"./prosemirror-state-Dg8eoqF5.js";import{E as l,D as d,a as c}from"./prosemirror-view-D1p6KQzm.js";import{g as u}from"./prosemirror-gapcursor-De4VkbqI.js";import{h as p,r as h,u as m}from"./prosemirror-history-B0l72feN.js";import{f,r as g,t as y,a as v}from"./linkifyjs-CgqKPF1I.js";import{t as b}from"./tippy.js-Cq-jft6S.js";import{r as k,d as w,p as M,m as x,a as T,h as S,b as E,o as A,c as O,w as L,n as C,u as H,g as P,e as $}from"./@vue-C18CzuYV.js";import{k as N}from"./prosemirror-keymap-CxzETJ23.js";import{D as _,F as I,S as j,N as D,a as R,b as B}from"./prosemirror-model-BwtArlLQ.js";import{f as z,c as q,T as K,a as U,j as V,l as W,R as F,b as Q}from"./prosemirror-transform-2YHSeD6B.js";import{w as G,s as J,a as X,b as Z,c as Y,d as tt,e as et,n as nt,l as ot,f as st,j as rt,g as it,h as at,i as lt,k as dt,m as ct,o as ut,p as pt,q as ht}from"./prosemirror-commands-jNaZT4ky.js";import{w as mt,s as ft,l as gt}from"./prosemirror-schema-list-DJ0wlwD0.js";function yt(t){const{state:e,transaction:n}=t;let{selection:o}=n,{doc:s}=n,{storedMarks:r}=n;return{...e,apply:e.apply.bind(e),applyTransaction:e.applyTransaction.bind(e),plugins:e.plugins,schema:e.schema,reconfigure:e.reconfigure.bind(e),toJSON:e.toJSON.bind(e),get storedMarks(){return r},get selection(){return o},get doc(){return s},get tr(){return o=n.selection,s=n.doc,r=n.storedMarks,n}}}class vt{constructor(t){this.editor=t.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=t.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){const{rawCommands:t,editor:e,state:n}=this,{view:o}=e,{tr:s}=n,r=this.buildProps(s);return Object.fromEntries(Object.entries(t).map((([t,e])=>[t,(...t)=>{const n=e(...t)(r);return s.getMeta("preventDispatch")||this.hasCustomState||o.dispatch(s),n}])))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(t,e=!0){const{rawCommands:n,editor:o,state:s}=this,{view:r}=o,i=[],a=!!t,l=t||s.tr,d={...Object.fromEntries(Object.entries(n).map((([t,n])=>[t,(...t)=>{const o=this.buildProps(l,e),s=n(...t)(o);return i.push(s),d}]))),run:()=>(a||!e||l.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(l),i.every((t=>!0===t)))};return d}createCan(t){const{rawCommands:e,state:n}=this,o=!1,s=t||n.tr,r=this.buildProps(s,o);return{...Object.fromEntries(Object.entries(e).map((([t,e])=>[t,(...t)=>e(...t)({...r,dispatch:void 0})]))),chain:()=>this.createChain(s,o)}}buildProps(t,e=!0){const{rawCommands:n,editor:o,state:s}=this,{view:r}=o,i={tr:t,editor:o,view:r,state:yt({state:s,transaction:t}),dispatch:e?()=>{}:void 0,chain:()=>this.createChain(t,e),can:()=>this.createCan(t),get commands(){return Object.fromEntries(Object.entries(n).map((([t,e])=>[t,(...t)=>e(...t)(i)])))}};return i}}class bt{constructor(){this.callbacks={}}on(t,e){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(e),this}emit(t,...e){const n=this.callbacks[t];return n&&n.forEach((t=>t.apply(this,e))),this}off(t,e){const n=this.callbacks[t];return n&&(e?this.callbacks[t]=n.filter((t=>t!==e)):delete this.callbacks[t]),this}once(t,e){const n=(...o)=>{this.off(t,n),e.apply(this,o)};return this.on(t,n)}removeAllListeners(){this.callbacks={}}}function kt(t,e,n){if(void 0===t.config[e]&&t.parent)return kt(t.parent,e,n);if("function"==typeof t.config[e]){return t.config[e].bind({...n,parent:t.parent?kt(t.parent,e,n):null})}return t.config[e]}function wt(t){return{baseExtensions:t.filter((t=>"extension"===t.type)),nodeExtensions:t.filter((t=>"node"===t.type)),markExtensions:t.filter((t=>"mark"===t.type))}}function Mt(t){const e=[],{nodeExtensions:n,markExtensions:o}=wt(t),s=[...n,...o],r={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return t.forEach((t=>{const n=kt(t,"addGlobalAttributes",{name:t.name,options:t.options,storage:t.storage,extensions:s});if(!n)return;n().forEach((t=>{t.types.forEach((n=>{Object.entries(t.attributes).forEach((([t,o])=>{e.push({type:n,name:t,attribute:{...r,...o}})}))}))}))})),s.forEach((t=>{const n={name:t.name,options:t.options,storage:t.storage},o=kt(t,"addAttributes",n);if(!o)return;const s=o();Object.entries(s).forEach((([n,o])=>{const s={...r,...o};"function"==typeof(null==s?void 0:s.default)&&(s.default=s.default()),(null==s?void 0:s.isRequired)&&void 0===(null==s?void 0:s.default)&&delete s.default,e.push({type:t.name,name:n,attribute:s})}))})),e}function xt(t,e){if("string"==typeof t){if(!e.nodes[t])throw Error(`There is no node type named '${t}'. Maybe you forgot to add the extension?`);return e.nodes[t]}return t}function Tt(...t){return t.filter((t=>!!t)).reduce(((t,e)=>{const n={...t};return Object.entries(e).forEach((([t,e])=>{if(n[t])if("class"===t){const o=e?String(e).split(" "):[],s=n[t]?n[t].split(" "):[],r=o.filter((t=>!s.includes(t)));n[t]=[...s,...r].join(" ")}else if("style"===t){const o=e?e.split(";").map((t=>t.trim())).filter(Boolean):[],s=n[t]?n[t].split(";").map((t=>t.trim())).filter(Boolean):[],r=new Map;s.forEach((t=>{const[e,n]=t.split(":").map((t=>t.trim()));r.set(e,n)})),o.forEach((t=>{const[e,n]=t.split(":").map((t=>t.trim()));r.set(e,n)})),n[t]=Array.from(r.entries()).map((([t,e])=>`${t}: ${e}`)).join("; ")}else n[t]=e;else n[t]=e})),n}),{})}function St(t,e){return e.filter((e=>e.type===t.type.name)).filter((t=>t.attribute.rendered)).map((e=>e.attribute.renderHTML?e.attribute.renderHTML(t.attrs)||{}:{[e.name]:t.attrs[e.name]})).reduce(((t,e)=>Tt(t,e)),{})}function Et(t){return"function"==typeof t}function At(t,e=void 0,...n){return Et(t)?e?t.bind(e)(...n):t(...n):t}function Ot(t,e){return"style"in t?t:{...t,getAttrs:n=>{const o=t.getAttrs?t.getAttrs(n):t.attrs;if(!1===o)return!1;const s=e.reduce(((t,e)=>{const o=e.attribute.parseHTML?e.attribute.parseHTML(n):function(t){return"string"!=typeof t?t:t.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(t):"true"===t||"false"!==t&&t}(n.getAttribute(e.name));return null==o?t:{...t,[e.name]:o}}),{});return{...o,...s}}}}function Lt(t){return Object.fromEntries(Object.entries(t).filter((([t,e])=>("attrs"!==t||!function(t={}){return 0===Object.keys(t).length&&t.constructor===Object}(e))&&null!=e)))}function Ct(t,e){return e.nodes[t]||e.marks[t]||null}function Ht(t,e){return Array.isArray(e)?e.some((e=>("string"==typeof e?e:e.name)===t.name)):e}function Pt(t,e){const n=_.fromSchema(e).serializeFragment(t),o=document.implementation.createHTMLDocument().createElement("div");return o.appendChild(n),o.innerHTML}function $t(t){return"[object RegExp]"===Object.prototype.toString.call(t)}class Nt{constructor(t){this.find=t.find,this.handler=t.handler}}function _t(t){var e;const{editor:n,from:o,to:s,text:r,rules:i,plugin:a}=t,{view:l}=n;if(l.composing)return!1;const d=l.state.doc.resolve(o);if(d.parent.type.spec.code||(null===(e=d.nodeBefore||d.nodeAfter)||void 0===e?void 0:e.marks.find((t=>t.type.spec.code))))return!1;let c=!1;const u=((t,e=500)=>{let n="";const o=t.parentOffset;return t.parent.nodesBetween(Math.max(0,o-e),o,((t,e,s,r)=>{var i,a;const l=(null===(a=(i=t.type.spec).toText)||void 0===a?void 0:a.call(i,{node:t,pos:e,parent:s,index:r}))||t.textContent||"%leaf%";n+=t.isAtom&&!t.isText?l:l.slice(0,Math.max(0,o-e))})),n})(d)+r;return i.forEach((t=>{if(c)return;const e=((t,e)=>{if($t(e))return e.exec(t);const n=e(t);if(!n)return null;const o=[n.text];return o.index=n.index,o.input=t,o.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith),o.push(n.replaceWith)),o})(u,t.find);if(!e)return;const i=l.state.tr,d=yt({state:l.state,transaction:i}),p={from:o-(e[0].length-r.length),to:s},{commands:h,chain:m,can:f}=new vt({editor:n,state:d});null!==t.handler({state:d,range:p,match:e,commands:h,chain:m,can:f})&&i.steps.length&&(i.setMeta(a,{transform:i,from:o,to:s,text:r}),l.dispatch(i),c=!0)})),c}function It(t){const{editor:e,rules:n}=t,s=new o({state:{init:()=>null,apply(t,o,r){const i=t.getMeta(s);if(i)return i;const a=t.getMeta("applyInputRules");return!!a&&setTimeout((()=>{let{text:t}=a;"string"==typeof t||(t=Pt(I.from(t),r.schema));const{from:o}=a,i=o+t.length;_t({editor:e,from:o,to:i,text:t,rules:n,plugin:s})})),t.selectionSet||t.docChanged?null:o}},props:{handleTextInput:(t,o,r,i)=>_t({editor:e,from:o,to:r,text:i,rules:n,plugin:s}),handleDOMEvents:{compositionend:t=>(setTimeout((()=>{const{$cursor:o}=t.state.selection;o&&_t({editor:e,from:o.pos,to:o.pos,text:"",rules:n,plugin:s})})),!1)},handleKeyDown(t,o){if("Enter"!==o.key)return!1;const{$cursor:r}=t.state.selection;return!!r&&_t({editor:e,from:r.pos,to:r.pos,text:"\n",rules:n,plugin:s})}},isInputRules:!0});return s}function jt(t){return"Object"===function(t){return Object.prototype.toString.call(t).slice(8,-1)}(t)&&(t.constructor===Object&&Object.getPrototypeOf(t)===Object.prototype)}function Dt(t,e){const n={...t};return jt(t)&&jt(e)&&Object.keys(e).forEach((o=>{jt(e[o])&&jt(t[o])?n[o]=Dt(t[o],e[o]):n[o]=e[o]})),n}class Rt{constructor(t={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length,this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=At(kt(this,"addOptions",{name:this.name}))),this.storage=At(kt(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Rt(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>Dt(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new Rt(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length,e.options=At(kt(e,"addOptions",{name:e.name})),e.storage=At(kt(e,"addStorage",{name:e.name,options:e.options})),e}static handleExit({editor:t,mark:e}){const{tr:n}=t.state,o=t.state.selection.$from;if(o.pos===o.end()){const s=o.marks();if(!!!s.find((t=>(null==t?void 0:t.type.name)===e.name)))return!1;const r=s.find((t=>(null==t?void 0:t.type.name)===e.name));return r&&n.removeStoredMark(r),n.insertText(" ",o.pos),t.view.dispatch(n),!0}return!1}}class Bt{constructor(t){this.find=t.find,this.handler=t.handler}}function zt(t){const{editor:e,state:n,from:o,to:s,rule:r,pasteEvent:i,dropEvent:a}=t,{commands:l,chain:d,can:c}=new vt({editor:e,state:n}),u=[];n.doc.nodesBetween(o,s,((t,e)=>{if(!t.isTextblock||t.type.spec.code)return;const p=Math.max(o,e),h=Math.min(s,e+t.content.size);((t,e,n)=>{if($t(e))return[...t.matchAll(e)];const o=e(t,n);return o?o.map((e=>{const n=[e.text];return n.index=e.index,n.input=t,n.data=e.data,e.replaceWith&&(e.text.includes(e.replaceWith),n.push(e.replaceWith)),n})):[]})(t.textBetween(p-e,h-e,void 0,"￼"),r.find,i).forEach((t=>{if(void 0===t.index)return;const e=p+t.index+1,o=e+t[0].length,s={from:n.tr.mapping.map(e),to:n.tr.mapping.map(o)},h=r.handler({state:n,range:s,match:t,commands:l,chain:d,can:c,pasteEvent:i,dropEvent:a});u.push(h)}))}));return u.every((t=>null!==t))}let qt=null;function Kt(t){const{editor:e,rules:n}=t;let s,r=null,i=!1,a=!1,l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{s="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{s=null}const d=({state:t,from:n,to:o,rule:r,pasteEvt:i})=>{const a=t.tr,d=yt({state:t,transaction:a});if(zt({editor:e,state:d,from:Math.max(n-1,0),to:o.b-1,rule:r,pasteEvent:i,dropEvent:s})&&a.steps.length){try{s="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{s=null}return l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,a}};return n.map((t=>new o({view(t){const n=n=>{var o;r=(null===(o=t.dom.parentElement)||void 0===o?void 0:o.contains(n.target))?t.dom.parentElement:null,r&&(qt=e)},o=()=>{qt&&(qt=null)};return window.addEventListener("dragstart",n),window.addEventListener("dragend",o),{destroy(){window.removeEventListener("dragstart",n),window.removeEventListener("dragend",o)}}},props:{handleDOMEvents:{drop:(t,e)=>{if(a=r===t.dom.parentElement,s=e,!a){const t=qt;t&&setTimeout((()=>{const e=t.state.selection;e&&t.commands.deleteRange({from:e.from,to:e.to})}),10)}return!1},paste:(t,e)=>{var n;const o=null===(n=e.clipboardData)||void 0===n?void 0:n.getData("text/html");return l=e,i=!!(null==o?void 0:o.includes("data-pm-slice")),!1}}},appendTransaction:(e,n,o)=>{const s=e[0],r="paste"===s.getMeta("uiEvent")&&!i,c="drop"===s.getMeta("uiEvent")&&!a,u=s.getMeta("applyPasteRules"),p=!!u;if(!r&&!c&&!p)return;if(p){let{text:e}=u;"string"==typeof e||(e=Pt(I.from(e),o.schema));const{from:n}=u,s=n+e.length,r=(t=>{var e;const n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null===(e=n.clipboardData)||void 0===e||e.setData("text/html",t),n})(e);return d({rule:t,state:o,from:n,to:{b:s},pasteEvt:r})}const h=n.doc.content.findDiffStart(o.doc.content),m=n.doc.content.findDiffEnd(o.doc.content);return"number"==typeof h&&m&&h!==m.b?d({rule:t,state:o,from:h,to:m,pasteEvt:l}):void 0}})))}class Ut{constructor(t,e){this.splittableMarks=[],this.editor=e,this.extensions=Ut.resolve(t),this.schema=function(t,e){var n;const o=Mt(t),{nodeExtensions:s,markExtensions:r}=wt(t),i=null===(n=s.find((t=>kt(t,"topNode"))))||void 0===n?void 0:n.name,a=Object.fromEntries(s.map((n=>{const s=o.filter((t=>t.type===n.name)),r={name:n.name,options:n.options,storage:n.storage,editor:e},i=Lt({...t.reduce(((t,e)=>{const o=kt(e,"extendNodeSchema",r);return{...t,...o?o(n):{}}}),{}),content:At(kt(n,"content",r)),marks:At(kt(n,"marks",r)),group:At(kt(n,"group",r)),inline:At(kt(n,"inline",r)),atom:At(kt(n,"atom",r)),selectable:At(kt(n,"selectable",r)),draggable:At(kt(n,"draggable",r)),code:At(kt(n,"code",r)),whitespace:At(kt(n,"whitespace",r)),linebreakReplacement:At(kt(n,"linebreakReplacement",r)),defining:At(kt(n,"defining",r)),isolating:At(kt(n,"isolating",r)),attrs:Object.fromEntries(s.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),a=At(kt(n,"parseHTML",r));a&&(i.parseDOM=a.map((t=>Ot(t,s))));const l=kt(n,"renderHTML",r);l&&(i.toDOM=t=>l({node:t,HTMLAttributes:St(t,s)}));const d=kt(n,"renderText",r);return d&&(i.toText=d),[n.name,i]}))),l=Object.fromEntries(r.map((n=>{const s=o.filter((t=>t.type===n.name)),r={name:n.name,options:n.options,storage:n.storage,editor:e},i=Lt({...t.reduce(((t,e)=>{const o=kt(e,"extendMarkSchema",r);return{...t,...o?o(n):{}}}),{}),inclusive:At(kt(n,"inclusive",r)),excludes:At(kt(n,"excludes",r)),group:At(kt(n,"group",r)),spanning:At(kt(n,"spanning",r)),code:At(kt(n,"code",r)),attrs:Object.fromEntries(s.map((t=>{var e;return[t.name,{default:null===(e=null==t?void 0:t.attribute)||void 0===e?void 0:e.default}]})))}),a=At(kt(n,"parseHTML",r));a&&(i.parseDOM=a.map((t=>Ot(t,s))));const l=kt(n,"renderHTML",r);return l&&(i.toDOM=t=>l({mark:t,HTMLAttributes:St(t,s)})),[n.name,i]})));return new R({topNode:i,nodes:a,marks:l})}(this.extensions,e),this.setupExtensions()}static resolve(t){const e=Ut.sort(Ut.flatten(t));return function(t){const e=t.filter(((e,n)=>t.indexOf(e)!==n));return Array.from(new Set(e))}(e.map((t=>t.name))).length,e}static flatten(t){return t.map((t=>{const e=kt(t,"addExtensions",{name:t.name,options:t.options,storage:t.storage});return e?[t,...this.flatten(e())]:t})).flat(10)}static sort(t){return t.sort(((t,e)=>{const n=kt(t,"priority")||100,o=kt(e,"priority")||100;return n>o?-1:n<o?1:0}))}get commands(){return this.extensions.reduce(((t,e)=>{const n=kt(e,"addCommands",{name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:Ct(e.name,this.schema)});return n?{...t,...n()}:t}),{})}get plugins(){const{editor:t}=this,e=Ut.sort([...this.extensions].reverse()),n=[],o=[],s=e.map((e=>{const s={name:e.name,options:e.options,storage:e.storage,editor:t,type:Ct(e.name,this.schema)},r=[],i=kt(e,"addKeyboardShortcuts",s);let a={};if("mark"===e.type&&kt(e,"exitable",s)&&(a.ArrowRight=()=>Rt.handleExit({editor:t,mark:e})),i){const e=Object.fromEntries(Object.entries(i()).map((([e,n])=>[e,()=>n({editor:t})])));a={...a,...e}}const l=N(a);r.push(l);const d=kt(e,"addInputRules",s);Ht(e,t.options.enableInputRules)&&d&&n.push(...d());const c=kt(e,"addPasteRules",s);Ht(e,t.options.enablePasteRules)&&c&&o.push(...c());const u=kt(e,"addProseMirrorPlugins",s);if(u){const t=u();r.push(...t)}return r})).flat();return[It({editor:t,rules:n}),...Kt({editor:t,rules:o}),...s]}get attributes(){return Mt(this.extensions)}get nodeViews(){const{editor:t}=this,{nodeExtensions:e}=wt(this.extensions);return Object.fromEntries(e.filter((t=>!!kt(t,"addNodeView"))).map((e=>{const n=this.attributes.filter((t=>t.type===e.name)),o={name:e.name,options:e.options,storage:e.storage,editor:t,type:xt(e.name,this.schema)},s=kt(e,"addNodeView",o);if(!s)return[];return[e.name,(o,r,i,a,l)=>{const d=St(o,n);return s()({node:o,view:r,getPos:i,decorations:a,innerDecorations:l,editor:t,extension:e,HTMLAttributes:d})}]})))}setupExtensions(){this.extensions.forEach((t=>{var e;this.editor.extensionStorage[t.name]=t.storage;const n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:Ct(t.name,this.schema)};if("mark"===t.type){(null===(e=At(kt(t,"keepOnSplit",n)))||void 0===e||e)&&this.splittableMarks.push(t.name)}const o=kt(t,"onBeforeCreate",n),s=kt(t,"onCreate",n),r=kt(t,"onUpdate",n),i=kt(t,"onSelectionUpdate",n),a=kt(t,"onTransaction",n),l=kt(t,"onFocus",n),d=kt(t,"onBlur",n),c=kt(t,"onDestroy",n);o&&this.editor.on("beforeCreate",o),s&&this.editor.on("create",s),r&&this.editor.on("update",r),i&&this.editor.on("selectionUpdate",i),a&&this.editor.on("transaction",a),l&&this.editor.on("focus",l),d&&this.editor.on("blur",d),c&&this.editor.on("destroy",c)}))}}class Vt{constructor(t={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length,this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=At(kt(this,"addOptions",{name:this.name}))),this.storage=At(kt(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new Vt(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>Dt(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new Vt({...this.config,...t});return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length,e.options=At(kt(e,"addOptions",{name:e.name})),e.storage=At(kt(e,"addStorage",{name:e.name,options:e.options})),e}}function Wt(t,e,n){const{from:o,to:s}=e,{blockSeparator:r="\n\n",textSerializers:i={}}=n||{};let a="";return t.nodesBetween(o,s,((t,n,l,d)=>{var c;t.isBlock&&n>o&&(a+=r);const u=null==i?void 0:i[t.type.name];if(u)return l&&(a+=u({node:t,pos:n,parent:l,index:d,range:e})),!1;t.isText&&(a+=null===(c=null==t?void 0:t.text)||void 0===c?void 0:c.slice(Math.max(o,n)-n,s-n))})),a}function Ft(t){return Object.fromEntries(Object.entries(t.nodes).filter((([,t])=>t.spec.toText)).map((([t,e])=>[t,e.spec.toText])))}const Qt=Vt.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new o({key:new n("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{const{editor:t}=this,{state:e,schema:n}=t,{doc:o,selection:s}=e,{ranges:r}=s,i=Math.min(...r.map((t=>t.$from.pos))),a=Math.max(...r.map((t=>t.$to.pos))),l=Ft(n);return Wt(o,{from:i,to:a},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:l})}}})]}});function Gt(t,e,n={strict:!0}){const o=Object.keys(e);return!o.length||o.every((o=>n.strict?e[o]===t[o]:$t(e[o])?e[o].test(t[o]):e[o]===t[o]))}function Jt(t,e,n={}){return t.find((t=>t.type===e&&Gt(Object.fromEntries(Object.keys(n).map((e=>[e,t.attrs[e]]))),n)))}function Xt(t,e,n={}){return!!Jt(t,e,n)}function Zt(t,e,n){var o;if(!t||!e)return;let s=t.parent.childAfter(t.parentOffset);if(s.node&&s.node.marks.some((t=>t.type===e))||(s=t.parent.childBefore(t.parentOffset)),!s.node||!s.node.marks.some((t=>t.type===e)))return;n=n||(null===(o=s.node.marks[0])||void 0===o?void 0:o.attrs);if(!Jt([...s.node.marks],e,n))return;let r=s.index,i=t.start()+s.offset,a=r+1,l=i+s.node.nodeSize;for(;r>0&&Xt([...t.parent.child(r-1).marks],e,n);)r-=1,i-=t.parent.child(r).nodeSize;for(;a<t.parent.childCount&&Xt([...t.parent.child(a).marks],e,n);)l+=t.parent.child(a).nodeSize,a+=1;return{from:i,to:l}}function Yt(t,e){if("string"==typeof t){if(!e.marks[t])throw Error(`There is no mark type named '${t}'. Maybe you forgot to add the extension?`);return e.marks[t]}return t}function te(t){return t instanceof r}function ee(t=0,e=0,n=0){return Math.min(Math.max(t,e),n)}function ne(t,e=null){if(!e)return null;const n=s.atStart(t),o=s.atEnd(t);if("start"===e||!0===e)return n;if("end"===e)return o;const i=n.from,a=o.to;return"all"===e?r.create(t,ee(0,i,a),ee(t.content.size,i,a)):r.create(t,ee(e,i,a),ee(e,i,a))}function oe(){return"Android"===navigator.platform||/android/i.test(navigator.userAgent)}function se(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}const re=t=>{const e=t.childNodes;for(let n=e.length-1;n>=0;n-=1){const o=e[n];3===o.nodeType&&o.nodeValue&&/^(\n\s\s|\n)$/.test(o.nodeValue)?t.removeChild(o):1===o.nodeType&&re(o)}return t};function ie(t){const e=`<body>${t}</body>`,n=(new window.DOMParser).parseFromString(e,"text/html").body;return re(n)}function ae(t,e,n){if(t instanceof D||t instanceof I)return t;n={slice:!0,parseOptions:{},...n};const o="string"==typeof t;if("object"==typeof t&&null!==t)try{if(Array.isArray(t)&&t.length>0)return I.fromArray(t.map((t=>e.nodeFromJSON(t))));const o=e.nodeFromJSON(t);return n.errorOnInvalidContent&&o.check(),o}catch(s){if(n.errorOnInvalidContent)throw new Error("[tiptap error]: Invalid JSON content",{cause:s});return ae("",e,n)}if(o){if(n.errorOnInvalidContent){let o=!1,s="";const r=new R({topNode:e.spec.topNode,marks:e.spec.marks,nodes:e.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:t=>(o=!0,s="string"==typeof t?t:t.outerHTML,null)}]}})});if(n.slice?B.fromSchema(r).parseSlice(ie(t),n.parseOptions):B.fromSchema(r).parse(ie(t),n.parseOptions),n.errorOnInvalidContent&&o)throw new Error("[tiptap error]: Invalid HTML content",{cause:new Error(`Invalid element found: ${s}`)})}const o=B.fromSchema(e);return n.slice?o.parseSlice(ie(t),n.parseOptions).content:o.parse(ie(t),n.parseOptions)}return ae("",e,n)}function le(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function de(t,e,n={}){const{from:o,to:s,empty:r}=t.selection,i=e?xt(e,t.schema):null,a=[];t.doc.nodesBetween(o,s,((t,e)=>{if(t.isText)return;const n=Math.max(o,e),r=Math.min(s,e+t.nodeSize);a.push({node:t,from:n,to:r})}));const l=s-o,d=a.filter((t=>!i||i.name===t.node.type.name)).filter((t=>Gt(t.node.attrs,n,{strict:!1})));if(r)return!!d.length;return d.reduce(((t,e)=>t+e.to-e.from),0)>=l}function ce(t,e){return e.nodes[t]?"node":e.marks[t]?"mark":null}function ue(t,e){const n="string"==typeof e?[e]:e;return Object.keys(t).reduce(((e,o)=>(n.includes(o)||(e[o]=t[o]),e)),{})}function pe(t,e,n={},o={}){return ae(t,e,{slice:!1,parseOptions:n,errorOnInvalidContent:o.errorOnInvalidContent})}function he(t,e){const n=Yt(e,t.schema),{from:o,to:s,empty:r}=t.selection,i=[];r?(t.storedMarks&&i.push(...t.storedMarks),i.push(...t.selection.$head.marks())):t.doc.nodesBetween(o,s,(t=>{i.push(...t.marks)}));const a=i.find((t=>t.type.name===n.name));return a?{...a.attrs}:{}}function me(t,e){const n=[];return t.descendants(((t,o)=>{e(t)&&n.push({node:t,pos:o})})),n}function fe(t){return e=>function(t,e){for(let n=t.depth;n>0;n-=1){const o=t.node(n);if(e(o))return{pos:n>0?t.before(n):0,start:t.start(n),depth:n,node:o}}}(e.$from,t)}function ge(t,e){return Wt(t,{from:0,to:t.content.size},e)}function ye(t,e){const n=ce("string"==typeof e?e:e.name,t.schema);return"node"===n?function(t,e){const n=xt(e,t.schema),{from:o,to:s}=t.selection,r=[];t.doc.nodesBetween(o,s,(t=>{r.push(t)}));const i=r.reverse().find((t=>t.type.name===n.name));return i?{...i.attrs}:{}}(t,e):"mark"===n?he(t,e):{}}function ve(t){const e=function(t,e=JSON.stringify){const n={};return t.filter((t=>{const o=e(t);return!Object.prototype.hasOwnProperty.call(n,o)&&(n[o]=!0)}))}(t);return 1===e.length?e:e.filter(((t,n)=>!e.filter(((t,e)=>e!==n)).some((e=>t.oldRange.from>=e.oldRange.from&&t.oldRange.to<=e.oldRange.to&&t.newRange.from>=e.newRange.from&&t.newRange.to<=e.newRange.to))))}function be(t,e,n){const o=[];return t===e?n.resolve(t).marks().forEach((e=>{const s=Zt(n.resolve(t),e.type);s&&o.push({mark:e,...s})})):n.nodesBetween(t,e,((t,e)=>{t&&void 0!==(null==t?void 0:t.nodeSize)&&o.push(...t.marks.map((n=>({from:e,to:e+t.nodeSize,mark:n}))))})),o}function ke(t,e,n){return Object.fromEntries(Object.entries(n).filter((([n])=>{const o=t.find((t=>t.type===e&&t.name===n));return!!o&&o.attribute.keepOnSplit})))}function we(t,e,n={}){const{empty:o,ranges:s}=t.selection,r=e?Yt(e,t.schema):null;if(o)return!!(t.storedMarks||t.selection.$from.marks()).filter((t=>!r||r.name===t.type.name)).find((t=>Gt(t.attrs,n,{strict:!1})));let i=0;const a=[];if(s.forEach((({$from:e,$to:n})=>{const o=e.pos,s=n.pos;t.doc.nodesBetween(o,s,((t,e)=>{if(!t.isText&&!t.marks.length)return;const n=Math.max(o,e),r=Math.min(s,e+t.nodeSize);i+=r-n,a.push(...t.marks.map((t=>({mark:t,from:n,to:r}))))}))})),0===i)return!1;const l=a.filter((t=>!r||r.name===t.mark.type.name)).filter((t=>Gt(t.mark.attrs,n,{strict:!1}))).reduce(((t,e)=>t+e.to-e.from),0),d=a.filter((t=>!r||t.mark.type!==r&&t.mark.type.excludes(r))).reduce(((t,e)=>t+e.to-e.from),0);return(l>0?l+d:l)>=i}function Me(t,e){const{nodeExtensions:n}=wt(e),o=n.find((e=>e.name===t));if(!o)return!1;const s=At(kt(o,"group",{name:o.name,options:o.options,storage:o.storage}));return"string"==typeof s&&s.split(" ").includes("list")}function xe(t,{checkChildren:e=!0,ignoreWhitespace:n=!1}={}){var o;if(n){if("hardBreak"===t.type.name)return!0;if(t.isText)return/^\s*$/m.test(null!==(o=t.text)&&void 0!==o?o:"")}if(t.isText)return!t.text;if(t.isAtom||t.isLeaf)return!1;if(0===t.content.childCount)return!0;if(e){let o=!0;return t.content.forEach((t=>{!1!==o&&(xe(t,{ignoreWhitespace:n,checkChildren:e})||(o=!1))})),o}return!1}function Te(t){return t instanceof e}function Se(t,e,n){const o=t.state.doc.content.size,s=ee(e,0,o),r=ee(n,0,o),i=t.coordsAtPos(s),a=t.coordsAtPos(r,-1),l=Math.min(i.top,a.top),d=Math.max(i.bottom,a.bottom),c=Math.min(i.left,a.left),u=Math.max(i.right,a.right),p={top:l,bottom:d,left:c,right:u,width:u-c,height:d-l,x:c,y:l};return{...p,toJSON:()=>p}}function Ee(t,e){const n=t.storedMarks||t.selection.$to.parentOffset&&t.selection.$from.marks();if(n){const o=n.filter((t=>null==e?void 0:e.includes(t.type.name)));t.tr.ensureMarks(o)}}const Ae=(t,e)=>{const n=fe((t=>t.type===e))(t.selection);if(!n)return!0;const o=t.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===o)return!0;const s=t.doc.nodeAt(o);return n.node.type!==(null==s?void 0:s.type)||!q(t.doc,n.pos)||(t.join(n.pos),!0)},Oe=(t,e)=>{const n=fe((t=>t.type===e))(t.selection);if(!n)return!0;const o=t.doc.resolve(n.start).after(n.depth);if(void 0===o)return!0;const s=t.doc.nodeAt(o);return n.node.type!==(null==s?void 0:s.type)||!q(t.doc,o)||(t.join(o),!0)};var Le=Object.freeze({__proto__:null,blur:()=>({editor:t,view:e})=>(requestAnimationFrame((()=>{var n;t.isDestroyed||(e.dom.blur(),null===(n=null===window||void 0===window?void 0:window.getSelection())||void 0===n||n.removeAllRanges())})),!0),clearContent:(t=!1)=>({commands:e})=>e.setContent("",t),clearNodes:()=>({state:t,tr:e,dispatch:n})=>{const{selection:o}=e,{ranges:s}=o;return!n||(s.forEach((({$from:n,$to:o})=>{t.doc.nodesBetween(n.pos,o.pos,((t,n)=>{if(t.type.isText)return;const{doc:o,mapping:s}=e,r=o.resolve(s.map(n)),i=o.resolve(s.map(n+t.nodeSize)),a=r.blockRange(i);if(!a)return;const l=W(a);if(t.type.isTextblock){const{defaultType:t}=r.parent.contentMatchAt(r.index());e.setNodeMarkup(a.start,t)}(l||0===l)&&e.lift(a,l)}))})),!0)},command:t=>e=>t(e),createParagraphNear:()=>({state:t,dispatch:e})=>ht(t,e),cut:(t,e)=>({editor:n,tr:o})=>{const{state:s}=n,i=s.doc.slice(t.from,t.to);o.deleteRange(t.from,t.to);const a=o.mapping.map(e);return o.insert(a,i.content),o.setSelection(new r(o.doc.resolve(a-1))),!0},deleteCurrentNode:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,o=n.$anchor.node();if(o.content.size>0)return!1;const s=t.selection.$anchor;for(let r=s.depth;r>0;r-=1){if(s.node(r).type===o.type){if(e){const e=s.before(r),n=s.after(r);t.delete(e,n).scrollIntoView()}return!0}}return!1},deleteNode:t=>({tr:e,state:n,dispatch:o})=>{const s=xt(t,n.schema),r=e.selection.$anchor;for(let t=r.depth;t>0;t-=1){if(r.node(t).type===s){if(o){const n=r.before(t),o=r.after(t);e.delete(n,o).scrollIntoView()}return!0}}return!1},deleteRange:t=>({tr:e,dispatch:n})=>{const{from:o,to:s}=t;return n&&e.delete(o,s),!0},deleteSelection:()=>({state:t,dispatch:e})=>pt(t,e),enter:()=>({commands:t})=>t.keyboardShortcut("Enter"),exitCode:()=>({state:t,dispatch:e})=>ut(t,e),extendMarkRange:(t,e={})=>({tr:n,state:o,dispatch:s})=>{const i=Yt(t,o.schema),{doc:a,selection:l}=n,{$from:d,from:c,to:u}=l;if(s){const t=Zt(d,i,e);if(t&&t.from<=c&&t.to>=u){const e=r.create(a,t.from,t.to);n.setSelection(e)}}return!0},first:t=>e=>{const n="function"==typeof t?t(e):t;for(let t=0;t<n.length;t+=1)if(n[t](e))return!0;return!1},focus:(t=null,e={})=>({editor:n,view:o,tr:s,dispatch:r})=>{e={scrollIntoView:!0,...e};const i=()=>{(se()||oe())&&o.dom.focus(),requestAnimationFrame((()=>{n.isDestroyed||(o.focus(),(null==e?void 0:e.scrollIntoView)&&n.commands.scrollIntoView())}))};if(o.hasFocus()&&null===t||!1===t)return!0;if(r&&null===t&&!te(n.state.selection))return i(),!0;const a=ne(s.doc,t)||n.state.selection,l=n.state.selection.eq(a);return r&&(l||s.setSelection(a),l&&s.storedMarks&&s.setStoredMarks(s.storedMarks),i()),!0},forEach:(t,e)=>n=>t.every(((t,o)=>e(t,{...n,index:o}))),insertContent:(t,e)=>({tr:n,commands:o})=>o.insertContentAt({from:n.selection.from,to:n.selection.to},t,e),insertContentAt:(t,e,n)=>({tr:o,dispatch:r,editor:i})=>{var a;if(r){let r;n={parseOptions:i.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};try{r=ae(e,i.schema,{parseOptions:{preserveWhitespace:"full",...n.parseOptions},errorOnInvalidContent:null!==(a=n.errorOnInvalidContent)&&void 0!==a?a:i.options.enableContentCheck})}catch(l){return i.emit("contentError",{editor:i,error:l,disableCollaboration:()=>{i.storage.collaboration&&(i.storage.collaboration.isDisabled=!0)}}),!1}let{from:d,to:c}="number"==typeof t?{from:t,to:t}:{from:t.from,to:t.to},u=!0,p=!0;if(("type"in r?[r]:r).forEach((t=>{t.check(),u=!!u&&(t.isText&&0===t.marks.length),p=!!p&&t.isBlock})),d===c&&p){const{parent:t}=o.doc.resolve(d);t.isTextblock&&!t.type.spec.code&&!t.childCount&&(d-=1,c+=1)}let h;if(u){if(Array.isArray(e))h=e.map((t=>t.text||"")).join("");else if(e instanceof I){let t="";e.forEach((e=>{e.text&&(t+=e.text)})),h=t}else h="object"==typeof e&&e&&e.text?e.text:e;o.insertText(h,d,c)}else h=r,o.replaceWith(d,c,h);n.updateSelection&&function(t,e,n){const o=t.steps.length-1;if(o<e)return;const r=t.steps[o];if(!(r instanceof F||r instanceof Q))return;const i=t.mapping.maps[o];let a=0;i.forEach(((t,e,n,o)=>{0===a&&(a=o)})),t.setSelection(s.near(t.doc.resolve(a),n))}(o,o.steps.length-1,-1),n.applyInputRules&&o.setMeta("applyInputRules",{from:d,text:h}),n.applyPasteRules&&o.setMeta("applyPasteRules",{from:d,text:h})}return!0},joinBackward:()=>({state:t,dispatch:e})=>ct(t,e),joinDown:()=>({state:t,dispatch:e})=>dt(t,e),joinForward:()=>({state:t,dispatch:e})=>lt(t,e),joinItemBackward:()=>({state:t,dispatch:e,tr:n})=>{try{const o=V(t.doc,t.selection.$from.pos,-1);return null!=o&&(n.join(o,2),e&&e(n),!0)}catch{return!1}},joinItemForward:()=>({state:t,dispatch:e,tr:n})=>{try{const o=V(t.doc,t.selection.$from.pos,1);return null!=o&&(n.join(o,2),e&&e(n),!0)}catch{return!1}},joinTextblockBackward:()=>({state:t,dispatch:e})=>at(t,e),joinTextblockForward:()=>({state:t,dispatch:e})=>it(t,e),joinUp:()=>({state:t,dispatch:e})=>rt(t,e),keyboardShortcut:t=>({editor:e,view:n,tr:o,dispatch:s})=>{const r=function(t){const e=t.split(/-(?!$)/);let n,o,s,r,i=e[e.length-1];"Space"===i&&(i=" ");for(let a=0;a<e.length-1;a+=1){const t=e[a];if(/^(cmd|meta|m)$/i.test(t))r=!0;else if(/^a(lt)?$/i.test(t))n=!0;else if(/^(c|ctrl|control)$/i.test(t))o=!0;else if(/^s(hift)?$/i.test(t))s=!0;else{if(!/^mod$/i.test(t))throw new Error(`Unrecognized modifier name: ${t}`);se()||le()?r=!0:o=!0}}return n&&(i=`Alt-${i}`),o&&(i=`Ctrl-${i}`),r&&(i=`Meta-${i}`),s&&(i=`Shift-${i}`),i}(t).split(/-(?!$)/),i=r.find((t=>!["Alt","Ctrl","Meta","Shift"].includes(t))),a=new KeyboardEvent("keydown",{key:"Space"===i?" ":i,altKey:r.includes("Alt"),ctrlKey:r.includes("Ctrl"),metaKey:r.includes("Meta"),shiftKey:r.includes("Shift"),bubbles:!0,cancelable:!0}),l=e.captureTransaction((()=>{n.someProp("handleKeyDown",(t=>t(n,a)))}));return null==l||l.steps.forEach((t=>{const e=t.map(o.mapping);e&&s&&o.maybeStep(e)})),!0},lift:(t,e={})=>({state:n,dispatch:o})=>!!de(n,xt(t,n.schema),e)&&st(n,o),liftEmptyBlock:()=>({state:t,dispatch:e})=>ot(t,e),liftListItem:t=>({state:e,dispatch:n})=>{const o=xt(t,e.schema);return gt(o)(e,n)},newlineInCode:()=>({state:t,dispatch:e})=>nt(t,e),resetAttributes:(t,e)=>({tr:n,state:o,dispatch:s})=>{let r=null,i=null;const a=ce("string"==typeof t?t:t.name,o.schema);return!!a&&("node"===a&&(r=xt(t,o.schema)),"mark"===a&&(i=Yt(t,o.schema)),s&&n.selection.ranges.forEach((t=>{o.doc.nodesBetween(t.$from.pos,t.$to.pos,((t,o)=>{r&&r===t.type&&n.setNodeMarkup(o,void 0,ue(t.attrs,e)),i&&t.marks.length&&t.marks.forEach((s=>{i===s.type&&n.addMark(o,o+t.nodeSize,i.create(ue(s.attrs,e)))}))}))})),!0)},scrollIntoView:()=>({tr:t,dispatch:e})=>(e&&t.scrollIntoView(),!0),selectAll:()=>({tr:t,dispatch:e})=>{if(e){const e=new i(t.doc);t.setSelection(e)}return!0},selectNodeBackward:()=>({state:t,dispatch:e})=>et(t,e),selectNodeForward:()=>({state:t,dispatch:e})=>tt(t,e),selectParentNode:()=>({state:t,dispatch:e})=>Y(t,e),selectTextblockEnd:()=>({state:t,dispatch:e})=>Z(t,e),selectTextblockStart:()=>({state:t,dispatch:e})=>X(t,e),setContent:(t,e=!1,n={},o={})=>({editor:s,tr:r,dispatch:i,commands:a})=>{var l,d;const{doc:c}=r;if("full"!==n.preserveWhitespace){const a=pe(t,s.schema,n,{errorOnInvalidContent:null!==(l=o.errorOnInvalidContent)&&void 0!==l?l:s.options.enableContentCheck});return i&&r.replaceWith(0,c.content.size,a).setMeta("preventUpdate",!e),!0}return i&&r.setMeta("preventUpdate",!e),a.insertContentAt({from:0,to:c.content.size},t,{parseOptions:n,errorOnInvalidContent:null!==(d=o.errorOnInvalidContent)&&void 0!==d?d:s.options.enableContentCheck})},setMark:(t,e={})=>({tr:n,state:o,dispatch:s})=>{const{selection:r}=n,{empty:i,ranges:a}=r,l=Yt(t,o.schema);if(s)if(i){const t=he(o,l);n.addStoredMark(l.create({...t,...e}))}else a.forEach((t=>{const s=t.$from.pos,r=t.$to.pos;o.doc.nodesBetween(s,r,((t,o)=>{const i=Math.max(o,s),a=Math.min(o+t.nodeSize,r);t.marks.find((t=>t.type===l))?t.marks.forEach((t=>{l===t.type&&n.addMark(i,a,l.create({...t.attrs,...e}))})):n.addMark(i,a,l.create(e))}))}));return function(t,e,n){var o;const{selection:s}=e;let r=null;if(te(s)&&(r=s.$cursor),r){const e=null!==(o=t.storedMarks)&&void 0!==o?o:r.marks();return!!n.isInSet(e)||!e.some((t=>t.type.excludes(n)))}const{ranges:i}=s;return i.some((({$from:e,$to:o})=>{let s=0===e.depth&&t.doc.inlineContent&&t.doc.type.allowsMarkType(n);return t.doc.nodesBetween(e.pos,o.pos,((t,e,o)=>{if(s)return!1;if(t.isInline){const e=!o||o.type.allowsMarkType(n),r=!!n.isInSet(t.marks)||!t.marks.some((t=>t.type.excludes(n)));s=e&&r}return!s})),s}))}(o,n,l)},setMeta:(t,e)=>({tr:n})=>(n.setMeta(t,e),!0),setNode:(t,e={})=>({state:n,dispatch:o,chain:s})=>{const r=xt(t,n.schema);let i;return n.selection.$anchor.sameParent(n.selection.$head)&&(i=n.selection.$anchor.parent.attrs),!!r.isTextblock&&s().command((({commands:t})=>!!J(r,{...i,...e})(n)||t.clearNodes())).command((({state:t})=>J(r,{...i,...e})(t,o))).run()},setNodeSelection:t=>({tr:n,dispatch:o})=>{if(o){const{doc:o}=n,s=ee(t,0,o.content.size),r=e.create(o,s);n.setSelection(r)}return!0},setTextSelection:t=>({tr:e,dispatch:n})=>{if(n){const{doc:n}=e,{from:o,to:s}="number"==typeof t?{from:t,to:t}:t,i=r.atStart(n).from,a=r.atEnd(n).to,l=ee(o,i,a),d=ee(s,i,a),c=r.create(n,l,d);e.setSelection(c)}return!0},sinkListItem:t=>({state:e,dispatch:n})=>{const o=xt(t,e.schema);return ft(o)(e,n)},splitBlock:({keepMarks:t=!0}={})=>({tr:n,state:o,dispatch:s,editor:i})=>{const{selection:a,doc:l}=n,{$from:d,$to:c}=a,u=ke(i.extensionManager.attributes,d.node().type.name,d.node().attrs);if(a instanceof e&&a.node.isBlock)return!(!d.parentOffset||!U(l,d.pos))&&(s&&(t&&Ee(o,i.extensionManager.splittableMarks),n.split(d.pos).scrollIntoView()),!0);if(!d.parent.isBlock)return!1;const p=c.parentOffset===c.parent.content.size,h=0===d.depth?void 0:function(t){for(let e=0;e<t.edgeCount;e+=1){const{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(d.node(-1).contentMatchAt(d.indexAfter(-1)));let m=p&&h?[{type:h,attrs:u}]:void 0,f=U(n.doc,n.mapping.map(d.pos),1,m);if(m||f||!U(n.doc,n.mapping.map(d.pos),1,h?[{type:h}]:void 0)||(f=!0,m=h?[{type:h,attrs:u}]:void 0),s){if(f&&(a instanceof r&&n.deleteSelection(),n.split(n.mapping.map(d.pos),1,m),h&&!p&&!d.parentOffset&&d.parent.type!==h)){const t=n.mapping.map(d.before()),e=n.doc.resolve(t);d.node(-1).canReplaceWith(e.index(),e.index()+1,h)&&n.setNodeMarkup(n.mapping.map(d.before()),h)}t&&Ee(o,i.extensionManager.splittableMarks),n.scrollIntoView()}return f},splitListItem:(t,e={})=>({tr:n,state:o,dispatch:s,editor:i})=>{var a;const l=xt(t,o.schema),{$from:d,$to:c}=o.selection,u=o.selection.node;if(u&&u.isBlock||d.depth<2||!d.sameParent(c))return!1;const p=d.node(-1);if(p.type!==l)return!1;const h=i.extensionManager.attributes;if(0===d.parent.content.size&&d.node(-1).childCount===d.indexAfter(-1)){if(2===d.depth||d.node(-3).type!==l||d.index(-2)!==d.node(-2).childCount-1)return!1;if(s){let t=I.empty;const o=d.index(-1)?1:d.index(-2)?2:3;for(let e=d.depth-o;e>=d.depth-3;e-=1)t=I.from(d.node(e).copy(t));const s=d.indexAfter(-1)<d.node(-2).childCount?1:d.indexAfter(-2)<d.node(-3).childCount?2:3,i={...ke(h,d.node().type.name,d.node().attrs),...e},c=(null===(a=l.contentMatch.defaultType)||void 0===a?void 0:a.createAndFill(i))||void 0;t=t.append(I.from(l.createAndFill(null,c)||void 0));const u=d.before(d.depth-(o-1));n.replace(u,d.after(-s),new j(t,4-o,0));let p=-1;n.doc.nodesBetween(u,n.doc.content.size,((t,e)=>{if(p>-1)return!1;t.isTextblock&&0===t.content.size&&(p=e+1)})),p>-1&&n.setSelection(r.near(n.doc.resolve(p))),n.scrollIntoView()}return!0}const m=c.pos===d.end()?p.contentMatchAt(0).defaultType:null,f={...ke(h,p.type.name,p.attrs),...e},g={...ke(h,d.node().type.name,d.node().attrs),...e};n.delete(d.pos,c.pos);const y=m?[{type:l,attrs:f},{type:m,attrs:g}]:[{type:l,attrs:f}];if(!U(n.doc,d.pos,2))return!1;if(s){const{selection:t,storedMarks:e}=o,{splittableMarks:r}=i.extensionManager,a=e||t.$to.parentOffset&&t.$from.marks();if(n.split(d.pos,2,y).scrollIntoView(),!a||!s)return!0;const l=a.filter((t=>r.includes(t.type.name)));n.ensureMarks(l)}return!0},toggleList:(t,e,n,o={})=>({editor:s,tr:r,state:i,dispatch:a,chain:l,commands:d,can:c})=>{const{extensions:u,splittableMarks:p}=s.extensionManager,h=xt(t,i.schema),m=xt(e,i.schema),{selection:f,storedMarks:g}=i,{$from:y,$to:v}=f,b=y.blockRange(v),k=g||f.$to.parentOffset&&f.$from.marks();if(!b)return!1;const w=fe((t=>Me(t.type.name,u)))(f);if(b.depth>=1&&w&&b.depth-w.depth<=1){if(w.node.type===h)return d.liftListItem(m);if(Me(w.node.type.name,u)&&h.validContent(w.node.content)&&a)return l().command((()=>(r.setNodeMarkup(w.pos,h),!0))).command((()=>Ae(r,h))).command((()=>Oe(r,h))).run()}return n&&k&&a?l().command((()=>{const t=c().wrapInList(h,o),e=k.filter((t=>p.includes(t.type.name)));return r.ensureMarks(e),!!t||d.clearNodes()})).wrapInList(h,o).command((()=>Ae(r,h))).command((()=>Oe(r,h))).run():l().command((()=>!!c().wrapInList(h,o)||d.clearNodes())).wrapInList(h,o).command((()=>Ae(r,h))).command((()=>Oe(r,h))).run()},toggleMark:(t,e={},n={})=>({state:o,commands:s})=>{const{extendEmptyMarkRange:r=!1}=n,i=Yt(t,o.schema);return we(o,i,e)?s.unsetMark(i,{extendEmptyMarkRange:r}):s.setMark(i,e)},toggleNode:(t,e,n={})=>({state:o,commands:s})=>{const r=xt(t,o.schema),i=xt(e,o.schema),a=de(o,r,n);let l;return o.selection.$anchor.sameParent(o.selection.$head)&&(l=o.selection.$anchor.parent.attrs),a?s.setNode(i,l):s.setNode(r,{...l,...n})},toggleWrap:(t,e={})=>({state:n,commands:o})=>{const s=xt(t,n.schema);return de(n,s,e)?o.lift(s):o.wrapIn(s,e)},undoInputRule:()=>({state:t,dispatch:e})=>{const n=t.plugins;for(let o=0;o<n.length;o+=1){const s=n[o];let r;if(s.spec.isInputRules&&(r=s.getState(t))){if(e){const e=t.tr,n=r.transform;for(let t=n.steps.length-1;t>=0;t-=1)e.step(n.steps[t].invert(n.docs[t]));if(r.text){const n=e.doc.resolve(r.from).marks();e.replaceWith(r.from,r.to,t.schema.text(r.text,n))}else e.delete(r.from,r.to)}return!0}}return!1},unsetAllMarks:()=>({tr:t,dispatch:e})=>{const{selection:n}=t,{empty:o,ranges:s}=n;return o||e&&s.forEach((e=>{t.removeMark(e.$from.pos,e.$to.pos)})),!0},unsetMark:(t,e={})=>({tr:n,state:o,dispatch:s})=>{var r;const{extendEmptyMarkRange:i=!1}=e,{selection:a}=n,l=Yt(t,o.schema),{$from:d,empty:c,ranges:u}=a;if(!s)return!0;if(c&&i){let{from:t,to:e}=a;const o=null===(r=d.marks().find((t=>t.type===l)))||void 0===r?void 0:r.attrs,s=Zt(d,l,o);s&&(t=s.from,e=s.to),n.removeMark(t,e,l)}else u.forEach((t=>{n.removeMark(t.$from.pos,t.$to.pos,l)}));return n.removeStoredMark(l),!0},updateAttributes:(t,e={})=>({tr:n,state:o,dispatch:s})=>{let r=null,i=null;const a=ce("string"==typeof t?t:t.name,o.schema);return!!a&&("node"===a&&(r=xt(t,o.schema)),"mark"===a&&(i=Yt(t,o.schema)),s&&n.selection.ranges.forEach((t=>{const s=t.$from.pos,a=t.$to.pos;let l,d,c,u;n.selection.empty?o.doc.nodesBetween(s,a,((t,e)=>{r&&r===t.type&&(c=Math.max(e,s),u=Math.min(e+t.nodeSize,a),l=e,d=t)})):o.doc.nodesBetween(s,a,((t,o)=>{o<s&&r&&r===t.type&&(c=Math.max(o,s),u=Math.min(o+t.nodeSize,a),l=o,d=t),o>=s&&o<=a&&(r&&r===t.type&&n.setNodeMarkup(o,void 0,{...t.attrs,...e}),i&&t.marks.length&&t.marks.forEach((r=>{if(i===r.type){const l=Math.max(o,s),d=Math.min(o+t.nodeSize,a);n.addMark(l,d,i.create({...r.attrs,...e}))}})))})),d&&(void 0!==l&&n.setNodeMarkup(l,void 0,{...d.attrs,...e}),i&&d.marks.length&&d.marks.forEach((t=>{i===t.type&&n.addMark(c,u,i.create({...t.attrs,...e}))})))})),!0)},wrapIn:(t,e={})=>({state:n,dispatch:o})=>{const s=xt(t,n.schema);return G(s,e)(n,o)},wrapInList:(t,e={})=>({state:n,dispatch:o})=>{const s=xt(t,n.schema);return mt(s,e)(n,o)}});const Ce=Vt.create({name:"commands",addCommands:()=>({...Le})}),He=Vt.create({name:"drop",addProseMirrorPlugins(){return[new o({key:new n("tiptapDrop"),props:{handleDrop:(t,e,n,o)=>{this.editor.emit("drop",{editor:this.editor,event:e,slice:n,moved:o})}}})]}}),Pe=Vt.create({name:"editable",addProseMirrorPlugins(){return[new o({key:new n("editable"),props:{editable:()=>this.editor.options.editable}})]}}),$e=new n("focusEvents"),Ne=Vt.create({name:"focusEvents",addProseMirrorPlugins(){const{editor:t}=this;return[new o({key:$e,props:{handleDOMEvents:{focus:(e,n)=>{t.isFocused=!0;const o=t.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return e.dispatch(o),!1},blur:(e,n)=>{t.isFocused=!1;const o=t.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return e.dispatch(o),!1}}}})]}}),_e=Vt.create({name:"keymap",addKeyboardShortcuts(){const t=()=>this.editor.commands.first((({commands:t})=>[()=>t.undoInputRule(),()=>t.command((({tr:e})=>{const{selection:n,doc:o}=e,{empty:r,$anchor:i}=n,{pos:a,parent:l}=i,d=i.parent.isTextblock&&a>0?e.doc.resolve(a-1):i,c=d.parent.type.spec.isolating,u=i.pos-i.parentOffset,p=c&&1===d.parent.childCount?u===i.pos:s.atStart(o).from===a;return!(!r||!l.type.isTextblock||l.textContent.length||!p||p&&"paragraph"===i.parent.type.name)&&t.clearNodes()})),()=>t.deleteSelection(),()=>t.joinBackward(),()=>t.selectNodeBackward()])),e=()=>this.editor.commands.first((({commands:t})=>[()=>t.deleteSelection(),()=>t.deleteCurrentNode(),()=>t.joinForward(),()=>t.selectNodeForward()])),n={Enter:()=>this.editor.commands.first((({commands:t})=>[()=>t.newlineInCode(),()=>t.createParagraphNear(),()=>t.liftEmptyBlock(),()=>t.splitBlock()])),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:t,"Mod-Backspace":t,"Shift-Backspace":t,Delete:e,"Mod-Delete":e,"Mod-a":()=>this.editor.commands.selectAll()},o={...n},r={...n,"Ctrl-h":t,"Alt-Backspace":t,"Ctrl-d":e,"Ctrl-Alt-Backspace":e,"Alt-Delete":e,"Alt-d":e,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return se()||le()?r:o},addProseMirrorPlugins(){return[new o({key:new n("clearDocument"),appendTransaction:(t,e,n)=>{if(t.some((t=>t.getMeta("composition"))))return;const o=t.some((t=>t.docChanged))&&!e.doc.eq(n.doc),r=t.some((t=>t.getMeta("preventClearDocument")));if(!o||r)return;const{empty:i,from:a,to:l}=e.selection,d=s.atStart(e.doc).from,c=s.atEnd(e.doc).to;if(i||!(a===d&&l===c))return;if(!xe(n.doc))return;const u=n.tr,p=yt({state:n,transaction:u}),{commands:h}=new vt({editor:this.editor,state:p});return h.clearNodes(),u.steps.length?u:void 0}})]}}),Ie=Vt.create({name:"paste",addProseMirrorPlugins(){return[new o({key:new n("tiptapPaste"),props:{handlePaste:(t,e,n)=>{this.editor.emit("paste",{editor:this.editor,event:e,slice:n})}}})]}}),je=Vt.create({name:"tabindex",addProseMirrorPlugins(){return[new o({key:new n("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});var De=Object.freeze({__proto__:null,ClipboardTextSerializer:Qt,Commands:Ce,Drop:He,Editable:Pe,FocusEvents:Ne,Keymap:_e,Paste:Ie,Tabindex:je,focusEventsPluginKey:$e});class Re{get name(){return this.node.type.name}constructor(t,e,n=!1,o=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=t,this.editor=e,this.currentNode=o}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var t;return null!==(t=this.actualDepth)&&void 0!==t?t:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(t){let e=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return;e=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:e,to:n},t)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+(this.node.isText?0:1)}get parent(){if(0===this.depth)return null;const t=this.resolvedPos.start(this.resolvedPos.depth-1),e=this.resolvedPos.doc.resolve(t);return new Re(e,this.editor)}get before(){let t=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.from-3)),new Re(t,this.editor)}get after(){let t=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return t.depth!==this.depth&&(t=this.resolvedPos.doc.resolve(this.to+3)),new Re(t,this.editor)}get children(){const t=[];return this.node.content.forEach(((e,n)=>{const o=e.isBlock&&!e.isTextblock,s=e.isAtom&&!e.isText,r=this.pos+n+(s?0:1),i=this.resolvedPos.doc.resolve(r);if(!o&&i.depth<=this.depth)return;const a=new Re(i,this.editor,o,o?e:null);o&&(a.actualDepth=this.depth+1),t.push(new Re(i,this.editor,o,o?e:null))})),t}get firstChild(){return this.children[0]||null}get lastChild(){const t=this.children;return t[t.length-1]||null}closest(t,e={}){let n=null,o=this.parent;for(;o&&!n;){if(o.node.type.name===t)if(Object.keys(e).length>0){const t=o.node.attrs,n=Object.keys(e);for(let o=0;o<n.length;o+=1){const s=n[o];if(t[s]!==e[s])break}}else n=o;o=o.parent}return n}querySelector(t,e={}){return this.querySelectorAll(t,e,!0)[0]||null}querySelectorAll(t,e={},n=!1){let o=[];if(!this.children||0===this.children.length)return o;const s=Object.keys(e);return this.children.forEach((r=>{if(!(n&&o.length>0)){if(r.node.type.name===t){s.every((t=>e[t]===r.node.attrs[t]))&&o.push(r)}n&&o.length>0||(o=o.concat(r.querySelectorAll(t,e,n)))}})),o}setAttribute(t){const{tr:e}=this.editor.state;e.setNodeMarkup(this.from,void 0,{...this.node.attrs,...t}),this.editor.view.dispatch(e)}}let Be=class extends bt{constructor(t={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:t})=>{throw t},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(t),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",(({event:t,slice:e,moved:n})=>this.options.onDrop(t,e,n))),this.on("paste",(({event:t,slice:e})=>this.options.onPaste(t,e))),window.setTimeout((()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)}),0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(t,e){const n=document.querySelector("style[data-tiptap-style]");if(null!==n)return n;const o=document.createElement("style");return e&&o.setAttribute("nonce",e),o.setAttribute("data-tiptap-style",""),o.innerHTML=t,document.getElementsByTagName("head")[0].appendChild(o),o}('.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: "liga" 0; /* the above doesn\'t seem to work in Edge */\n}\n\n.ProseMirror [contenteditable="false"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable="false"] [contenteditable="true"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: "";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}',this.options.injectNonce))}setOptions(t={}){this.options={...this.options,...t},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(t,e=!0){this.setOptions({editable:t}),e&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(t,e){const n=Et(e)?e(t,[...this.state.plugins]):[...this.state.plugins,t],o=this.state.reconfigure({plugins:n});return this.view.updateState(o),o}unregisterPlugin(t){if(this.isDestroyed)return;const e=this.state.plugins;let n=e;if([].concat(t).forEach((t=>{const e="string"==typeof t?`${t}$`:t.key;n=n.filter((t=>!t.key.startsWith(e)))})),e.length===n.length)return;const o=this.state.reconfigure({plugins:n});return this.view.updateState(o),o}createExtensionManager(){var t,e;const n=[...this.options.enableCoreExtensions?[Pe,Qt.configure({blockSeparator:null===(e=null===(t=this.options.coreExtensionOptions)||void 0===t?void 0:t.clipboardTextSerializer)||void 0===e?void 0:e.blockSeparator}),Ce,Ne,_e,je,He,Ie].filter((t=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[t.name])):[],...this.options.extensions].filter((t=>["extension","node","mark"].includes(null==t?void 0:t.type)));this.extensionManager=new Ut(n,this)}createCommandManager(){this.commandManager=new vt({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var t;let e;try{e=pe(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(s){if(!(s instanceof Error&&["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(s.message)))throw s;this.emit("contentError",{editor:this,error:s,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter((t=>"collaboration"!==t.name)),this.createExtensionManager()}}),e=pe(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}const n=ne(e,this.options.autofocus);this.view=new l(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null===(t=this.options.editorProps)||void 0===t?void 0:t.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:a.create({doc:e,selection:n||void 0})});const o=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(o),this.createNodeViews(),this.prependClass();this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(t){this.isCapturingTransaction=!0,t(),this.isCapturingTransaction=!1;const e=this.capturedTransaction;return this.capturedTransaction=null,e}dispatchTransaction(t){if(this.view.isDestroyed)return;if(this.isCapturingTransaction)return this.capturedTransaction?void t.steps.forEach((t=>{var e;return null===(e=this.capturedTransaction)||void 0===e?void 0:e.step(t)})):void(this.capturedTransaction=t);const e=this.state.apply(t),n=!this.state.selection.eq(e.selection);this.emit("beforeTransaction",{editor:this,transaction:t,nextState:e}),this.view.updateState(e),this.emit("transaction",{editor:this,transaction:t}),n&&this.emit("selectionUpdate",{editor:this,transaction:t});const o=t.getMeta("focus"),s=t.getMeta("blur");o&&this.emit("focus",{editor:this,event:o.event,transaction:t}),s&&this.emit("blur",{editor:this,event:s.event,transaction:t}),t.docChanged&&!t.getMeta("preventUpdate")&&this.emit("update",{editor:this,transaction:t})}getAttributes(t){return ye(this.state,t)}isActive(t,e){const n="string"==typeof t?t:null,o="string"==typeof t?e:t;return function(t,e,n={}){if(!e)return de(t,null,n)||we(t,null,n);const o=ce(e,t.schema);return"node"===o?de(t,e,n):"mark"===o&&we(t,e,n)}(this.state,n,o)}getJSON(){return this.state.doc.toJSON()}getHTML(){return Pt(this.state.doc.content,this.schema)}getText(t){const{blockSeparator:e="\n\n",textSerializers:n={}}=t||{};return ge(this.state.doc,{blockSeparator:e,textSerializers:{...Ft(this.schema),...n}})}get isEmpty(){return xe(this.state.doc)}getCharacterCount(){return this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){const t=this.view.dom;t&&t.editor&&delete t.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var t;return!(null===(t=this.view)||void 0===t?void 0:t.docView)}$node(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelector(t,e))||null}$nodes(t,e){var n;return(null===(n=this.$doc)||void 0===n?void 0:n.querySelectorAll(t,e))||null}$pos(t){const e=this.state.doc.resolve(t);return new Re(e,this)}get $doc(){return this.$pos(0)}};function ze(t){return new Nt({find:t.find,handler:({state:e,range:n,match:o})=>{const s=At(t.getAttributes,void 0,o);if(!1===s||null===s)return null;const{tr:r}=e,i=o[o.length-1],a=o[0];if(i){const o=a.search(/\S/),l=n.from+a.indexOf(i),d=l+i.length;if(be(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>l)).length)return null;d<n.to&&r.delete(d,n.to),l>n.from&&r.delete(n.from+o,l);const c=n.from+o+i.length;r.addMark(n.from+o,c,t.type.create(s||{})),r.removeStoredMark(t.type)}}})}function qe(t){return new Nt({find:t.find,handler:({state:e,range:n,match:o})=>{const s=At(t.getAttributes,void 0,o)||{},{tr:r}=e,i=n.from;let a=n.to;const l=t.type.create(s);if(o[1]){let t=i+o[0].lastIndexOf(o[1]);t>a?t=a:a=t+o[1].length;const e=o[0][o[0].length-1];r.insertText(e,i+o[0].length-1),r.replaceWith(t,a,l)}else if(o[0]){const e=t.type.isInline?i:i-1;r.insert(e,t.type.create(s)).delete(r.mapping.map(i),r.mapping.map(a))}r.scrollIntoView()}})}function Ke(t){return new Nt({find:t.find,handler:({state:e,range:n,match:o})=>{const s=e.doc.resolve(n.from),r=At(t.getAttributes,void 0,o)||{};if(!s.node(-1).canReplaceWith(s.index(-1),s.indexAfter(-1),t.type))return null;e.tr.delete(n.from,n.to).setBlockType(n.from,n.from,t.type,r)}})}function Ue(t){return new Nt({find:t.find,handler:({state:e,range:n,match:o})=>{let s=t.replace,r=n.from;const i=n.to;if(o[1]){const t=o[0].lastIndexOf(o[1]);s+=o[0].slice(t+o[1].length),r+=t;const e=r-i;e>0&&(s=o[0].slice(t-e,t)+s,r=i)}e.tr.insertText(s,r,i)}})}function Ve(t){return new Nt({find:t.find,handler:({state:e,range:n,match:o,chain:s})=>{const r=At(t.getAttributes,void 0,o)||{},i=e.tr.delete(n.from,n.to),a=i.doc.resolve(n.from).blockRange(),l=a&&z(a,t.type,r);if(!l)return null;if(i.wrap(a,l),t.keepMarks&&t.editor){const{selection:n,storedMarks:o}=e,{splittableMarks:s}=t.editor.extensionManager,r=o||n.$to.parentOffset&&n.$from.marks();if(r){const t=r.filter((t=>s.includes(t.type.name)));i.ensureMarks(t)}}if(t.keepAttributes){const e="bulletList"===t.type.name||"orderedList"===t.type.name?"listItem":"taskList";s().updateAttributes(e,r).run()}const d=i.doc.resolve(n.from-1).nodeBefore;d&&d.type===t.type&&q(i.doc,n.from-1)&&(!t.joinPredicate||t.joinPredicate(o,d))&&i.join(n.from-1)}})}class We{constructor(t={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...t},this.name=this.config.name,t.defaultOptions&&Object.keys(t.defaultOptions).length,this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=At(kt(this,"addOptions",{name:this.name}))),this.storage=At(kt(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(t={}){return new We(t)}configure(t={}){const e=this.extend({...this.config,addOptions:()=>Dt(this.options,t)});return e.name=this.name,e.parent=this.parent,e}extend(t={}){const e=new We(t);return e.parent=this,this.child=e,e.name=t.name?t.name:e.parent.name,t.defaultOptions&&Object.keys(t.defaultOptions).length,e.options=At(kt(e,"addOptions",{name:e.name})),e.storage=At(kt(e,"addStorage",{name:e.name,options:e.options})),e}}class Fe{constructor(t,e,n){this.isDragging=!1,this.component=t,this.editor=e.editor,this.options={stopEvent:null,ignoreMutation:null,...n},this.extension=e.extension,this.node=e.node,this.decorations=e.decorations,this.innerDecorations=e.innerDecorations,this.view=e.view,this.HTMLAttributes=e.HTMLAttributes,this.getPos=e.getPos,this.mount()}mount(){}get dom(){return this.editor.view.dom}get contentDOM(){return null}onDragStart(t){var n,o,s,r,i,a,l;const{view:d}=this.editor,c=t.target,u=3===c.nodeType?null===(n=c.parentElement)||void 0===n?void 0:n.closest("[data-drag-handle]"):c.closest("[data-drag-handle]");if(!this.dom||(null===(o=this.contentDOM)||void 0===o?void 0:o.contains(c))||!u)return;let p=0,h=0;if(this.dom!==u){const e=this.dom.getBoundingClientRect(),n=u.getBoundingClientRect(),o=null!==(s=t.offsetX)&&void 0!==s?s:null===(r=t.nativeEvent)||void 0===r?void 0:r.offsetX,l=null!==(i=t.offsetY)&&void 0!==i?i:null===(a=t.nativeEvent)||void 0===a?void 0:a.offsetY;p=n.x-e.x+o,h=n.y-e.y+l}const m=this.dom.cloneNode(!0);null===(l=t.dataTransfer)||void 0===l||l.setDragImage(m,p,h);const f=this.getPos();if("number"!=typeof f)return;const g=e.create(d.state.doc,f),y=d.state.tr.setSelection(g);d.dispatch(y)}stopEvent(t){var n;if(!this.dom)return!1;if("function"==typeof this.options.stopEvent)return this.options.stopEvent({event:t});const o=t.target;if(!(this.dom.contains(o)&&!(null===(n=this.contentDOM)||void 0===n?void 0:n.contains(o))))return!1;const s=t.type.startsWith("drag"),r="drop"===t.type;if((["INPUT","BUTTON","SELECT","TEXTAREA"].includes(o.tagName)||o.isContentEditable)&&!r&&!s)return!0;const{isEditable:i}=this.editor,{isDragging:a}=this,l=!!this.node.type.spec.draggable,d=e.isSelectable(this.node),c="copy"===t.type,u="paste"===t.type,p="cut"===t.type,h="mousedown"===t.type;if(!l&&d&&s&&t.target===this.dom&&t.preventDefault(),l&&s&&!a&&t.target===this.dom)return t.preventDefault(),!1;if(l&&i&&!a&&h){const t=o.closest("[data-drag-handle]");t&&(this.dom===t||this.dom.contains(t))&&(this.isDragging=!0,document.addEventListener("dragend",(()=>{this.isDragging=!1}),{once:!0}),document.addEventListener("drop",(()=>{this.isDragging=!1}),{once:!0}),document.addEventListener("mouseup",(()=>{this.isDragging=!1}),{once:!0}))}return!(a||r||c||u||p||h&&d)}ignoreMutation(t){if(!this.dom||!this.contentDOM)return!0;if("function"==typeof this.options.ignoreMutation)return this.options.ignoreMutation({mutation:t});if(this.node.isLeaf||this.node.isAtom)return!0;if("selection"===t.type)return!1;if(this.dom.contains(t.target)&&"childList"===t.type&&(se()||oe())&&this.editor.isFocused){if([...Array.from(t.addedNodes),...Array.from(t.removedNodes)].every((t=>t.isContentEditable)))return!1}return this.contentDOM===t.target&&"attributes"===t.type||!this.contentDOM.contains(t.target)}updateAttributes(t){this.editor.commands.command((({tr:e})=>{const n=this.getPos();return"number"==typeof n&&(e.setNodeMarkup(n,void 0,{...this.node.attrs,...t}),!0)}))}deleteNode(){const t=this.getPos();if("number"!=typeof t)return;const e=t+this.node.nodeSize;this.editor.commands.deleteRange({from:t,to:e})}}function Qe(t){return new Bt({find:t.find,handler:({state:e,range:n,match:o,pasteEvent:s})=>{const r=At(t.getAttributes,void 0,o,s);if(!1===r||null===r)return null;const{tr:i}=e,a=o[o.length-1],l=o[0];let d=n.to;if(a){const o=l.search(/\S/),s=n.from+l.indexOf(a),c=s+a.length;if(be(n.from,n.to,e.doc).filter((e=>e.mark.type.excluded.find((n=>n===t.type&&n!==e.mark.type)))).filter((t=>t.to>s)).length)return null;c<n.to&&i.delete(c,n.to),s>n.from&&i.delete(n.from+o,s),d=n.from+o+a.length,i.addMark(n.from+o,d,t.type.create(r||{})),i.removeStoredMark(t.type)}}})}const Ge=/^\s*>\s$/,Je=We.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[Ve({find:Ge,type:this.type})]}}),Xe=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,Ze=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,Ye=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,tn=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,en=Rt.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[ze({find:Xe,type:this.type}),ze({find:Ye,type:this.type})]},addPasteRules(){return[Qe({find:Ze,type:this.type}),Qe({find:tn,type:this.type})]}});class nn{constructor({editor:t,element:e,view:n,tippyOptions:o={},updateDelay:s=250,shouldShow:r}){this.preventHide=!1,this.shouldShow=({view:t,state:e,from:n,to:o})=>{const{doc:s,selection:r}=e,{empty:i}=r,a=!s.textBetween(n,o).length&&te(e.selection),l=this.element.contains(document.activeElement);return!(!t.hasFocus()&&!l||i||a||!this.editor.isEditable)},this.mousedownHandler=()=>{this.preventHide=!0},this.dragstartHandler=()=>{this.hide()},this.focusHandler=()=>{setTimeout((()=>this.update(this.editor.view)))},this.blurHandler=({event:t})=>{var e;this.preventHide?this.preventHide=!1:(null==t?void 0:t.relatedTarget)&&(null===(e=this.element.parentNode)||void 0===e?void 0:e.contains(t.relatedTarget))||(null==t?void 0:t.relatedTarget)!==this.editor.view.dom&&this.hide()},this.tippyBlurHandler=t=>{this.blurHandler({event:t})},this.handleDebouncedUpdate=(t,e)=>{const n=!(null==e?void 0:e.selection.eq(t.state.selection)),o=!(null==e?void 0:e.doc.eq(t.state.doc));(n||o)&&(this.updateDebounceTimer&&clearTimeout(this.updateDebounceTimer),this.updateDebounceTimer=window.setTimeout((()=>{this.updateHandler(t,n,o,e)}),this.updateDelay))},this.updateHandler=(t,e,n,o)=>{var s,r,i;const{state:a,composing:l}=t,{selection:d}=a;if(l||!e&&!n)return;this.createTooltip();const{ranges:c}=d,u=Math.min(...c.map((t=>t.$from.pos))),p=Math.max(...c.map((t=>t.$to.pos)));(null===(s=this.shouldShow)||void 0===s?void 0:s.call(this,{editor:this.editor,element:this.element,view:t,state:a,oldState:o,from:u,to:p}))?(null===(r=this.tippy)||void 0===r||r.setProps({getReferenceClientRect:(null===(i=this.tippyOptions)||void 0===i?void 0:i.getReferenceClientRect)||(()=>{if(Te(a.selection)){let e=t.nodeDOM(u);if(e){const t=e.dataset.nodeViewWrapper?e:e.querySelector("[data-node-view-wrapper]");if(t&&(e=t.firstChild),e)return e.getBoundingClientRect()}}return Se(t,u,p)})}),this.show()):this.hide()},this.editor=t,this.element=e,this.view=n,this.updateDelay=s,r&&(this.shouldShow=r),this.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.view.dom.addEventListener("dragstart",this.dragstartHandler),this.editor.on("focus",this.focusHandler),this.editor.on("blur",this.blurHandler),this.tippyOptions=o,this.element.remove(),this.element.style.visibility="visible"}createTooltip(){const{element:t}=this.editor.options,e=!!t.parentElement;!this.tippy&&e&&(this.tippy=b(t,{duration:0,getReferenceClientRect:null,content:this.element,interactive:!0,trigger:"manual",placement:"top",hideOnClick:"toggle",...this.tippyOptions}),this.tippy.popper.firstChild&&this.tippy.popper.firstChild.addEventListener("blur",this.tippyBlurHandler))}update(t,e){const{state:n}=t,o=n.selection.from!==n.selection.to;if(this.updateDelay>0&&o)return void this.handleDebouncedUpdate(t,e);const s=!(null==e?void 0:e.selection.eq(t.state.selection)),r=!(null==e?void 0:e.doc.eq(t.state.doc));this.updateHandler(t,s,r,e)}show(){var t;null===(t=this.tippy)||void 0===t||t.show()}hide(){var t;null===(t=this.tippy)||void 0===t||t.hide()}destroy(){var t,e;(null===(t=this.tippy)||void 0===t?void 0:t.popper.firstChild)&&this.tippy.popper.firstChild.removeEventListener("blur",this.tippyBlurHandler),null===(e=this.tippy)||void 0===e||e.destroy(),this.element.removeEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.view.dom.removeEventListener("dragstart",this.dragstartHandler),this.editor.off("focus",this.focusHandler),this.editor.off("blur",this.blurHandler)}}const on=t=>new o({key:"string"==typeof t.pluginKey?new n(t.pluginKey):t.pluginKey,view:e=>new nn({view:e,...t})}),sn=Vt.create({name:"bubbleMenu",addOptions:()=>({element:null,tippyOptions:{},pluginKey:"bubbleMenu",updateDelay:void 0,shouldShow:null}),addProseMirrorPlugins(){return this.options.element?[on({pluginKey:this.options.pluginKey,editor:this.editor,element:this.options.element,tippyOptions:this.options.tippyOptions,updateDelay:this.options.updateDelay,shouldShow:this.options.shouldShow})]:[]}}),rn="textStyle",an=/^\s*([-+*])\s$/,ln=We.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(rn)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=Ve({find:an,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Ve({find:an,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(rn),editor:this.editor})),[t]}}),dn=/(^|[^`])`([^`]+)`(?!`)/,cn=/(^|[^`])`([^`]+)`(?!`)/g,un=Rt.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:t}){return["code",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[ze({find:dn,type:this.type})]},addPasteRules(){return[Qe({find:cn,type:this.type})]}}),pn=Rt.create({name:"textStyle",priority:101,addOptions:()=>({HTMLAttributes:{},mergeNestedSpanStyles:!1}),parseHTML(){return[{tag:"span",getAttrs:t=>!!t.hasAttribute("style")&&(this.options.mergeNestedSpanStyles&&(t=>{if(!t.children.length)return;const e=t.querySelectorAll("span");e&&e.forEach((t=>{var e,n;const o=t.getAttribute("style"),s=null===(n=null===(e=t.parentElement)||void 0===e?void 0:e.closest("span"))||void 0===n?void 0:n.getAttribute("style");t.setAttribute("style",`${s};${o}`)}))})(t),{})}]},renderHTML({HTMLAttributes:t}){return["span",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{removeEmptyTextStyle:()=>({tr:t})=>{const{selection:e}=t;return t.doc.nodesBetween(e.from,e.to,((e,n)=>{if(e.isTextblock)return!0;e.marks.filter((t=>t.type===this.type)).some((t=>Object.values(t.attrs).some((t=>!!t))))||t.removeMark(n,n+e.nodeSize,this.type)})),!0}}}}),hn=Vt.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:t=>{var e;return null===(e=t.style.color)||void 0===e?void 0:e.replace(/['"]+/g,"")},renderHTML:t=>t.color?{style:`color: ${t.color}`}:{}}}}]},addCommands:()=>({setColor:t=>({chain:e})=>e().setMark("textStyle",{color:t}).run(),unsetColor:()=>({chain:t})=>t().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})}),mn=We.create({name:"doc",topNode:!0,content:"block+"}),fn=Vt.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[t(this.options)]}});class gn{getTextContent(t){return ge(t,{textSerializers:Ft(this.editor.schema)})}constructor({editor:t,element:e,view:n,tippyOptions:o={},shouldShow:s}){this.preventHide=!1,this.shouldShow=({view:t,state:e})=>{const{selection:n}=e,{$anchor:o,empty:s}=n,r=1===o.depth,i=o.parent.isTextblock&&!o.parent.type.spec.code&&!o.parent.textContent&&0===o.parent.childCount&&!this.getTextContent(o.parent);return!!(t.hasFocus()&&s&&r&&i&&this.editor.isEditable)},this.mousedownHandler=()=>{this.preventHide=!0},this.focusHandler=()=>{setTimeout((()=>this.update(this.editor.view)))},this.blurHandler=({event:t})=>{var e;this.preventHide?this.preventHide=!1:(null==t?void 0:t.relatedTarget)&&(null===(e=this.element.parentNode)||void 0===e?void 0:e.contains(t.relatedTarget))||(null==t?void 0:t.relatedTarget)!==this.editor.view.dom&&this.hide()},this.tippyBlurHandler=t=>{this.blurHandler({event:t})},this.editor=t,this.element=e,this.view=n,s&&(this.shouldShow=s),this.element.addEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.editor.on("focus",this.focusHandler),this.editor.on("blur",this.blurHandler),this.tippyOptions=o,this.element.remove(),this.element.style.visibility="visible"}createTooltip(){const{element:t}=this.editor.options,e=!!t.parentElement;!this.tippy&&e&&(this.tippy=b(t,{duration:0,getReferenceClientRect:null,content:this.element,interactive:!0,trigger:"manual",placement:"right",hideOnClick:"toggle",...this.tippyOptions}),this.tippy.popper.firstChild&&this.tippy.popper.firstChild.addEventListener("blur",this.tippyBlurHandler))}update(t,e){var n,o,s;const{state:r}=t,{doc:i,selection:a}=r,{from:l,to:d}=a;if(e&&e.doc.eq(i)&&e.selection.eq(a))return;this.createTooltip();(null===(n=this.shouldShow)||void 0===n?void 0:n.call(this,{editor:this.editor,view:t,state:r,oldState:e}))?(null===(o=this.tippy)||void 0===o||o.setProps({getReferenceClientRect:(null===(s=this.tippyOptions)||void 0===s?void 0:s.getReferenceClientRect)||(()=>Se(t,l,d))}),this.show()):this.hide()}show(){var t;null===(t=this.tippy)||void 0===t||t.show()}hide(){var t;null===(t=this.tippy)||void 0===t||t.hide()}destroy(){var t,e;(null===(t=this.tippy)||void 0===t?void 0:t.popper.firstChild)&&this.tippy.popper.firstChild.removeEventListener("blur",this.tippyBlurHandler),null===(e=this.tippy)||void 0===e||e.destroy(),this.element.removeEventListener("mousedown",this.mousedownHandler,{capture:!0}),this.editor.off("focus",this.focusHandler),this.editor.off("blur",this.blurHandler)}}const yn=t=>new o({key:"string"==typeof t.pluginKey?new n(t.pluginKey):t.pluginKey,view:e=>new gn({view:e,...t})}),vn=Vt.create({name:"floatingMenu",addOptions:()=>({element:null,tippyOptions:{},pluginKey:"floatingMenu",shouldShow:null}),addProseMirrorPlugins(){return this.options.element?[yn({pluginKey:this.options.pluginKey,editor:this.editor,element:this.options.element,tippyOptions:this.options.tippyOptions,shouldShow:this.options.shouldShow})]:[]}}),bn=Vt.create({name:"focus",addOptions:()=>({className:"has-focus",mode:"all"}),addProseMirrorPlugins(){return[new o({key:new n("focus"),props:{decorations:({doc:t,selection:e})=>{const{isEditable:n,isFocused:o}=this.editor,{anchor:s}=e,r=[];if(!n||!o)return d.create(t,[]);let i=0;"deepest"===this.options.mode&&t.descendants(((t,e)=>{if(t.isText)return;if(!(s>=e&&s<=e+t.nodeSize-1))return!1;i+=1}));let a=0;return t.descendants(((t,e)=>{if(t.isText)return!1;if(!(s>=e&&s<=e+t.nodeSize-1))return!1;a+=1;if("deepest"===this.options.mode&&i-a>0||"shallowest"===this.options.mode&&a>1)return"deepest"===this.options.mode;r.push(c.node(e,e+t.nodeSize,{class:this.options.className}))})),d.create(t,r)}}})]}}),kn=Vt.create({name:"gapCursor",addProseMirrorPlugins:()=>[u()],extendNodeSchema(t){var e;return{allowGapCursor:null!==(e=At(kt(t,"allowGapCursor",{name:t.name,options:t.options,storage:t.storage})))&&void 0!==e?e:null}}}),wn=We.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map((t=>({tag:`h${t}`,attrs:{level:t}})))},renderHTML({node:t,HTMLAttributes:e}){return[`h${this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0]}`,Tt(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce(((t,e)=>({...t,[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})})),{})},addInputRules(){return this.options.levels.map((t=>Ke({find:new RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}})))}}),Mn=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,xn=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,Tn=Rt.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:t}){return["mark",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[ze({find:Mn,type:this.type})]},addPasteRules(){return[Qe({find:xn,type:this.type})]}}),Sn=Vt.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>m(t,e),redo:()=>({state:t,dispatch:e})=>h(t,e)}),addProseMirrorPlugins(){return[p(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),En=We.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",Tt(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:n})=>{const{selection:o}=n,{$from:s,$to:i}=o,a=t();return 0===s.parentOffset?a.insertContentAt({from:Math.max(s.pos-1,0),to:i.pos},{type:this.name}):Te(o)?a.insertContentAt(i.pos,{type:this.name}):a.insertContent({type:this.name}),a.command((({tr:t,dispatch:n})=>{var o;if(n){const{$to:n}=t.selection,s=n.end();if(n.nodeAfter)n.nodeAfter.isTextblock?t.setSelection(r.create(t.doc,n.pos+1)):n.nodeAfter.isBlock?t.setSelection(e.create(t.doc,n.pos)):t.setSelection(r.create(t.doc,n.pos));else{const e=null===(o=n.parent.type.contentMatch.defaultType)||void 0===o?void 0:o.create();e&&(t.insert(s,e),t.setSelection(r.create(t.doc,s+1)))}t.scrollIntoView()}return!0})).run()}}},addInputRules(){return[qe({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),An=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,On=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,Ln=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,Cn=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,Hn=Rt.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[ze({find:An,type:this.type}),ze({find:Ln,type:this.type})]},addPasteRules(){return[Qe({find:On,type:this.type}),Qe({find:Cn,type:this.type})]}});function Pn(t){return new o({key:new n("autolink"),appendTransaction:(e,n,o)=>{const s=e.some((t=>t.docChanged))&&!n.doc.eq(o.doc),r=e.some((t=>t.getMeta("preventAutolink")));if(!s||r)return;const{tr:i}=o,a=function(t,e){const n=new K(t);return e.forEach((t=>{t.steps.forEach((t=>{n.step(t)}))})),n}(n.doc,[...e]),l=function(t){const{mapping:e,steps:n}=t,o=[];return e.maps.forEach(((t,s)=>{const r=[];if(t.ranges.length)t.forEach(((t,e)=>{r.push({from:t,to:e})}));else{const{from:t,to:e}=n[s];if(void 0===t||void 0===e)return;r.push({from:t,to:e})}r.forEach((({from:t,to:n})=>{const r=e.slice(s).map(t,-1),i=e.slice(s).map(n),a=e.invert().map(r,-1),l=e.invert().map(i);o.push({oldRange:{from:a,to:l},newRange:{from:r,to:i}})}))})),ve(o)}(a);return l.forEach((({newRange:e})=>{const n=function(t,e,n){const o=[];return t.nodesBetween(e.from,e.to,((t,e)=>{n(t)&&o.push({node:t,pos:e})})),o}(o.doc,e,(t=>t.isTextblock));let s,r;if(n.length>1?(s=n[0],r=o.doc.textBetween(s.pos,s.pos+s.node.nodeSize,void 0," ")):n.length&&o.doc.textBetween(e.from,e.to," "," ").endsWith(" ")&&(s=n[0],r=o.doc.textBetween(s.pos,e.to,void 0," ")),s&&r){const e=r.split(" ").filter((t=>""!==t));if(e.length<=0)return!1;const n=e[e.length-1],l=s.pos+r.lastIndexOf(n);if(!n)return!1;const d=y(n).map((e=>e.toObject(t.defaultProtocol)));if(!(1===(a=d).length?a[0].isLink:3===a.length&&a[1].isLink&&["()","[]"].includes(a[0].value+a[2].value)))return!1;d.filter((t=>t.isLink)).map((t=>({...t,from:l+t.start+1,to:l+t.end+1}))).filter((t=>!o.schema.marks.code||!o.doc.rangeHasMark(t.from,t.to,o.schema.marks.code))).filter((e=>t.validate(e.value))).filter((e=>t.shouldAutoLink(e.value))).forEach((e=>{be(e.from,e.to,o.doc).some((e=>e.mark.type===t.type))||i.addMark(e.from,e.to,t.type.create({href:e.href}))}))}var a})),i.steps.length?i:void 0}})}const $n=/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g;function Nn(t,e){const n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach((t=>{const e="string"==typeof t?t:t.scheme;e&&n.push(e)})),!t||t.replace($n,"").match(new RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}const _n=Rt.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate),this.options.protocols.forEach((t=>{"string"!=typeof t?g(t.scheme,t.optionalSlashes):g(t)}))},onDestroy(){v()},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!Nn(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}),addAttributes(){return{href:{default:null,parseHTML:t=>t.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{const e=t.getAttribute("href");return!(!e||!this.options.isAllowedUri(e,{defaultValidate:t=>!!Nn(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:t=>!!Nn(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",Tt(this.options.HTMLAttributes,t),0]:["a",Tt(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!Nn(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().setMark(this.name,t).setMeta("preventAutolink",!0).run()},toggleLink:t=>({chain:e})=>{const{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!Nn(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[Qe({find:t=>{const e=[];if(t){const{protocols:n,defaultProtocol:o}=this.options,s=f(t).filter((t=>t.isLink&&this.options.isAllowedUri(t.value,{defaultValidate:t=>!!Nn(t,n),protocols:n,defaultProtocol:o})));s.length&&s.forEach((t=>e.push({text:t.value,data:{href:t.href},index:t.start})))}return e},type:this.type,getAttributes:t=>{var e;return{href:null===(e=t.data)||void 0===e?void 0:e.href}}})]},addProseMirrorPlugins(){const t=[],{protocols:e,defaultProtocol:s}=this.options;var r;return this.options.autolink&&t.push(Pn({type:this.type,defaultProtocol:this.options.defaultProtocol,validate:t=>this.options.isAllowedUri(t,{defaultValidate:t=>!!Nn(t,e),protocols:e,defaultProtocol:s}),shouldAutoLink:this.options.shouldAutoLink})),!0===this.options.openOnClick&&t.push((r={type:this.type},new o({key:new n("handleClickLink"),props:{handleClick:(t,e,n)=>{var o,s;if(0!==n.button)return!1;if(!t.editable)return!1;let i=n.target;const a=[];for(;"DIV"!==i.nodeName;)a.push(i),i=i.parentNode;if(!a.find((t=>"A"===t.nodeName)))return!1;const l=ye(t.state,r.type.name),d=n.target,c=null!==(o=null==d?void 0:d.href)&&void 0!==o?o:l.href,u=null!==(s=null==d?void 0:d.target)&&void 0!==s?s:l.target;return!(!d||!c||(window.open(c,u),0))}}}))),this.options.linkOnPaste&&t.push(function(t){return new o({key:new n("handlePasteLink"),props:{handlePaste:(e,n,o)=>{const{state:s}=e,{selection:r}=s,{empty:i}=r;if(i)return!1;let a="";o.content.forEach((t=>{a+=t.textContent}));const l=f(a,{defaultProtocol:t.defaultProtocol}).find((t=>t.isLink&&t.value===a));return!(!a||!l)&&t.editor.commands.setMark(t.type,{href:l.href})}}})}({editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type})),t}}),In=We.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",Tt(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),jn="textStyle",Dn=/^(\d+)\.\s$/,Rn=We.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){const{start:e,...n}=t;return 1===e?["ol",Tt(this.options.HTMLAttributes,n),0]:["ol",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(jn)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=Ve({find:Dn,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=Ve({find:Dn,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(jn)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),Bn=We.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),zn=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,qn=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,Kn=Rt.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("line-through")&&{}}],renderHTML({HTMLAttributes:t}){return["s",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[ze({find:zn,type:this.type})]},addPasteRules(){return[Qe({find:qn,type:this.type})]}}),Un=We.create({name:"taskList",addOptions:()=>({itemTypeName:"taskItem",HTMLAttributes:{}}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:t}){return["ul",Tt(this.options.HTMLAttributes,t,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:t})=>t.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}}),Vn=We.create({name:"text",group:"inline"}),Wn=Vt.create({name:"textAlign",addOptions:()=>({types:[],alignments:["left","center","right","justify"],defaultAlignment:null}),addGlobalAttributes(){return[{types:this.options.types,attributes:{textAlign:{default:this.options.defaultAlignment,parseHTML:t=>{const e=t.style.textAlign;return this.options.alignments.includes(e)?e:this.options.defaultAlignment},renderHTML:t=>t.textAlign?{style:`text-align: ${t.textAlign}`}:{}}}}]},addCommands(){return{setTextAlign:t=>({commands:e})=>!!this.options.alignments.includes(t)&&this.options.types.map((n=>e.updateAttributes(n,{textAlign:t}))).every((t=>t)),unsetTextAlign:()=>({commands:t})=>this.options.types.map((e=>t.resetAttributes(e,"textAlign"))).every((t=>t)),toggleTextAlign:t=>({editor:e,commands:n})=>!!this.options.alignments.includes(t)&&(e.isActive({textAlign:t})?n.unsetTextAlign():n.setTextAlign(t))}},addKeyboardShortcuts(){return{"Mod-Shift-l":()=>this.editor.commands.setTextAlign("left"),"Mod-Shift-e":()=>this.editor.commands.setTextAlign("center"),"Mod-Shift-r":()=>this.editor.commands.setTextAlign("right"),"Mod-Shift-j":()=>this.editor.commands.setTextAlign("justify")}}}),Fn=Vt.create({name:"typography",addOptions:()=>({closeDoubleQuote:"”",closeSingleQuote:"’",copyright:"©",ellipsis:"…",emDash:"—",laquo:"«",leftArrow:"←",multiplication:"×",notEqual:"≠",oneHalf:"½",oneQuarter:"¼",openDoubleQuote:"“",openSingleQuote:"‘",plusMinus:"±",raquo:"»",registeredTrademark:"®",rightArrow:"→",servicemark:"℠",superscriptThree:"³",superscriptTwo:"²",threeQuarters:"¾",trademark:"™"}),addInputRules(){const t=[];var e;return!1!==this.options.emDash&&t.push(Ue({find:/--$/,replace:null!=(e=this.options.emDash)?e:"—"})),!1!==this.options.ellipsis&&t.push((t=>Ue({find:/\.\.\.$/,replace:null!=t?t:"…"}))(this.options.ellipsis)),!1!==this.options.openDoubleQuote&&t.push((t=>Ue({find:/(?:^|[\s{[(<'"\u2018\u201C])(")$/,replace:null!=t?t:"“"}))(this.options.openDoubleQuote)),!1!==this.options.closeDoubleQuote&&t.push((t=>Ue({find:/"$/,replace:null!=t?t:"”"}))(this.options.closeDoubleQuote)),!1!==this.options.openSingleQuote&&t.push((t=>Ue({find:/(?:^|[\s{[(<'"\u2018\u201C])(')$/,replace:null!=t?t:"‘"}))(this.options.openSingleQuote)),!1!==this.options.closeSingleQuote&&t.push((t=>Ue({find:/'$/,replace:null!=t?t:"’"}))(this.options.closeSingleQuote)),!1!==this.options.leftArrow&&t.push((t=>Ue({find:/<-$/,replace:null!=t?t:"←"}))(this.options.leftArrow)),!1!==this.options.rightArrow&&t.push((t=>Ue({find:/->$/,replace:null!=t?t:"→"}))(this.options.rightArrow)),!1!==this.options.copyright&&t.push((t=>Ue({find:/\(c\)$/,replace:null!=t?t:"©"}))(this.options.copyright)),!1!==this.options.trademark&&t.push((t=>Ue({find:/\(tm\)$/,replace:null!=t?t:"™"}))(this.options.trademark)),!1!==this.options.servicemark&&t.push((t=>Ue({find:/\(sm\)$/,replace:null!=t?t:"℠"}))(this.options.servicemark)),!1!==this.options.registeredTrademark&&t.push((t=>Ue({find:/\(r\)$/,replace:null!=t?t:"®"}))(this.options.registeredTrademark)),!1!==this.options.oneHalf&&t.push((t=>Ue({find:/(?:^|\s)(1\/2)\s$/,replace:null!=t?t:"½"}))(this.options.oneHalf)),!1!==this.options.plusMinus&&t.push((t=>Ue({find:/\+\/-$/,replace:null!=t?t:"±"}))(this.options.plusMinus)),!1!==this.options.notEqual&&t.push((t=>Ue({find:/!=$/,replace:null!=t?t:"≠"}))(this.options.notEqual)),!1!==this.options.laquo&&t.push((t=>Ue({find:/<<$/,replace:null!=t?t:"«"}))(this.options.laquo)),!1!==this.options.raquo&&t.push((t=>Ue({find:/>>$/,replace:null!=t?t:"»"}))(this.options.raquo)),!1!==this.options.multiplication&&t.push((t=>Ue({find:/\d+\s?([*x])\s?\d+$/,replace:null!=t?t:"×"}))(this.options.multiplication)),!1!==this.options.superscriptTwo&&t.push((t=>Ue({find:/\^2$/,replace:null!=t?t:"²"}))(this.options.superscriptTwo)),!1!==this.options.superscriptThree&&t.push((t=>Ue({find:/\^3$/,replace:null!=t?t:"³"}))(this.options.superscriptThree)),!1!==this.options.oneQuarter&&t.push((t=>Ue({find:/(?:^|\s)(1\/4)\s$/,replace:null!=t?t:"¼"}))(this.options.oneQuarter)),!1!==this.options.threeQuarters&&t.push((t=>Ue({find:/(?:^|\s)(3\/4)\s$/,replace:null!=t?t:"¾"}))(this.options.threeQuarters)),t}}),Qn=Rt.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("underline")&&{}}],renderHTML({HTMLAttributes:t}){return["u",Tt(this.options.HTMLAttributes,t),0]},addCommands(){return{setUnderline:()=>({commands:t})=>t.setMark(this.name),toggleUnderline:()=>({commands:t})=>t.toggleMark(this.name),unsetUnderline:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),Gn=w({name:"BubbleMenu",props:{pluginKey:{type:[String,Object],default:"bubbleMenu"},editor:{type:Object,required:!0},updateDelay:{type:Number,default:void 0},tippyOptions:{type:Object,default:()=>({})},shouldShow:{type:Function,default:null}},setup(t,{slots:e}){const n=k(null);return A((()=>{const{updateDelay:e,editor:o,pluginKey:s,shouldShow:r,tippyOptions:i}=t;o.registerPlugin(on({updateDelay:e,editor:o,element:n.value,pluginKey:s,shouldShow:r,tippyOptions:i}))})),O((()=>{const{pluginKey:e,editor:n}=t;n.unregisterPlugin(e)})),()=>{var t;return S("div",{ref:n},null===(t=e.default)||void 0===t?void 0:t.call(e))}}});function Jn(t){return $(((e,n)=>({get:()=>(e(),t),set(e){t=e,requestAnimationFrame((()=>{requestAnimationFrame((()=>{n()}))}))}})))}class Xn extends Be{constructor(t={}){return super(t),this.contentComponent=null,this.appContext=null,this.reactiveState=Jn(this.view.state),this.reactiveExtensionStorage=Jn(this.extensionStorage),this.on("beforeTransaction",(({nextState:t})=>{this.reactiveState.value=t,this.reactiveExtensionStorage.value=this.extensionStorage})),x(this)}get state(){return this.reactiveState?this.reactiveState.value:this.view.state}get storage(){return this.reactiveExtensionStorage?this.reactiveExtensionStorage.value:super.storage}registerPlugin(t,e){const n=super.registerPlugin(t,e);return this.reactiveState&&(this.reactiveState.value=n),n}unregisterPlugin(t){const e=super.unregisterPlugin(t);return this.reactiveState&&e&&(this.reactiveState.value=e),e}}const Zn=w({name:"EditorContent",props:{editor:{default:null,type:Object}},setup(t){const e=k(),n=P();return L((()=>{const o=t.editor;o&&o.options.element&&e.value&&C((()=>{if(!e.value||!o.options.element.firstChild)return;const t=H(e.value);e.value.append(...o.options.element.childNodes),o.contentComponent=n.ctx._,n&&(o.appContext={...n.appContext,provides:n.provides}),o.setOptions({element:t}),o.createNodeViews()}))})),O((()=>{const e=t.editor;e&&(e.contentComponent=null,e.appContext=null)})),{rootEl:e}},render(){return S("div",{ref:t=>{this.rootEl=t}})}}),Yn=w({name:"FloatingMenu",props:{pluginKey:{type:null,default:"floatingMenu"},editor:{type:Object,required:!0},tippyOptions:{type:Object,default:()=>({})},shouldShow:{type:Function,default:null}},setup(t,{slots:e}){const n=k(null);return A((()=>{const{pluginKey:e,editor:o,tippyOptions:s,shouldShow:r}=t;o.registerPlugin(yn({pluginKey:e,editor:o,element:n.value,tippyOptions:s,shouldShow:r}))})),O((()=>{const{pluginKey:e,editor:n}=t;n.unregisterPlugin(e)})),()=>{var t;return S("div",{ref:n},null===(t=e.default)||void 0===t?void 0:t.call(e))}}}),to=w({name:"NodeViewContent",props:{as:{type:String,default:"div"}},render(){return S(this.as,{style:{whiteSpace:"pre-wrap"},"data-node-view-content":""})}}),eo=w({name:"NodeViewWrapper",props:{as:{type:String,default:"div"}},inject:["onDragStart","decorationClasses"],render(){var t,e;return S(this.as,{class:this.decorationClasses,style:{whiteSpace:"normal"},"data-node-view-wrapper":"",onDragstart:this.onDragStart},null===(e=(t=this.$slots).default)||void 0===e?void 0:e.call(t))}});class no{constructor(t,{props:e={},editor:n}){this.editor=n,this.component=x(t),this.el=document.createElement("div"),this.props=T(e),this.renderedComponent=this.renderComponent()}get element(){return this.renderedComponent.el}get ref(){var t,e,n,o;return(null===(e=null===(t=this.renderedComponent.vNode)||void 0===t?void 0:t.component)||void 0===e?void 0:e.exposed)?this.renderedComponent.vNode.component.exposed:null===(o=null===(n=this.renderedComponent.vNode)||void 0===n?void 0:n.component)||void 0===o?void 0:o.proxy}renderComponent(){let t=S(this.component,this.props);this.editor.appContext&&(t.appContext=this.editor.appContext),"undefined"!=typeof document&&this.el&&E(t,this.el);return{vNode:t,destroy:()=>{this.el&&E(null,this.el),this.el=null,t=null},el:this.el?this.el.firstElementChild:null}}updateProps(t={}){Object.entries(t).forEach((([t,e])=>{this.props[t]=e})),this.renderComponent()}destroy(){this.renderedComponent.destroy()}}class oo extends Fe{mount(){const t={editor:this.editor,node:this.node,decorations:this.decorations,innerDecorations:this.innerDecorations,view:this.view,selected:!1,extension:this.extension,HTMLAttributes:this.HTMLAttributes,getPos:()=>this.getPos(),updateAttributes:(t={})=>this.updateAttributes(t),deleteNode:()=>this.deleteNode()},e=this.onDragStart.bind(this);this.decorationClasses=k(this.getDecorationClasses());const n=w({extends:{...this.component},props:Object.keys(t),template:this.component.template,setup:t=>{var n,o;return M("onDragStart",e),M("decorationClasses",this.decorationClasses),null===(o=(n=this.component).setup)||void 0===o?void 0:o.call(n,t,{expose:()=>{}})},__scopeId:this.component.__scopeId,__cssModules:this.component.__cssModules,__name:this.component.__name,__file:this.component.__file});this.handleSelectionUpdate=this.handleSelectionUpdate.bind(this),this.editor.on("selectionUpdate",this.handleSelectionUpdate),this.renderer=new no(n,{editor:this.editor,props:t})}get dom(){if(!this.renderer.element||!this.renderer.element.hasAttribute("data-node-view-wrapper"))throw Error("Please use the NodeViewWrapper component for your node view.");return this.renderer.element}get contentDOM(){return this.node.isLeaf?null:this.dom.querySelector("[data-node-view-content]")}handleSelectionUpdate(){const{from:t,to:e}=this.editor.state.selection,n=this.getPos();if("number"==typeof n)if(t<=n&&e>=n+this.node.nodeSize){if(this.renderer.props.selected)return;this.selectNode()}else{if(!this.renderer.props.selected)return;this.deselectNode()}}update(t,e,n){const o=t=>{this.decorationClasses.value=this.getDecorationClasses(),this.renderer.updateProps(t)};if("function"==typeof this.options.update){const s=this.node,r=this.decorations,i=this.innerDecorations;return this.node=t,this.decorations=e,this.innerDecorations=n,this.options.update({oldNode:s,oldDecorations:r,newNode:t,newDecorations:e,oldInnerDecorations:i,innerDecorations:n,updateProps:()=>o({node:t,decorations:e,innerDecorations:n})})}return t.type===this.node.type&&(t===this.node&&this.decorations===e&&this.innerDecorations===n||(this.node=t,this.decorations=e,this.innerDecorations=n,o({node:t,decorations:e,innerDecorations:n})),!0)}selectNode(){this.renderer.updateProps({selected:!0}),this.renderer.element&&this.renderer.element.classList.add("ProseMirror-selectednode")}deselectNode(){this.renderer.updateProps({selected:!1}),this.renderer.element&&this.renderer.element.classList.remove("ProseMirror-selectednode")}getDecorationClasses(){return this.decorations.map((t=>t.type.attrs.class)).flat().join(" ")}destroy(){this.renderer.destroy(),this.editor.off("selectionUpdate",this.handleSelectionUpdate)}}function so(t,e){return n=>{if(!n.editor.contentComponent)return{};const o="function"==typeof t&&"__vccOpts"in t?t.__vccOpts:t;return new oo(o,n,e)}}const ro=/^```([a-z]+)?[\s\n]$/,io=/^~~~([a-z]+)?[\s\n]$/,ao=We.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;const{languageClassPrefix:n}=this.options,o=[...(null===(e=t.firstElementChild)||void 0===e?void 0:e.classList)||[]].filter((t=>t.startsWith(n))).map((t=>t.replace(n,"")))[0];return o||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",Tt(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{const{empty:t,$anchor:e}=this.editor.state.selection,n=1===e.pos;return!(!t||e.parent.type.name!==this.name)&&(!(!n&&e.parent.textContent.length)&&this.editor.commands.clearNodes())},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;const{state:e}=t,{selection:n}=e,{$from:o,empty:s}=n;if(!s||o.parent.type!==this.type)return!1;const r=o.parentOffset===o.parent.nodeSize-2,i=o.parent.textContent.endsWith("\n\n");return!(!r||!i)&&t.chain().command((({tr:t})=>(t.delete(o.pos-2,o.pos),!0))).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;const{state:e}=t,{selection:n,doc:o}=e,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;if(!(r.parentOffset===r.parent.nodeSize-2))return!1;const a=r.after();if(void 0===a)return!1;return o.nodeAt(a)?t.commands.command((({tr:t})=>(t.setSelection(s.near(o.resolve(a))),!0))):t.commands.exitCode()}}},addInputRules(){return[Ke({find:ro,type:this.type,getAttributes:t=>({language:t[1]})}),Ke({find:io,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new o({key:new n("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData)return!1;if(this.editor.isActive(this.type.name))return!1;const n=e.clipboardData.getData("text/plain"),o=e.clipboardData.getData("vscode-editor-data"),s=o?JSON.parse(o):void 0,i=null==s?void 0:s.mode;if(!n||!i)return!1;const{tr:a,schema:l}=t.state,d=l.text(n.replace(/\r\n?/g,"\n"));return a.replaceSelectionWith(this.type.create({language:i},d)),a.selection.$from.parent.type!==this.type&&a.setSelection(r.near(a.doc.resolve(Math.max(0,a.selection.from-2)))),a.setMeta("paste",!0),t.dispatch(a),!0}}})]}});function lo(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function co(t){return t instanceof Map?t.clear=t.delete=t.set=function(){throw new Error("map is read-only")}:t instanceof Set&&(t.add=t.clear=t.delete=function(){throw new Error("set is read-only")}),Object.freeze(t),Object.getOwnPropertyNames(t).forEach((e=>{const n=t[e],o=typeof n;"object"!==o&&"function"!==o||Object.isFrozen(n)||co(n)})),t}class uo{constructor(t){void 0===t.data&&(t.data={}),this.data=t.data,this.isMatchIgnored=!1}ignoreMatch(){this.isMatchIgnored=!0}}function po(t){return t.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#x27;")}function ho(t,...e){const n=Object.create(null);for(const o in t)n[o]=t[o];return e.forEach((function(t){for(const e in t)n[e]=t[e]})),n}const mo=t=>!!t.scope;class fo{constructor(t,e){this.buffer="",this.classPrefix=e.classPrefix,t.walk(this)}addText(t){this.buffer+=po(t)}openNode(t){if(!mo(t))return;const e=((t,{prefix:e})=>{if(t.startsWith("language:"))return t.replace("language:","language-");if(t.includes(".")){const n=t.split(".");return[`${e}${n.shift()}`,...n.map(((t,e)=>`${t}${"_".repeat(e+1)}`))].join(" ")}return`${e}${t}`})(t.scope,{prefix:this.classPrefix});this.span(e)}closeNode(t){mo(t)&&(this.buffer+="</span>")}value(){return this.buffer}span(t){this.buffer+=`<span class="${t}">`}}const go=(t={})=>{const e={children:[]};return Object.assign(e,t),e};class yo{constructor(){this.rootNode=go(),this.stack=[this.rootNode]}get top(){return this.stack[this.stack.length-1]}get root(){return this.rootNode}add(t){this.top.children.push(t)}openNode(t){const e=go({scope:t});this.add(e),this.stack.push(e)}closeNode(){if(this.stack.length>1)return this.stack.pop()}closeAllNodes(){for(;this.closeNode(););}toJSON(){return JSON.stringify(this.rootNode,null,4)}walk(t){return this.constructor._walk(t,this.rootNode)}static _walk(t,e){return"string"==typeof e?t.addText(e):e.children&&(t.openNode(e),e.children.forEach((e=>this._walk(t,e))),t.closeNode(e)),t}static _collapse(t){"string"!=typeof t&&t.children&&(t.children.every((t=>"string"==typeof t))?t.children=[t.children.join("")]:t.children.forEach((t=>{yo._collapse(t)})))}}class vo extends yo{constructor(t){super(),this.options=t}addText(t){""!==t&&this.add(t)}startScope(t){this.openNode(t)}endScope(){this.closeNode()}__addSublanguage(t,e){const n=t.root;e&&(n.scope=`language:${e}`),this.add(n)}toHTML(){return new fo(this,this.options).value()}finalize(){return this.closeAllNodes(),!0}}function bo(t){return t?"string"==typeof t?t:t.source:null}function ko(t){return xo("(?=",t,")")}function wo(t){return xo("(?:",t,")*")}function Mo(t){return xo("(?:",t,")?")}function xo(...t){return t.map((t=>bo(t))).join("")}function To(...t){const e=function(t){const e=t[t.length-1];return"object"==typeof e&&e.constructor===Object?(t.splice(t.length-1,1),e):{}}(t);return"("+(e.capture?"":"?:")+t.map((t=>bo(t))).join("|")+")"}function So(t){return new RegExp(t.toString()+"|").exec("").length-1}const Eo=/\[(?:[^\\\]]|\\.)*\]|\(\??|\\([1-9][0-9]*)|\\./;function Ao(t,{joinWith:e}){let n=0;return t.map((t=>{n+=1;const e=n;let o=bo(t),s="";for(;o.length>0;){const t=Eo.exec(o);if(!t){s+=o;break}s+=o.substring(0,t.index),o=o.substring(t.index+t[0].length),"\\"===t[0][0]&&t[1]?s+="\\"+String(Number(t[1])+e):(s+=t[0],"("===t[0]&&n++)}return s})).map((t=>`(${t})`)).join(e)}const Oo="[a-zA-Z]\\w*",Lo="[a-zA-Z_]\\w*",Co="\\b\\d+(\\.\\d+)?",Ho="(-?)(\\b0[xX][a-fA-F0-9]+|(\\b\\d+(\\.\\d*)?|\\.\\d+)([eE][-+]?\\d+)?)",Po="\\b(0b[01]+)",$o={begin:"\\\\[\\s\\S]",relevance:0},No={scope:"string",begin:"'",end:"'",illegal:"\\n",contains:[$o]},_o={scope:"string",begin:'"',end:'"',illegal:"\\n",contains:[$o]},Io=function(t,e,n={}){const o=ho({scope:"comment",begin:t,end:e,contains:[]},n);o.contains.push({scope:"doctag",begin:"[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)",end:/(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,excludeBegin:!0,relevance:0});const s=To("I","a","is","so","us","to","at","if","in","it","on",/[A-Za-z]+['](d|ve|re|ll|t|s|n)/,/[A-Za-z]+[-][a-z]+/,/[A-Za-z][a-z]{2,}/);return o.contains.push({begin:xo(/[ ]+/,"(",s,/[.]?[:]?([.][ ]|[ ])/,"){3}")}),o},jo=Io("//","$"),Do=Io("/\\*","\\*/"),Ro=Io("#","$"),Bo={scope:"number",begin:Co,relevance:0},zo={scope:"number",begin:Ho,relevance:0},qo={scope:"number",begin:Po,relevance:0},Ko={scope:"regexp",begin:/\/(?=[^/\n]*\/)/,end:/\/[gimuy]*/,contains:[$o,{begin:/\[/,end:/\]/,relevance:0,contains:[$o]}]},Uo={scope:"title",begin:Oo,relevance:0},Vo={scope:"title",begin:Lo,relevance:0},Wo={begin:"\\.\\s*"+Lo,relevance:0};var Fo=Object.freeze({__proto__:null,APOS_STRING_MODE:No,BACKSLASH_ESCAPE:$o,BINARY_NUMBER_MODE:qo,BINARY_NUMBER_RE:Po,COMMENT:Io,C_BLOCK_COMMENT_MODE:Do,C_LINE_COMMENT_MODE:jo,C_NUMBER_MODE:zo,C_NUMBER_RE:Ho,END_SAME_AS_BEGIN:function(t){return Object.assign(t,{"on:begin":(t,e)=>{e.data._beginMatch=t[1]},"on:end":(t,e)=>{e.data._beginMatch!==t[1]&&e.ignoreMatch()}})},HASH_COMMENT_MODE:Ro,IDENT_RE:Oo,MATCH_NOTHING_RE:/\b\B/,METHOD_GUARD:Wo,NUMBER_MODE:Bo,NUMBER_RE:Co,PHRASAL_WORDS_MODE:{begin:/\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\b/},QUOTE_STRING_MODE:_o,REGEXP_MODE:Ko,RE_STARTERS_RE:"!|!=|!==|%|%=|&|&&|&=|\\*|\\*=|\\+|\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\?|\\[|\\{|\\(|\\^|\\^=|\\||\\|=|\\|\\||~",SHEBANG:(t={})=>{const e=/^#![ ]*\//;return t.binary&&(t.begin=xo(e,/.*\b/,t.binary,/\b.*/)),ho({scope:"meta",begin:e,end:/$/,relevance:0,"on:begin":(t,e)=>{0!==t.index&&e.ignoreMatch()}},t)},TITLE_MODE:Uo,UNDERSCORE_IDENT_RE:Lo,UNDERSCORE_TITLE_MODE:Vo});function Qo(t,e){"."===t.input[t.index-1]&&e.ignoreMatch()}function Go(t,e){void 0!==t.className&&(t.scope=t.className,delete t.className)}function Jo(t,e){e&&t.beginKeywords&&(t.begin="\\b("+t.beginKeywords.split(" ").join("|")+")(?!\\.)(?=\\b|\\s)",t.__beforeBegin=Qo,t.keywords=t.keywords||t.beginKeywords,delete t.beginKeywords,void 0===t.relevance&&(t.relevance=0))}function Xo(t,e){Array.isArray(t.illegal)&&(t.illegal=To(...t.illegal))}function Zo(t,e){if(t.match){if(t.begin||t.end)throw new Error("begin & end are not supported with match");t.begin=t.match,delete t.match}}function Yo(t,e){void 0===t.relevance&&(t.relevance=1)}const ts=(t,e)=>{if(!t.beforeMatch)return;if(t.starts)throw new Error("beforeMatch cannot be used with starts");const n=Object.assign({},t);Object.keys(t).forEach((e=>{delete t[e]})),t.keywords=n.keywords,t.begin=xo(n.beforeMatch,ko(n.begin)),t.starts={relevance:0,contains:[Object.assign(n,{endsParent:!0})]},t.relevance=0,delete n.beforeMatch},es=["of","and","for","in","not","or","if","then","parent","list","value"];function ns(t,e,n="keyword"){const o=Object.create(null);return"string"==typeof t?s(n,t.split(" ")):Array.isArray(t)?s(n,t):Object.keys(t).forEach((function(n){Object.assign(o,ns(t[n],e,n))})),o;function s(t,n){e&&(n=n.map((t=>t.toLowerCase()))),n.forEach((function(e){const n=e.split("|");o[n[0]]=[t,os(n[0],n[1])]}))}}function os(t,e){return e?Number(e):function(t){return es.includes(t.toLowerCase())}(t)?0:1}const ss={},rs=(t,e)=>{ss[`${t}/${e}`]||(ss[`${t}/${e}`]=!0)},is=new Error;function as(t,e,{key:n}){let o=0;const s=t[n],r={},i={};for(let a=1;a<=e.length;a++)i[a+o]=s[a],r[a+o]=!0,o+=So(e[a-1]);t[n]=i,t[n]._emit=r,t[n]._multi=!0}function ls(t){!function(t){t.scope&&"object"==typeof t.scope&&null!==t.scope&&(t.beginScope=t.scope,delete t.scope)}(t),"string"==typeof t.beginScope&&(t.beginScope={_wrap:t.beginScope}),"string"==typeof t.endScope&&(t.endScope={_wrap:t.endScope}),function(t){if(Array.isArray(t.begin)){if(t.skip||t.excludeBegin||t.returnBegin)throw is;if("object"!=typeof t.beginScope||null===t.beginScope)throw is;as(t,t.begin,{key:"beginScope"}),t.begin=Ao(t.begin,{joinWith:""})}}(t),function(t){if(Array.isArray(t.end)){if(t.skip||t.excludeEnd||t.returnEnd)throw is;if("object"!=typeof t.endScope||null===t.endScope)throw is;as(t,t.end,{key:"endScope"}),t.end=Ao(t.end,{joinWith:""})}}(t)}function ds(t){function e(e,n){return new RegExp(bo(e),"m"+(t.case_insensitive?"i":"")+(t.unicodeRegex?"u":"")+(n?"g":""))}class n{constructor(){this.matchIndexes={},this.regexes=[],this.matchAt=1,this.position=0}addRule(t,e){e.position=this.position++,this.matchIndexes[this.matchAt]=e,this.regexes.push([e,t]),this.matchAt+=So(t)+1}compile(){0===this.regexes.length&&(this.exec=()=>null);const t=this.regexes.map((t=>t[1]));this.matcherRe=e(Ao(t,{joinWith:"|"}),!0),this.lastIndex=0}exec(t){this.matcherRe.lastIndex=this.lastIndex;const e=this.matcherRe.exec(t);if(!e)return null;const n=e.findIndex(((t,e)=>e>0&&void 0!==t)),o=this.matchIndexes[n];return e.splice(0,n),Object.assign(e,o)}}class o{constructor(){this.rules=[],this.multiRegexes=[],this.count=0,this.lastIndex=0,this.regexIndex=0}getMatcher(t){if(this.multiRegexes[t])return this.multiRegexes[t];const e=new n;return this.rules.slice(t).forEach((([t,n])=>e.addRule(t,n))),e.compile(),this.multiRegexes[t]=e,e}resumingScanAtSamePosition(){return 0!==this.regexIndex}considerAll(){this.regexIndex=0}addRule(t,e){this.rules.push([t,e]),"begin"===e.type&&this.count++}exec(t){const e=this.getMatcher(this.regexIndex);e.lastIndex=this.lastIndex;let n=e.exec(t);if(this.resumingScanAtSamePosition())if(n&&n.index===this.lastIndex);else{const e=this.getMatcher(0);e.lastIndex=this.lastIndex+1,n=e.exec(t)}return n&&(this.regexIndex+=n.position+1,this.regexIndex===this.count&&this.considerAll()),n}}if(t.compilerExtensions||(t.compilerExtensions=[]),t.contains&&t.contains.includes("self"))throw new Error("ERR: contains `self` is not supported at the top-level of a language.  See documentation.");return t.classNameAliases=ho(t.classNameAliases||{}),function n(s,r){const i=s;if(s.isCompiled)return i;[Go,Zo,ls,ts].forEach((t=>t(s,r))),t.compilerExtensions.forEach((t=>t(s,r))),s.__beforeBegin=null,[Jo,Xo,Yo].forEach((t=>t(s,r))),s.isCompiled=!0;let a=null;return"object"==typeof s.keywords&&s.keywords.$pattern&&(s.keywords=Object.assign({},s.keywords),a=s.keywords.$pattern,delete s.keywords.$pattern),a=a||/\w+/,s.keywords&&(s.keywords=ns(s.keywords,t.case_insensitive)),i.keywordPatternRe=e(a,!0),r&&(s.begin||(s.begin=/\B|\b/),i.beginRe=e(i.begin),s.end||s.endsWithParent||(s.end=/\B|\b/),s.end&&(i.endRe=e(i.end)),i.terminatorEnd=bo(i.end)||"",s.endsWithParent&&r.terminatorEnd&&(i.terminatorEnd+=(s.end?"|":"")+r.terminatorEnd)),s.illegal&&(i.illegalRe=e(s.illegal)),s.contains||(s.contains=[]),s.contains=[].concat(...s.contains.map((function(t){return function(t){t.variants&&!t.cachedVariants&&(t.cachedVariants=t.variants.map((function(e){return ho(t,{variants:null},e)})));if(t.cachedVariants)return t.cachedVariants;if(cs(t))return ho(t,{starts:t.starts?ho(t.starts):null});if(Object.isFrozen(t))return ho(t);return t}("self"===t?s:t)}))),s.contains.forEach((function(t){n(t,i)})),s.starts&&n(s.starts,r),i.matcher=function(t){const e=new o;return t.contains.forEach((t=>e.addRule(t.begin,{rule:t,type:"begin"}))),t.terminatorEnd&&e.addRule(t.terminatorEnd,{type:"end"}),t.illegal&&e.addRule(t.illegal,{type:"illegal"}),e}(i),i}(t)}function cs(t){return!!t&&(t.endsWithParent||cs(t.starts))}class us extends Error{constructor(t,e){super(t),this.name="HTMLInjectionError",this.html=e}}const ps=po,hs=ho,ms=Symbol("nomatch"),fs=function(t){const e=Object.create(null),n=Object.create(null),o=[];let s=!0;const r="Could not find the language '{}', did you forget to load/include a language module?",i={disableAutodetect:!0,name:"Plain text",contains:[]};let a={ignoreUnescapedHTML:!1,throwUnescapedHTML:!1,noHighlightRe:/^(no-?highlight)$/i,languageDetectRe:/\blang(?:uage)?-([\w-]+)\b/i,classPrefix:"hljs-",cssSelector:"pre code",languages:null,__emitter:vo};function l(t){return a.noHighlightRe.test(t)}function d(t,e,n){let o="",s="";"object"==typeof e?(o=t,n=e.ignoreIllegals,s=e.language):(rs("10.7.0","highlight(lang, code, ...args) has been deprecated."),rs("10.7.0","Please use highlight(code, options) instead.\nhttps://github.com/highlightjs/highlight.js/issues/2277"),s=t,o=e),void 0===n&&(n=!0);const r={code:o,language:s};v("before:highlight",r);const i=r.result?r.result:c(r.language,r.code,n);return i.code=r.code,v("after:highlight",i),i}function c(t,n,o,i){const l=Object.create(null);function d(){if(!S.keywords)return void A.addText(O);let t=0;S.keywordPatternRe.lastIndex=0;let e=S.keywordPatternRe.exec(O),n="";for(;e;){n+=O.substring(t,e.index);const s=M.case_insensitive?e[0].toLowerCase():e[0],r=(o=s,S.keywords[o]);if(r){const[t,o]=r;if(A.addText(n),n="",l[s]=(l[s]||0)+1,l[s]<=7&&(L+=o),t.startsWith("_"))n+=e[0];else{const n=M.classNameAliases[t]||t;h(e[0],n)}}else n+=e[0];t=S.keywordPatternRe.lastIndex,e=S.keywordPatternRe.exec(O)}var o;n+=O.substring(t),A.addText(n)}function p(){null!=S.subLanguage?function(){if(""===O)return;let t=null;if("string"==typeof S.subLanguage){if(!e[S.subLanguage])return void A.addText(O);t=c(S.subLanguage,O,!0,E[S.subLanguage]),E[S.subLanguage]=t._top}else t=u(O,S.subLanguage.length?S.subLanguage:null);S.relevance>0&&(L+=t.relevance),A.__addSublanguage(t._emitter,t.language)}():d(),O=""}function h(t,e){""!==t&&(A.startScope(e),A.addText(t),A.endScope())}function m(t,e){let n=1;const o=e.length-1;for(;n<=o;){if(!t._emit[n]){n++;continue}const o=M.classNameAliases[t[n]]||t[n],s=e[n];o?h(s,o):(O=s,d(),O=""),n++}}function g(t,e){return t.scope&&"string"==typeof t.scope&&A.openNode(M.classNameAliases[t.scope]||t.scope),t.beginScope&&(t.beginScope._wrap?(h(O,M.classNameAliases[t.beginScope._wrap]||t.beginScope._wrap),O=""):t.beginScope._multi&&(m(t.beginScope,e),O="")),S=Object.create(t,{parent:{value:S}}),S}function y(t,e,n){let o=function(t,e){const n=t&&t.exec(e);return n&&0===n.index}(t.endRe,n);if(o){if(t["on:end"]){const n=new uo(t);t["on:end"](e,n),n.isMatchIgnored&&(o=!1)}if(o){for(;t.endsParent&&t.parent;)t=t.parent;return t}}if(t.endsWithParent)return y(t.parent,e,n)}function v(t){return 0===S.matcher.regexIndex?(O+=t[0],1):(P=!0,0)}function b(t){const e=t[0],o=n.substring(t.index),s=y(S,t,o);if(!s)return ms;const r=S;S.endScope&&S.endScope._wrap?(p(),h(e,S.endScope._wrap)):S.endScope&&S.endScope._multi?(p(),m(S.endScope,t)):r.skip?O+=e:(r.returnEnd||r.excludeEnd||(O+=e),p(),r.excludeEnd&&(O=e));do{S.scope&&A.closeNode(),S.skip||S.subLanguage||(L+=S.relevance),S=S.parent}while(S!==s.parent);return s.starts&&g(s.starts,t),r.returnEnd?0:e.length}let k={};function w(e,r){const i=r&&r[0];if(O+=e,null==i)return p(),0;if("begin"===k.type&&"end"===r.type&&k.index===r.index&&""===i){if(O+=n.slice(r.index,r.index+1),!s){const e=new Error(`0 width match regex (${t})`);throw e.languageName=t,e.badRule=k.rule,e}return 1}if(k=r,"begin"===r.type)return function(t){const e=t[0],n=t.rule,o=new uo(n),s=[n.__beforeBegin,n["on:begin"]];for(const r of s)if(r&&(r(t,o),o.isMatchIgnored))return v(e);return n.skip?O+=e:(n.excludeBegin&&(O+=e),p(),n.returnBegin||n.excludeBegin||(O=e)),g(n,t),n.returnBegin?0:e.length}(r);if("illegal"===r.type&&!o){const t=new Error('Illegal lexeme "'+i+'" for mode "'+(S.scope||"<unnamed>")+'"');throw t.mode=S,t}if("end"===r.type){const t=b(r);if(t!==ms)return t}if("illegal"===r.type&&""===i)return 1;if(H>1e5&&H>3*r.index){throw new Error("potential infinite loop, way more iterations than matches")}return O+=i,i.length}const M=f(t);if(!M)throw r.replace("{}",t),new Error('Unknown language: "'+t+'"');const x=ds(M);let T="",S=i||x;const E={},A=new a.__emitter(a);!function(){const t=[];for(let e=S;e!==M;e=e.parent)e.scope&&t.unshift(e.scope);t.forEach((t=>A.openNode(t)))}();let O="",L=0,C=0,H=0,P=!1;try{if(M.__emitTokens)M.__emitTokens(n,A);else{for(S.matcher.considerAll();;){H++,P?P=!1:S.matcher.considerAll(),S.matcher.lastIndex=C;const t=S.matcher.exec(n);if(!t)break;const e=w(n.substring(C,t.index),t);C=t.index+e}w(n.substring(C))}return A.finalize(),T=A.toHTML(),{language:t,value:T,relevance:L,illegal:!1,_emitter:A,_top:S}}catch($){if($.message&&$.message.includes("Illegal"))return{language:t,value:ps(n),illegal:!0,relevance:0,_illegalBy:{message:$.message,index:C,context:n.slice(C-100,C+100),mode:$.mode,resultSoFar:T},_emitter:A};if(s)return{language:t,value:ps(n),illegal:!1,relevance:0,errorRaised:$,_emitter:A,_top:S};throw $}}function u(t,n){n=n||a.languages||Object.keys(e);const o=function(t){const e={value:ps(t),illegal:!1,relevance:0,_top:i,_emitter:new a.__emitter(a)};return e._emitter.addText(t),e}(t),s=n.filter(f).filter(y).map((e=>c(e,t,!1)));s.unshift(o);const r=s.sort(((t,e)=>{if(t.relevance!==e.relevance)return e.relevance-t.relevance;if(t.language&&e.language){if(f(t.language).supersetOf===e.language)return 1;if(f(e.language).supersetOf===t.language)return-1}return 0})),[l,d]=r,u=l;return u.secondBest=d,u}function p(t){let e=null;const o=function(t){let e=t.className+" ";e+=t.parentNode?t.parentNode.className:"";const n=a.languageDetectRe.exec(e);if(n){const t=f(n[1]);return t||r.replace("{}",n[1]),t?n[1]:"no-highlight"}return e.split(/\s+/).find((t=>l(t)||f(t)))}(t);if(l(o))return;if(v("before:highlightElement",{el:t,language:o}),t.dataset.highlighted)return;if(t.children.length>0&&(a.ignoreUnescapedHTML,a.throwUnescapedHTML)){throw new us("One of your code blocks includes unescaped HTML.",t.innerHTML)}e=t;const s=e.textContent,i=o?d(s,{language:o,ignoreIllegals:!0}):u(s);t.innerHTML=i.value,t.dataset.highlighted="yes",function(t,e,o){const s=e&&n[e]||o;t.classList.add("hljs"),t.classList.add(`language-${s}`)}(t,o,i.language),t.result={language:i.language,re:i.relevance,relevance:i.relevance},i.secondBest&&(t.secondBest={language:i.secondBest.language,relevance:i.secondBest.relevance}),v("after:highlightElement",{el:t,result:i,text:s})}let h=!1;function m(){if("loading"===document.readyState)return void(h=!0);document.querySelectorAll(a.cssSelector).forEach(p)}function f(t){return t=(t||"").toLowerCase(),e[t]||e[n[t]]}function g(t,{languageName:e}){"string"==typeof t&&(t=[t]),t.forEach((t=>{n[t.toLowerCase()]=e}))}function y(t){const e=f(t);return e&&!e.disableAutodetect}function v(t,e){const n=t;o.forEach((function(t){t[n]&&t[n](e)}))}"undefined"!=typeof window&&window.addEventListener&&window.addEventListener("DOMContentLoaded",(function(){h&&m()}),!1),Object.assign(t,{highlight:d,highlightAuto:u,highlightAll:m,highlightElement:p,highlightBlock:function(t){return rs("10.7.0","highlightBlock will be removed entirely in v12.0"),rs("10.7.0","Please use highlightElement now."),p(t)},configure:function(t){a=hs(a,t)},initHighlighting:()=>{m(),rs("10.6.0","initHighlighting() deprecated.  Use highlightAll() now.")},initHighlightingOnLoad:function(){m(),rs("10.6.0","initHighlightingOnLoad() deprecated.  Use highlightAll() now.")},registerLanguage:function(n,o){let r=null;try{r=o(t)}catch(a){if("Language definition for '{}' could not be registered.".replace("{}",n),!s)throw a;r=i}r.name||(r.name=n),e[n]=r,r.rawDefinition=o.bind(null,t),r.aliases&&g(r.aliases,{languageName:n})},unregisterLanguage:function(t){delete e[t];for(const e of Object.keys(n))n[e]===t&&delete n[e]},listLanguages:function(){return Object.keys(e)},getLanguage:f,registerAliases:g,autoDetection:y,inherit:hs,addPlugin:function(t){!function(t){t["before:highlightBlock"]&&!t["before:highlightElement"]&&(t["before:highlightElement"]=e=>{t["before:highlightBlock"](Object.assign({block:e.el},e))}),t["after:highlightBlock"]&&!t["after:highlightElement"]&&(t["after:highlightElement"]=e=>{t["after:highlightBlock"](Object.assign({block:e.el},e))})}(t),o.push(t)},removePlugin:function(t){const e=o.indexOf(t);-1!==e&&o.splice(e,1)}}),t.debugMode=function(){s=!1},t.safeMode=function(){s=!0},t.versionString="11.10.0",t.regex={concat:xo,lookahead:ko,either:To,optional:Mo,anyNumberOfTimes:wo};for(const b in Fo)"object"==typeof Fo[b]&&co(Fo[b]);return Object.assign(t,Fo),t},gs=fs({});gs.newInstance=()=>fs({});var ys=gs;gs.HighlightJS=gs,gs.default=gs;var vs=lo(ys);function bs(t,e=[]){return t.map((t=>{const n=[...e,...t.properties?t.properties.className:[]];return t.children?bs(t.children,n):{text:t.value,classes:n}})).flat()}function ks(t){return t.value||t.children||[]}function ws({doc:t,name:e,lowlight:n,defaultLanguage:o}){const s=[];return me(t,(t=>t.type.name===e)).forEach((t=>{var e;let r=t.pos+1;const i=t.node.attrs.language||o,a=n.listLanguages();var l;bs(i&&(a.includes(i)||(l=i,Boolean(vs.getLanguage(l)))||(null===(e=n.registered)||void 0===e?void 0:e.call(n,i)))?ks(n.highlight(i,t.node.textContent)):ks(n.highlightAuto(t.node.textContent))).forEach((t=>{const e=r+t.text.length;if(t.classes.length){const n=c.inline(r,e,{class:t.classes.join(" ")});s.push(n)}r=e}))})),d.create(t,s)}function Ms({name:t,lowlight:e,defaultLanguage:s}){if(!["highlight","highlightAuto","listLanguages"].every((t=>"function"==typeof e[t])))throw Error("You should provide an instance of lowlight to use the code-block-lowlight extension");const r=new o({key:new n("lowlight"),state:{init:(n,{doc:o})=>ws({doc:o,name:t,lowlight:e,defaultLanguage:s}),apply:(n,o,r,i)=>{const a=r.selection.$head.parent.type.name,l=i.selection.$head.parent.type.name,d=me(r.doc,(e=>e.type.name===t)),c=me(i.doc,(e=>e.type.name===t));return n.docChanged&&([a,l].includes(t)||c.length!==d.length||n.steps.some((t=>void 0!==t.from&&void 0!==t.to&&d.some((e=>e.pos>=t.from&&e.pos+e.node.nodeSize<=t.to)))))?ws({doc:n.doc,name:t,lowlight:e,defaultLanguage:s}):o.map(n.mapping,n.doc)}},props:{decorations:t=>r.getState(t)}});return r}const xs=ao.extend({addOptions(){var t;return{...null===(t=this.parent)||void 0===t?void 0:t.call(this),lowlight:{},languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}},addProseMirrorPlugins(){var t;return[...(null===(t=this.parent)||void 0===t?void 0:t.call(this))||[],Ms({name:this.name,lowlight:this.options.lowlight,defaultLanguage:this.options.defaultLanguage})]}}),Ts=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,Ss=We.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:t}){return["img",Tt(this.options.HTMLAttributes,t)]},addCommands(){return{setImage:t=>({commands:e})=>e.insertContent({type:this.name,attrs:t})}},addInputRules(){return[qe({find:Ts,type:this.type,getAttributes:t=>{const[,,e,n,o]=t;return{src:n,alt:e,title:o}}})]}});function Es(t){var e;const{char:n,allowSpaces:o,allowToIncludeChar:s,allowedPrefixes:r,startOfLine:i,$position:a}=t,l=o&&!s,d=n.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&");const c=new RegExp(`\\s${d}$`),u=i?"^":"",p=s?"":d,h=l?new RegExp(`${u}${d}.*?(?=\\s${p}|$)`,"gm"):new RegExp(`${u}(?:^)?${d}[^\\s${p}]*`,"gm"),m=(null===(e=a.nodeBefore)||void 0===e?void 0:e.isText)&&a.nodeBefore.text;if(!m)return null;const f=a.pos-m.length,g=Array.from(m.matchAll(h)).pop();if(!g||void 0===g.input||void 0===g.index)return null;const y=g.input.slice(Math.max(0,g.index-1),g.index),v=new RegExp(`^[${null==r?void 0:r.join("")}\0]?$`).test(y);if(null!==r&&!v)return null;const b=f+g.index;let k=b+g[0].length;return l&&c.test(m.slice(k-1,k+1))&&(g[0]+=" ",k+=1),b<a.pos&&k>=a.pos?{range:{from:b,to:k},query:g[0].slice(n.length),text:g[0]}:null}const As=new n("suggestion");function Os({pluginKey:t=As,editor:e,char:n="@",allowSpaces:s=!1,allowToIncludeChar:r=!1,allowedPrefixes:i=[" "],startOfLine:a=!1,decorationTag:l="span",decorationClass:u="suggestion",command:p=()=>null,items:h=()=>[],render:m=()=>({}),allow:f=()=>!0,findSuggestionMatch:g=Es}){let y;const v=null==m?void 0:m(),b=new o({key:t,view(){return{update:async(t,n)=>{var o,s,r,i,a,l,d;const c=null===(o=this.key)||void 0===o?void 0:o.getState(n),u=null===(s=this.key)||void 0===s?void 0:s.getState(t.state),m=c.active&&u.active&&c.range.from!==u.range.from,f=!c.active&&u.active,g=c.active&&!u.active,b=!f&&!g&&c.query!==u.query,k=f||m&&b,w=b||m,M=g||m&&b;if(!k&&!w&&!M)return;const x=M&&!k?c:u,T=t.dom.querySelector(`[data-decoration-id="${x.decorationId}"]`);y={editor:e,range:x.range,query:x.query,text:x.text,items:[],command:t=>p({editor:e,range:x.range,props:t}),decorationNode:T,clientRect:T?()=>{var n;const{decorationId:o}=null===(n=this.key)||void 0===n?void 0:n.getState(e.state),s=t.dom.querySelector(`[data-decoration-id="${o}"]`);return(null==s?void 0:s.getBoundingClientRect())||null}:null},k&&(null===(r=null==v?void 0:v.onBeforeStart)||void 0===r||r.call(v,y)),w&&(null===(i=null==v?void 0:v.onBeforeUpdate)||void 0===i||i.call(v,y)),(w||k)&&(y.items=await h({editor:e,query:x.query})),M&&(null===(a=null==v?void 0:v.onExit)||void 0===a||a.call(v,y)),w&&(null===(l=null==v?void 0:v.onUpdate)||void 0===l||l.call(v,y)),k&&(null===(d=null==v?void 0:v.onStart)||void 0===d||d.call(v,y))},destroy:()=>{var t;y&&(null===(t=null==v?void 0:v.onExit)||void 0===t||t.call(v,y))}}},state:{init:()=>({active:!1,range:{from:0,to:0},query:null,text:null,composing:!1}),apply(t,o,l,d){const{isEditable:c}=e,{composing:u}=e.view,{selection:p}=t,{empty:h,from:m}=p,y={...o};if(y.composing=u,c&&(h||e.view.composing)){!(m<o.range.from||m>o.range.to)||u||o.composing||(y.active=!1);const t=g({char:n,allowSpaces:s,allowToIncludeChar:r,allowedPrefixes:i,startOfLine:a,$position:p.$from}),l=`id_${Math.floor(4294967295*Math.random())}`;t&&f({editor:e,state:d,range:t.range,isActive:o.active})?(y.active=!0,y.decorationId=o.decorationId?o.decorationId:l,y.range=t.range,y.query=t.query,y.text=t.text):y.active=!1}else y.active=!1;return y.active||(y.decorationId=null,y.range={from:0,to:0},y.query=null,y.text=null),y}},props:{handleKeyDown(t,e){var n;const{active:o,range:s}=b.getState(t.state);return o&&(null===(n=null==v?void 0:v.onKeyDown)||void 0===n?void 0:n.call(v,{view:t,event:e,range:s}))||!1},decorations(t){const{active:e,range:n,decorationId:o}=b.getState(t);return e?d.create(t.doc,[c.inline(n.from,n.to,{nodeName:l,class:u,"data-decoration-id":o})]):null}}});return b}const Ls=new n("mention"),Cs=We.create({name:"mention",priority:101,addOptions(){return{HTMLAttributes:{},renderText({options:t,node:e}){var n;return`${t.suggestion.char}${null!==(n=e.attrs.label)&&void 0!==n?n:e.attrs.id}`},deleteTriggerWithBackspace:!1,renderHTML({options:t,node:e}){var n;return["span",Tt(this.HTMLAttributes,t.HTMLAttributes),`${t.suggestion.char}${null!==(n=e.attrs.label)&&void 0!==n?n:e.attrs.id}`]},suggestion:{char:"@",pluginKey:Ls,command:({editor:t,range:e,props:n})=>{var o,s,r;const i=t.view.state.selection.$to.nodeAfter;(null===(o=null==i?void 0:i.text)||void 0===o?void 0:o.startsWith(" "))&&(e.to+=1),t.chain().focus().insertContentAt(e,[{type:this.name,attrs:n},{type:"text",text:" "}]).run(),null===(r=null===(s=t.view.dom.ownerDocument.defaultView)||void 0===s?void 0:s.getSelection())||void 0===r||r.collapseToEnd()},allow:({state:t,range:e})=>{const n=t.doc.resolve(e.from),o=t.schema.nodes[this.name];return!!n.parent.type.contentMatch.matchType(o)}}}},group:"inline",inline:!0,selectable:!1,atom:!0,addAttributes:()=>({id:{default:null,parseHTML:t=>t.getAttribute("data-id"),renderHTML:t=>t.id?{"data-id":t.id}:{}},label:{default:null,parseHTML:t=>t.getAttribute("data-label"),renderHTML:t=>t.label?{"data-label":t.label}:{}}}),parseHTML(){return[{tag:`span[data-type="${this.name}"]`}]},renderHTML({node:t,HTMLAttributes:e}){if(void 0!==this.options.renderLabel)return["span",Tt({"data-type":this.name},this.options.HTMLAttributes,e),this.options.renderLabel({options:this.options,node:t})];const n={...this.options};n.HTMLAttributes=Tt({"data-type":this.name},this.options.HTMLAttributes,e);const o=this.options.renderHTML({options:n,node:t});return"string"==typeof o?["span",Tt({"data-type":this.name},this.options.HTMLAttributes,e),o]:o},renderText({node:t}){return void 0!==this.options.renderLabel?this.options.renderLabel({options:this.options,node:t}):this.options.renderText({options:this.options,node:t})},addKeyboardShortcuts(){return{Backspace:()=>this.editor.commands.command((({tr:t,state:e})=>{let n=!1;const{selection:o}=e,{empty:s,anchor:r}=o;return!!s&&(e.doc.nodesBetween(r-1,r,((e,o)=>{if(e.type.name===this.name)return n=!0,t.insertText(this.options.deleteTriggerWithBackspace?"":this.options.suggestion.char||"",o,o+e.nodeSize),!1})),n)}))}},addProseMirrorPlugins(){return[Os({editor:this.editor,...this.options.suggestion})]}}),Hs=/^\s*(\[([( |x])?\])\s$/,Ps=We.create({name:"taskItem",addOptions:()=>({nested:!1,HTMLAttributes:{},taskListTypeName:"taskList"}),content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes:()=>({checked:{default:!1,keepOnSplit:!1,parseHTML:t=>{const e=t.getAttribute("data-checked");return""===e||"true"===e},renderHTML:t=>({"data-checked":t.checked})}}),parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:t,HTMLAttributes:e}){return["li",Tt(this.options.HTMLAttributes,e,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:t.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){const t={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...t,Tab:()=>this.editor.commands.sinkListItem(this.name)}:t},addNodeView(){return({node:t,HTMLAttributes:e,getPos:n,editor:o})=>{const s=document.createElement("li"),r=document.createElement("label"),i=document.createElement("span"),a=document.createElement("input"),l=document.createElement("div");return r.contentEditable="false",a.type="checkbox",a.addEventListener("mousedown",(t=>t.preventDefault())),a.addEventListener("change",(e=>{if(!o.isEditable&&!this.options.onReadOnlyChecked)return void(a.checked=!a.checked);const{checked:s}=e.target;o.isEditable&&"function"==typeof n&&o.chain().focus(void 0,{scrollIntoView:!1}).command((({tr:t})=>{const e=n();if("number"!=typeof e)return!1;const o=t.doc.nodeAt(e);return t.setNodeMarkup(e,void 0,{...null==o?void 0:o.attrs,checked:s}),!0})).run(),!o.isEditable&&this.options.onReadOnlyChecked&&(this.options.onReadOnlyChecked(t,s)||(a.checked=!a.checked))})),Object.entries(this.options.HTMLAttributes).forEach((([t,e])=>{s.setAttribute(t,e)})),s.dataset.checked=t.attrs.checked,a.checked=t.attrs.checked,r.append(a,i),s.append(r,l),Object.entries(e).forEach((([t,e])=>{s.setAttribute(t,e)})),{dom:s,contentDOM:l,update:t=>t.type===this.type&&(s.dataset.checked=t.attrs.checked,a.checked=t.attrs.checked,!0)}}},addInputRules(){return[Ve({find:Hs,type:this.type,getAttributes:t=>({checked:"x"===t[t.length-1]})})]}}),$s=Vt.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new o({key:new n("placeholder"),props:{decorations:({doc:t,selection:e})=>{const n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:o}=e,s=[];if(!n)return null;const r=this.editor.isEmpty;return t.descendants(((t,e)=>{const n=o>=e&&o<=e+t.nodeSize,i=!t.isLeaf&&xe(t);if((n||!this.options.showOnlyCurrent)&&i){const o=[this.options.emptyNodeClass];r&&o.push(this.options.emptyEditorClass);const i=c.node(e,e+t.nodeSize,{class:o.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:t,pos:e,hasAnchor:n}):this.options.placeholder});s.push(i)}return this.options.includeChildren})),d.create(t,s)}}})]}});export{Yn as A,en as B,xs as C,mn as D,Vt as E,bn as F,kn as G,wn as H,Ss as I,Zn as J,In as L,Rt as M,We as N,Rn as O,Bn as P,Os as S,Ps as T,Qn as U,so as V,eo as a,to as b,Cs as c,Vn as d,fn as e,Hn as f,Pt as g,Kn as h,De as i,un as j,ln as k,Un as l,Je as m,pn as n,hn as o,Tn as p,En as q,_n as r,Sn as s,Fn as t,vn as u,sn as v,Wn as w,$s as x,Xn as y,Gn as z};
