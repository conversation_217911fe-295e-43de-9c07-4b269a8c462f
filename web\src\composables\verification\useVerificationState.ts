import { ref, type Ref } from 'vue'

import type { VerificationResponse } from '@/types/verification/verification-response.types'

/**
 * 验证状态管理组合式函数返回值类型
 */
interface UseVerificationStateReturn {
  /** 当前验证列表 */
  verifications: Ref<VerificationResponse[]>
  /** 添加验证记录 */
  addVerification: (verification: VerificationResponse) => void
  /** 更新验证状态 */
  updateVerification: (id: number, updates: Partial<VerificationResponse>) => void
  /** 移除验证记录 */
  removeVerification: (id: number) => void
  /** 获取验证记录 */
  getVerification: (id: number) => VerificationResponse | undefined
  /** 清理所有验证记录 */
  clearVerifications: () => void
  /** 检查是否存在进行中的验证 */
  hasActiveVerification: (privilegeId: number) => boolean
}

/**
 * 验证状态管理组合式函数
 * 提供验证流程的状态管理功能
 */
export function useVerificationState(): UseVerificationStateReturn {
  // 当前验证列表
  const verifications = ref<VerificationResponse[]>([])

  /**
   * 添加验证记录
   * 如果已存在相同ID的记录则更新，否则新增
   */
  const addVerification = (verification: VerificationResponse): void => {
    const existingIndex = verifications.value.findIndex((v) => v.id === verification.id)
    if (existingIndex >= 0) {
      verifications.value[existingIndex] = verification
    } else {
      verifications.value.push(verification)
    }
  }

  /**
   * 更新验证状态
   * 根据ID更新指定验证记录的部分字段
   */
  const updateVerification = (id: number, updates: Partial<VerificationResponse>): void => {
    const index = verifications.value.findIndex((v) => v.id === id)
    if (index >= 0) {
      verifications.value[index] = { ...verifications.value[index], ...updates }
    }
  }

  /**
   * 移除验证记录
   * 根据ID移除指定的验证记录
   */
  const removeVerification = (id: number): void => {
    const index = verifications.value.findIndex((v) => v.id === id)
    if (index >= 0) {
      verifications.value.splice(index, 1)
    }
  }

  /**
   * 获取验证记录
   * 根据ID获取指定的验证记录
   */
  const getVerification = (id: number): VerificationResponse | undefined => {
    return verifications.value.find((v) => v.id === id)
  }

  /**
   * 清理所有验证记录
   * 清空验证列表
   */
  const clearVerifications = (): void => {
    verifications.value = []
  }

  /**
   * 检查是否存在进行中的验证
   * 检查指定特权是否有正在进行的验证流程
   */
  const hasActiveVerification = (privilegeId: number): boolean => {
    return verifications.value.some(
      (v) => v.privilegeId === privilegeId && v.status === 0, // 0表示进行中
    )
  }

  return {
    verifications,
    addVerification,
    updateVerification,
    removeVerification,
    getVerification,
    clearVerifications,
    hasActiveVerification,
  }
}
