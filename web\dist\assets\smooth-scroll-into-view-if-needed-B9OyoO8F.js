import{e}from"./scroll-into-view-if-needed-CoacbxIo.js";let o;const t=()=>(o||(o="performance"in window?performance.now.bind(performance):Date.now),o());function l(e){const o=t(),r=Math.min((o-e.startTime)/e.duration,1),n=e.ease(r),s=e.startX+(e.x-e.startX)*n,i=e.startY+(e.y-e.startY)*n;e.method(s,i,r,n),s!==e.x||i!==e.y?requestAnimationFrame((()=>l(e))):e.cb()}function r(e,o,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:600,s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:e=>1+--e*e*e*e*e,i=arguments.length>5?arguments[5]:void 0,a=arguments.length>6?arguments[6]:void 0;const c=e,d=e.scrollLeft,m=e.scrollTop;l({scrollable:c,method:(o,t,l,r)=>{const n=Math.ceil(o),s=Math.ceil(t);e.scrollLeft=n,e.scrollTop=s,null==a||a({target:e,elapsed:l,value:r,left:n,top:s})},startTime:t(),startX:d,startY:m,x:o,y:r,duration:n,ease:s,cb:i})}const n=function(o,t){const l=t||{};return(n=l)&&!n.behavior||"smooth"===n.behavior?e(o,{block:l.block,inline:l.inline,scrollMode:l.scrollMode,boundary:l.boundary,skipOverflowHiddenElements:l.skipOverflowHiddenElements,behavior:e=>Promise.all(e.reduce(((e,o)=>{let{el:t,left:n,top:s}=o;const i=t.scrollLeft,a=t.scrollTop;return i===n&&a===s?e:[...e,new Promise((e=>r(t,n,s,l.duration,l.ease,(()=>e({el:t,left:[i,n],top:[a,s]})),l.onScrollChange)))]}),[]))}):Promise.resolve(e(o,t));var n};export{n as s};
