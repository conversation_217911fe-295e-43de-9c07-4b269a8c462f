import { type Ref } from 'vue'

import { type PrivilegeResponse } from '@/types/privilege/privilege-response.types'

/**
 * 特权时间组合式函数返回值类型
 */
interface UsePrivilegeTimeReturn {
  /** 切换时间显示格式 */
  toggleTimeFormat: (privilege: PrivilegeResponse) => void
}

/**
 * 特权时间管理组合式函数
 * 提供特权时间显示格式的切换功能
 * @param _privileges 特权数据的响应式引用
 */
export function usePrivilegeTime(
  _privileges: Ref<PrivilegeResponse[]>,
): UsePrivilegeTimeReturn {
  // 切换时间显示格式
  const toggleTimeFormat = (privilege: PrivilegeResponse): void => {
    if (privilege.showExactExpireTime === undefined) {
      privilege.showExactExpireTime = true
    } else {
      privilege.showExactExpireTime = !privilege.showExactExpireTime
    }
  }

  return {
    toggleTimeFormat,
  }
}
