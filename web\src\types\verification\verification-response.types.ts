/**
 * 特权验证响应类型定义
 */
export interface VerificationResponse {
  /** 验证流程ID */
  id: number
  /** 申请用户ID */
  userId: number
  /** 申请的特权ID */
  privilegeId: number
  /** 特权名称 */
  privilegeName: string
  /** 验证类型：0-短信验证，1-二维码验证 */
  verificationType: number
  /** 当前步骤：1,2,3 */
  currentStep: number
  /** 状态：0-进行中，1-成功，2-失败，3-超时 */
  status: number
  /** 验证页面URL */
  pageUrl?: string
  /** 流程过期时间 */
  expireTime: number
  /** 创建时间 */
  ctTm: number
}

/**
 * 验证时间信息响应类型定义
 */
export interface VerificationTimeInfoResponse {
  /** 剩余秒数 */
  remainingSeconds: number
  /** 是否已自动完成 */
  autoCompleted: boolean
  /** 验证开始时间（页面访问时间） */
  startTime: number
  /** 自动完成时间 */
  autoCompleteTime: number
  /** 当前状态 */
  status: string
  /** 是否已开始计时 */
  timerStarted: boolean
}
