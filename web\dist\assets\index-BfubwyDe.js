const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ActivationCodeModal-BlDwYmT6.js","assets/@vue-C18CzuYV.js","assets/vue-router-CM3lNrYT.js","assets/naive-ui-CyvD4caj.js","assets/seemly-Cdi3gWV3.js","assets/vueuc-BKIYztcR.js","assets/evtd-hWw0KU7y.js","assets/@css-render-DcoOFqSU.js","assets/vooks-n2t0Q59N.js","assets/vdirs-Bvq7nEML.js","assets/@juggle-BnTvdTVm.js","assets/css-render-7x70jhNC.js","assets/@emotion-DFFAhID7.js","assets/lodash-es-BpE61GNB.js","assets/treemate-D3ikBJ7G.js","assets/date-fns-B2WPOLoy.js","assets/date-fns-tz-DYDqAHgp.js","assets/async-validator-Bed4cEOw.js","assets/vite-plugin-node-polyfills-CgB-Lgxu.js","assets/pinia-C1NaKXqp.js","assets/axios-CF6-kBsv.js","assets/@tiptap-BlbpWldy.js","assets/prosemirror-dropcursor-b1zulL1f.js","assets/prosemirror-state-Dg8eoqF5.js","assets/prosemirror-model-BwtArlLQ.js","assets/orderedmap-6uBVSRPO.js","assets/prosemirror-transform-2YHSeD6B.js","assets/prosemirror-view-D1p6KQzm.js","assets/prosemirror-gapcursor-De4VkbqI.js","assets/prosemirror-keymap-CxzETJ23.js","assets/w3c-keyname-DcELQ0J3.js","assets/prosemirror-history-B0l72feN.js","assets/rope-sequence-DIBo4VXF.js","assets/linkifyjs-CgqKPF1I.js","assets/tippy.js-Cq-jft6S.js","assets/@popperjs-B5AGR5A_.js","assets/prosemirror-commands-jNaZT4ky.js","assets/prosemirror-schema-list-DJ0wlwD0.js","assets/tiptap-markdown-C1mL59Et.js","assets/prosemirror-markdown-BKJFDS0M.js","assets/markdown-it-DV9r8h9J.js","assets/mdurl-DbZ9s47_.js","assets/uc.micro-CRGj88R_.js","assets/entities-D_unbCD7.js","assets/linkify-it-DVBmImI5.js","assets/punycode.js-H98b6B6Y.js","assets/markdown-it-task-lists-Dj-XbLVy.js","assets/highlight.js-O_OqoeFy.js","assets/smooth-scroll-into-view-if-needed-B9OyoO8F.js","assets/scroll-into-view-if-needed-CoacbxIo.js","assets/compute-scroll-into-view-Cfyw3hb3.js","assets/lowlight-CRhSxQ06.js","assets/sockjs-client-CnUIS0PG.js","assets/url-parse-BJt2elfP.js","assets/requires-port-CgMabaHb.js","assets/querystringify-B2QvdZsH.js","assets/inherits-BfZYsuMB.js","assets/@stomp-ba8ZO4qr.js","assets/ActivationCodeModal-CRBiMiD0.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,a,l)=>((t,a,l)=>a in t?e(t,a,{enumerable:!0,configurable:!0,writable:!0,value:l}):t[a]=l)(t,"symbol"!=typeof a?a+"":a,l);import{d as a,c as l}from"./pinia-C1NaKXqp.js";import{r as n,d as o,t as i,o as r,f as s,n as c,X as d,Y as u,Z as m,_ as p,$ as v,u as h,a0 as g,a1 as f,a2 as w,H as b,a3 as y,K as k,U as x,a4 as C,F as L,a5 as S,v as T,c as M,h as R,a as A,q as E,W as _,a6 as I,G as z,a7 as B,N as P,B as D,A as $,w as U,T as F,a8 as V,y as O,J as H,a9 as j,S as N,x as q,z as Y,aa as W}from"./@vue-C18CzuYV.js";import{d as J,N as K,a as X,b as G,c as Z,e as Q,f as ee,g as te,S as ae,h as le,B as ne,i as oe,j as ie,k as re,l as se,m as ce,n as de,o as ue,p as me,q as pe,r as ve,s as he,t as ge,u as fe,v as we,w as be,x as ye,y as ke,z as xe,A as Ce,C as Le,D as Se,E as Te,F as Me,G as Re,H as Ae,I as Ee,J as _e,K as Ie,L as ze,M as Be,O as Pe,P as De,Q as $e,R as Ue,T as Fe,U as Ve,V as Oe,W as He}from"./naive-ui-CyvD4caj.js";import{u as je,a as Ne,c as qe,b as Ye}from"./vue-router-CM3lNrYT.js";import{a as We}from"./axios-CF6-kBsv.js";import{a as Je,N as Ke,V as Xe,b as Ge,C as Ze,E as Qe,I as et,c as tt,S as at,T as lt,D as nt,P as ot,d as it,e as rt,B as st,f as ct,h as dt,U as ut,j as mt,H as pt,k as vt,O as ht,L as gt,l as ft,m as wt,n as bt,o as yt,p as kt,q as xt,r as Ct,s as Lt,t as St,F as Tt,G as Mt,u as Rt,v as At,w as Et,x as _t,y as It,z as zt,A as Bt,J as Pt}from"./@tiptap-BlbpWldy.js";import{M as Dt}from"./tiptap-markdown-C1mL59Et.js";import{S as $t,a as Ut,P as Ft}from"./prosemirror-state-Dg8eoqF5.js";import{s as Vt}from"./smooth-scroll-into-view-if-needed-B9OyoO8F.js";import{a as Ot,D as Ht}from"./prosemirror-view-D1p6KQzm.js";import{c as jt,g as Nt}from"./lowlight-CRhSxQ06.js";import{S as qt}from"./sockjs-client-CnUIS0PG.js";import{C as Yt}from"./@stomp-ba8ZO4qr.js";import"./seemly-Cdi3gWV3.js";import"./vueuc-BKIYztcR.js";import"./evtd-hWw0KU7y.js";import"./@css-render-DcoOFqSU.js";import"./vooks-n2t0Q59N.js";import"./vdirs-Bvq7nEML.js";import"./@juggle-BnTvdTVm.js";import"./css-render-7x70jhNC.js";import"./@emotion-DFFAhID7.js";import"./lodash-es-BpE61GNB.js";import"./treemate-D3ikBJ7G.js";import"./date-fns-B2WPOLoy.js";import"./date-fns-tz-DYDqAHgp.js";import"./async-validator-Bed4cEOw.js";import"./vite-plugin-node-polyfills-CgB-Lgxu.js";import"./prosemirror-dropcursor-b1zulL1f.js";import"./prosemirror-transform-2YHSeD6B.js";import"./prosemirror-model-BwtArlLQ.js";import"./orderedmap-6uBVSRPO.js";import"./prosemirror-gapcursor-De4VkbqI.js";import"./prosemirror-keymap-CxzETJ23.js";import"./w3c-keyname-DcELQ0J3.js";import"./prosemirror-history-B0l72feN.js";import"./rope-sequence-DIBo4VXF.js";import"./linkifyjs-CgqKPF1I.js";import"./tippy.js-Cq-jft6S.js";import"./@popperjs-B5AGR5A_.js";import"./prosemirror-commands-jNaZT4ky.js";import"./prosemirror-schema-list-DJ0wlwD0.js";import"./prosemirror-markdown-BKJFDS0M.js";import"./markdown-it-DV9r8h9J.js";import"./mdurl-DbZ9s47_.js";import"./uc.micro-CRGj88R_.js";import"./entities-D_unbCD7.js";import"./linkify-it-DVBmImI5.js";import"./punycode.js-H98b6B6Y.js";import"./markdown-it-task-lists-Dj-XbLVy.js";import"./highlight.js-O_OqoeFy.js";import"./scroll-into-view-if-needed-CoacbxIo.js";import"./compute-scroll-into-view-Cfyw3hb3.js";import"./url-parse-BJt2elfP.js";import"./requires-port-CgMabaHb.js";import"./querystringify-B2QvdZsH.js";import"./inherits-BfZYsuMB.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const a of e)if("childList"===a.type)for(const e of a.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const Wt="localhost"==window.location.hostname?`${window.location.protocol}//localhost:20001`:`${window.location.protocol}//${window.location.host}/api`,Jt={logLevel:"error",turnstile:{siteKey:"0x4AAAAAABk65WObKzE7_dzc"},backend:{baseURL:Wt,wsURL:`${Wt}/core/ws`,resourceURL:`${Wt}/osr`}},Kt={DEBUG:"debug",INFO:"info",WARN:"warn",ERROR:"error"},Xt=Jt.logLevel;function Gt(e){const t=Object.values(Kt),a=t.indexOf(Xt);return t.indexOf(e)>=a}const Zt={levels:Kt,debug(e,...t){Gt(Kt.DEBUG)},info(e,...t){Gt(Kt.INFO)},warn(e,...t){Gt(Kt.WARN)},error(e,...t){Gt(Kt.ERROR)}};var Qt=(e=>(e.LIGHT="light",e.DARK="dark",e))(Qt||{});const ea="loginUser";class ta{static parse(e){if("string"!=typeof e)return Zt.warn("Non-string input to JSON.parse:",typeof e),null;if(!e||""===e.trim())return Zt.warn("Empty string passed to JSON.parse"),null;if(e.includes("@")){Zt.debug("Detected @ symbol in string, trying to sanitize");try{const t=/@([^:]+)\s*:\s*(\{.*\})/,a=e.match(t);if(a&&a[2]){const e=a[2];return JSON.parse(e)}const l=e.indexOf("{"),n=e.lastIndexOf("}");if(-1!==l&&-1!==n&&n>l){const t=e.substring(l,n+1);return JSON.parse(t)}}catch(t){Zt.debug("Failed to extract JSON from string with @ symbol:",t)}}try{return JSON.parse(e)}catch(t){return Zt.warn("Invalid JSON format:",t instanceof Error?t.message:"Unknown error"),Zt.debug("Problem JSON string:",e.length>100?e.substring(0,97)+"...":e),null}}static stringify(e,t=!1){try{return t?JSON.stringify(e,null,2):JSON.stringify(e)}catch(a){return Zt.error("JSON stringify error:",a),""}}static deepClone(e){return JSON.parse(JSON.stringify(e))}static isValidJson(e){try{return JSON.parse(e),!0}catch{return!1}}static getProperty(e,t){return t.split(".").reduce(((e,t)=>{if(e&&"object"==typeof e&&!Array.isArray(e)&&t in e)return e[t]}),e)}static setProperty(e,t,a){const l=t.split(".");let n=e;l.forEach(((e,t)=>{if(t===l.length-1)n[e]=a;else{n[e]||(n[e]={});const t=n[e];"object"!=typeof t||Array.isArray(t)||null===t||(n=t)}}))}}class aa{static set(e,t){const a=ta.stringify(t);localStorage.setItem(e,a)}static get(e){const t=localStorage.getItem(e);return t?ta.parse(t):null}static remove(e){localStorage.removeItem(e)}static clear(){localStorage.clear()}static getLoginUser(){const e=this.get(ea);return e?function(e){return{id:e.id,username:e.username,phone:e.phone,email:e.email,avatar:e.avatar,ipLocation:e.ipLocation,job:e.job,level:e.level,notificationReceiveType:e.notificationReceiveType,config:e.config}}(e):null}static setLoginUser(e){const t=(a=e,{...a});var a;this.set(ea,t)}static removeLoginUser(){this.remove(ea)}}const la={white:"#fff","white-1":"#f0f0f0","white-2":"#ddd","creamy-white-1":"#eeece4","creamy-white-2":"#e4e1d8","creamy-white-3":"#dcd8ca",black:"#2e2b29","black-contrast":"#110f0e","dark-gray":"#191919","dark-gray-1":"#202020","deep-gray":"#111","gray-1":"rgba(61, 37, 20, 0.05)","gray-2":"rgba(61, 37, 20, 0.08)","gray-3":"rgba(61, 37, 20, 0.12)","gray-4":"rgba(53, 38, 28, 0.3)","gray-5":"rgba(28, 25, 23, 0.6)",green:"#22c55e",blue:"#4ba3fd","blue-light":"#e6f3ff",purple:"#6a00f5","purple-contrast":"#5800cc","purple-light":"rgba(88, 5, 255, 0.05)","yellow-contrast":"#facc15",yellow:"rgba(250, 204, 21, 0.4)","yellow-light":"#fffae5",red:"#ff5c33","red-light":"#ffebe5","border-1":"0.1rem solid rgba(61, 37, 20, 0.12)",shadow:"0 0.25rem 0.6rem rgba(0, 0, 0, 0.1)","code-text":"#24292e","code-comment":"#6a737d","code-keyword":"#d73a49","code-string":"#032f62","code-number":"#005cc5","code-function":"#6f42c1","code-variable":"#e36209","code-tag":"#22863a","code-attribute":"#6f42c1","code-builtin":"#005cc5","code-meta":"#6a737d","code-deletion-color":"#b31d28","code-deletion-bg":"#ffeef0","code-addition-color":"#22863a","code-addition-bg":"#f0fff4"},na={white:"#121212","white-1":"#242424","white-2":"#363636","creamy-white-1":"#1a1a1a","creamy-white-2":"#262626","creamy-white-3":"#333",black:"#e0e0e0","black-contrast":"#fff","dark-gray":"#191919","dark-gray-1":"#202020","deep-gray":"#111","gray-1":"rgba(200, 200, 200, 0.05)","gray-2":"rgba(200, 200, 200, 0.08)","gray-3":"rgba(200, 200, 200, 0.12)","gray-4":"rgba(200, 200, 200, 0.3)","gray-5":"rgba(200, 200, 200, 0.6)",green:"#22c55e",blue:"#4ba3fd","blue-light":"#2a3745",purple:"#9d6dff","purple-contrast":"#8a5cf5","purple-light":"rgba(154, 92, 255, 0.15)","yellow-contrast":"#facc15",yellow:"rgba(250, 204, 21, 0.4)","yellow-light":"#3f3a14",red:"#ff5c33","red-light":"#3d1a12","border-1":"0.1rem solid rgba(200, 200, 200, 0.12)",shadow:"0 0.25rem 0.6rem rgba(255, 255, 255, 0.1)","code-text":"#e6edf3","code-comment":"#8b949e","code-keyword":"#ff7b72","code-string":"#a5d6ff","code-number":"#79c0ff","code-function":"#d2a8ff","code-variable":"#ffa657","code-tag":"#7ee787","code-attribute":"#d2a8ff","code-builtin":"#79c0ff","code-meta":"#8b949e","code-deletion-color":"#ffa198","code-deletion-bg":"#490202","code-addition-color":"#56d364","code-addition-bg":"#0f5132"};function oa(e){const t=document.documentElement,a=function(e){const t={};for(const[a,l]of Object.entries(e))t[`--${a}`]=l;return t}(e);for(const[l,n]of Object.entries(a))t.style.setProperty(l,n)}const ia="wen-theme";Qt.LIGHT,Qt.DARK;const ra={[Qt.LIGHT]:null,[Qt.DARK]:J},sa=n(Qt.LIGHT),ca=n(!1),da=()=>{const e=sa.value===Qt.LIGHT?Qt.DARK:Qt.LIGHT;return ca.value=!0,sa.value=e,aa.set(ia,e),ua(e),ma(),setTimeout((()=>{ca.value=!1}),50),e},ua=e=>{const t=document.documentElement,a=e===Qt.DARK,l=function(e){return e?na:la}(a);oa(l),t.setAttribute("data-theme",e),a?t.classList.add("dark-theme"):t.classList.remove("dark-theme")},ma=e=>{[".search-container",".n-input",".user-info-group",".user-info",".avatar-container",".n-popover",".notification-container",".comment-info-container",".comment-list-container",".user-comment-container",".user-comment-container-fixed",".comment-content-row",".comment-input-row",".comment-reply-row",".tiptap-editor-wrapper",".editor-content",".ProseMirror",".ProseMirrorInput",".article-content"].forEach((e=>{document.querySelectorAll(e).forEach((e=>{e instanceof HTMLElement&&e.classList.add("theme-priority")}))})),document.querySelectorAll(".ProseMirror, .editor-content, .tiptap-editor-wrapper").forEach((e=>{e instanceof HTMLElement&&(e.setAttribute("data-theme-refresh","true"),requestAnimationFrame((()=>{e.offsetHeight,setTimeout((()=>e.removeAttribute("data-theme-refresh")),50)})))}));document.querySelectorAll(".comment-info-container, .article-info-container").forEach((e=>{if(e instanceof HTMLElement){const t=e.style.display;e.style.display="none",e.offsetHeight,e.style.display=t}}))},pa=o({__name:"App",setup(e){const t=i((()=>ra[sa.value]));return r((()=>{(()=>{const e=aa.get(ia);e&&Object.values(Qt).includes(e)&&(sa.value=e,ua(e))})()})),s(sa,(e=>{c((()=>{[".search-container",".n-input",".user-info-group",".user-info",".avatar-container",".comment-info-container",".comment-list-container",".user-comment-container",".user-comment-container-fixed",".comment-content-row",".comment-input-row",".comment-reply-row",".tiptap-editor-wrapper",".editor-content",".ProseMirror",".ProseMirrorInput"].forEach((e=>{document.querySelectorAll(e).forEach((e=>{e instanceof HTMLElement&&e.classList.add("theme-priority")}))})),document.querySelectorAll(".ProseMirror, .editor-content").forEach((e=>{e instanceof HTMLElement&&(e.setAttribute("data-force-update",Date.now().toString()),setTimeout((()=>e.removeAttribute("data-force-update")),10))})),Zt.debug(`Theme changed to: ${e}`)}))})),(e,a)=>{const l=d("router-view");return m(),u(h(Q),{theme:t.value},{default:p((()=>[v(h(K),null,{default:p((()=>[v(h(X),null,{default:p((()=>[v(h(G),null,{default:p((()=>[v(h(Z),null,{default:p((()=>[v(l)])),_:1})])),_:1})])),_:1})])),_:1})])),_:1},8,["theme"])}}}),va="request",ha={},ga={},fa=(e,t,a=300)=>{ha[e]&&clearTimeout(ha[e]),ha[e]=setTimeout((()=>{t()}),a)},wa=(e,t,a=1e3)=>{const l=Date.now();(!ga[e]||l-ga[e]>=a)&&(t(),ga[e]=l)},{message:ba}=ee(["message"]),ya=We.create({baseURL:Jt.backend.baseURL,timeout:1e4,withCredentials:!0,headers:{"Content-Type":"application/json"}});ya.interceptors.request.use(),ya.interceptors.response.use((e=>e),(e=>{const{response:t}=e;if(!t)return fa(va+"net",(()=>{ba.warning("网络错误，请稍后重试")})),Promise.reject(null);{const e=t.status;if(401===e)return aa.removeLoginUser(),fa(va+401,(()=>{ba.warning("登录已过期，请重新登录")})),Ic.push("/login"),document.cookie.split(";").forEach((function(e){const t=e.split("=")[0].trim();document.cookie=`${t}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=/`})),Promise.reject(null);if(403===e)return ba.warning(t.data.message),Promise.reject(null)}return Promise.reject(e)}));const ka=(e,t,a)=>ya.get(e,{...a,params:t}),xa=(e,t)=>ya.post(e,t),Ca=(e,t,a)=>ya.post(e,t,a),La=(e,t)=>ya.put(e,t),Sa=(e,t,a)=>ya.put(e,t,a),Ta=(e,t)=>ya.patch(e,t),Ma=(e,t)=>ya.delete(e,{params:t}),Ra={URL:"/core/files",imageTypes:["image/png","image/jpg","image/jpeg","image/gif","image/webp"],upload:async(e,t)=>{const a=new FormData;a.append("bucket",t),a.append("file",e);return(await Ca(Ra.URL,a,{headers:{"Content-Type":"multipart/form-data"}})).data},uploadImage:async(e,t)=>Ra.imageTypes.includes(e.type)?Ra.upload(e,t):Promise.reject(new Error("文件类型不支持，请上传图片文件")),getResourceURL:e=>e?e.startsWith("http")?e:`${Jt.backend.resourceURL}${e}`:""},Aa="/thumbnail";const Ea=e=>e.replace(/\/+/g,"/").replace(/^\/+/,"/"),_a=e=>{if(!e)return"";if(!e.startsWith("http"))return Ea(e);if(e.includes("localhost")){const t=e.match(/\/osr(\/.*$)/);if(t&&t[1])return Ea(t[1])}if(!/^https?:\/\/[^\s]+$/.test(e)){Zt.warn("Invalid URL format:",e);const t=e.match(/\/[^?#]*/);return Ea(t?t[0]:e)}const t=new URL(e).pathname;return t.startsWith("/")?Ea(t.substring(1)):Ea(t)},Ia=(e,t=!1)=>{const a=_a(e),l=t?a:a.replace(Aa,"");return Ra.getResourceURL(l)},za=(e,t,a,l=!1)=>(Zt.debug("handleImageUpload started for file:",e.name,e.type),Ra.uploadImage(e,a).then((e=>{var a,n;let o=e.data;Zt.debug("Original image URL from server:",o),!l&&o.includes(Aa)&&(o=o.replace(Aa,""),Zt.debug("Removed thumbnail from URL:",o));const i=(null==(a=t.storage.image)?void 0:a.transformSrc)?t.storage.image.transformSrc(o):_a(o);Zt.debug("Converted to relative path:",i);const r=document.createElement("img");r.onerror=e=>{Zt.error("Image loading error:",e)},r.onload=()=>{let e=r.width,a=r.height,l=1;if(Zt.debug("Original image dimensions:",{width:e,height:a,aspectRatio:e/a}),e>600||a>800){const t=600/e,n=800/a;l=Math.min(t,n),e=Math.round(e*l),a=Math.round(a*l)}Zt.debug("Adjusted image dimensions:",{width:e,height:a,ratio:l}),Zt.debug("Setting image with relative path:",i);const n={src:i,width:`${e}px`,height:`${a}px`};t.commands.setImage(n)};const s=(null==(n=t.storage.image)?void 0:n.getFullUrl)?t.storage.image.getFullUrl(o):Ia(o,l);return Zt.debug("Loading image with full URL:",s),r.src=s,i}))),Ba={replaceImageUrls:(e,t=!1)=>{var a;null==(a=null==e?void 0:e.content)||a.forEach((e=>{"image"===e.type&&e.attrs&&e.attrs.src&&(e.attrs.src=t?Ia(e.attrs.src,!1):_a(e.attrs.src)),e.content&&Ba.replaceImageUrls(e,t)}))},toJsonString:e=>{const t=JSON.parse(JSON.stringify(e)),a=e=>{if(e){if("image"===e.type&&e.attrs&&e.attrs.src){const t=e.attrs.src;(()=>{if(t.includes("/thumbnail"))return!1;const e=document.querySelectorAll("img[data-relative-src]");for(const a of e){const e=a.dataset.relativeSrc;if(e&&e.includes("/thumbnail")&&e.replace("/thumbnail","")===t)return!0}return!1})()?(e.attrs.src="/thumbnail"+t,Zt.debug("Restored thumbnail path:",{original:t,thumbnail:e.attrs.src})):(e.attrs.src=_a(t),Zt.debug("Image URL transformed:",{original:t,transformed:e.attrs.src}))}e.content&&Array.isArray(e.content)&&e.content.forEach(a)}};return a(t),ta.stringify(t)},toJsonObject:e=>{if(!e)return Zt.warn("Empty content passed to toJsonObject"),{type:"doc",content:[{type:"paragraph",content:[]}]};try{if("object"==typeof e)return e;const t=ta.parse(e);return t||(Zt.warn("Failed to parse content in toJsonObject, creating fallback structure"),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"无法显示内容"}]}]})}catch(t){return Zt.error("Error in toJsonObject:",t),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"解析错误"}]}]}}},serializeContent:e=>{let t="";if(!e)return t;return function e(a){var l;"text"===a.type?t+=a.text:"mention"===a.type?t+="@"+(null==(l=a.attrs)?void 0:l.label):"image"===a.type?t+="[图片]":a.content&&Array.isArray(a.content)&&a.content.forEach((t=>e(t)))}(e),t}},Pa={class:"bilibili-wrapper"},Da=["src","data-danmaku","data-mute"],$a={key:1,class:"bilibili-error"},Ua=(e,t)=>{const a=e.__vccOpts||e;for(const[l,n]of t)a[l]=n;return a},Fa=Ua(o({__name:"BilibiliNodeView",props:{node:{},decorations:{},selected:{type:Boolean},updateAttributes:{type:Function},deleteNode:{type:Function},view:{},getPos:{type:Function},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e){const t=e,a=i((()=>{const e=(e=>{if(!e)return null;const t=e.match(/(BV[a-zA-Z0-9]{10})/),a=e.match(/aid=(\d+)/),l=e.match(/b23\.tv\/([a-zA-Z0-9]+)/),n=e.match(/bilibili\.com\/video\/([a-zA-Z0-9]+)/);return t?t[1]:a?a[1]:l?l[1]:n?n[1]:null})(t.node.attrs.src);if(!e)return null;return`//www.bilibili.com/blackboard/html5mobileplayer.html?${new URLSearchParams({[e.startsWith("BV")?"bvid":"aid"]:e,page:"1",danmaku:t.node.attrs.danmaku?"1":"0",mute:t.node.attrs.mute?"1":"0",high_quality:"1"}).toString()}`}));return(e,t)=>(m(),u(h(Je),{class:"bilibili-container"},{default:p((()=>[g("div",Pa,[a.value?(m(),f("iframe",{key:0,src:a.value,scrolling:"no",border:"0",frameborder:"0",framespacing:"0",allowfullscreen:"true","data-danmaku":e.node.attrs.danmaku,"data-mute":e.node.attrs.mute,class:"bilibili-iframe"},null,8,Da)):(m(),f("div",$a,t[0]||(t[0]=[g("p",null,"无效的 Bilibili 视频链接",-1)])))])])),_:1}))}}),[["__scopeId","data-v-65da1327"]]),Va=e=>{const t=e.match(/(BV[a-zA-Z0-9]{10})/),a=e.match(/aid=(\d+)/),l=e.match(/b23\.tv\/([a-zA-Z0-9]+)/),n=e.match(/bilibili\.com\/video\/([a-zA-Z0-9]+)/);return t?t[1]:a?a[1]:l?l[1]:n?n[1]:null},Oa=Ke.create({name:"bilibili",group:"block",content:"text*",inline:!1,atom:!0,addAttributes:()=>({src:{default:null,parseHTML:e=>{const t=e.getAttribute("src");return t?Va(t):null}},danmaku:{default:!0},mute:{default:!1}}),parseHTML:()=>[{tag:"iframe[src]",getAttrs:e=>({src:e.getAttribute("src"),danmaku:"true"===e.getAttribute("data-danmaku"),mute:"true"===e.getAttribute("data-mute")})}],renderHTML({node:e}){const{src:t,danmaku:a,mute:l}=e.attrs,n=Va(t);if(!n)return["div",{class:"bilibili-error"},"无效的 Bilibili 视频链接"];return["div",{class:"bilibili-container"},["div",{class:"bilibili-wrapper"},["iframe",{src:`//www.bilibili.com/blackboard/html5mobileplayer.html?${new URLSearchParams({[n.startsWith("BV")?"bvid":"aid"]:n,page:"1",danmaku:a?"1":"0",mute:l?"1":"0",high_quality:"1"}).toString()}`,scrolling:"no",border:"0",frameborder:"0",framespacing:"0",allowfullscreen:"true","data-danmaku":a,"data-mute":l,class:"bilibili-iframe"}]]]},addNodeView:()=>Xe(Fa),addCommands(){return{setBilibiliVideo:e=>({commands:t})=>t.insertContent({type:this.name,attrs:e})}}}),Ha={class:"code-block-header"},ja={class:"code-block-language-container"},Na={key:1,class:"code-block-language",contenteditable:"false",style:{userSelect:"none",pointerEvents:"none"}},qa={class:"code-block-toolbar"},Ya=["title"],Wa=["title"],Ja=Ua(o({__name:"CodeBlockToolbar",props:{language:{},wrapMode:{type:Boolean},copyState:{},isEditable:{type:Boolean}},emits:["toggle-wrap","copy-code","language-change"],setup(e,{emit:t}){const a=t,l=[{label:"Plain Text",value:"text"},{label:"JavaScript",value:"javascript"},{label:"TypeScript",value:"typescript"},{label:"HTML",value:"html"},{label:"CSS",value:"css"},{label:"SCSS",value:"scss"},{label:"Vue",value:"vue"},{label:"Java",value:"java"},{label:"Python",value:"python"},{label:"C++",value:"cpp"},{label:"C",value:"c"},{label:"C#",value:"csharp"},{label:"Go",value:"go"},{label:"Rust",value:"rust"},{label:"PHP",value:"php"},{label:"Ruby",value:"ruby"},{label:"Swift",value:"swift"},{label:"Kotlin",value:"kotlin"},{label:"Dart",value:"dart"},{label:"Shell",value:"bash"},{label:"PowerShell",value:"powershell"},{label:"SQL",value:"sql"},{label:"JSON",value:"json"},{label:"XML",value:"xml"},{label:"YAML",value:"yaml"},{label:"Markdown",value:"markdown"},{label:"Dockerfile",value:"dockerfile"},{label:"Nginx",value:"nginx"},{label:"Apache",value:"apache"}],n=e=>{a("language-change",e)};return(e,t)=>(m(),f("div",Ha,[g("div",ja,[e.isEditable?(m(),u(h(te),{key:0,value:e.language,options:l,size:"small",class:"code-language-select","consistent-menu-width":!1,"onUpdate:value":n},null,8,["value"])):(m(),f("span",Na,w(e.language),1))]),g("div",qa,[g("button",{class:b(["code-wrap-button",{active:e.wrapMode}]),title:e.wrapMode?"禁用换行":"启用换行",onClick:t[0]||(t[0]=t=>e.$emit("toggle-wrap"))},t[2]||(t[2]=[y('<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" data-v-ec32a7e7><polyline points="17 2 21 6 17 10" data-v-ec32a7e7></polyline><path d="M3 11V9a4 4 0 0 1 4-4h14" data-v-ec32a7e7></path><polyline points="7 22 3 18 7 14" data-v-ec32a7e7></polyline><path d="M21 13v2a4 4 0 0 1-4 4H3" data-v-ec32a7e7></path></svg>',1)]),10,Ya),g("button",{class:b(["code-copy-button",{active:e.copyState.copied}]),title:e.copyState.copied?"已复制":"复制代码",onClick:t[1]||(t[1]=t=>e.$emit("copy-code"))},t[3]||(t[3]=[g("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},[g("rect",{x:"9",y:"9",width:"13",height:"13",rx:"2",ry:"2"}),g("path",{d:"M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"})],-1)]),10,Wa)])]))}}),[["__scopeId","data-v-ec32a7e7"]]),Ka=Ua(o({__name:"CodeBlockNodeView",props:{decorations:{},selected:{type:Boolean},updateAttributes:{type:Function},deleteNode:{type:Function},node:{},view:{},getPos:{type:Function},innerDecorations:{},editor:{},extension:{},HTMLAttributes:{}},setup(e){const t=e,a=n(!1),l=n({copied:!1,timer:null}),o=i((()=>t.editor.isEditable)),s=i((()=>{const e=t.node.attrs.language||"text";return"null"===e?"text":e})),c=i((()=>({display:"block",padding:"0.8rem 1rem",margin:"0",background:"transparent",border:"none",borderRadius:"0",fontFamily:"inherit",fontSize:"inherit",lineHeight:"inherit",whiteSpace:a.value?"pre-wrap":"pre",wordBreak:a.value?"break-word":"normal",overflowWrap:a.value?"break-word":"normal",width:"100%",boxSizing:"border-box"}))),d=()=>{a.value=!a.value},f=async()=>{try{const e=t.node.textContent||"";await navigator.clipboard.writeText(e),ba.success("代码已复制到剪贴板"),l.value.copied=!0,fa("tiptap-code-copy",(()=>{l.value.copied=!1}),3e3)}catch(e){ba.error("复制失败，请手动复制")}},w=e=>{t.updateAttributes&&t.updateAttributes({language:e})};return r((()=>{void 0!==t.node.attrs.wrap&&null!==t.node.attrs.wrap&&(a.value=Boolean(t.node.attrs.wrap))})),(e,t)=>(m(),u(h(Je),{as:"pre",class:b(["code-block-container",{"code-wrap":a.value,"code-block-readonly":!o.value,"editable-mode":o.value,"readonly-mode":!o.value}]),"data-language":s.value,"data-selectable":!1},{default:p((()=>[v(Ja,{language:s.value,"wrap-mode":a.value,"copy-state":l.value,"is-editable":o.value,onToggleWrap:d,onCopyCode:f,onLanguageChange:w},null,8,["language","wrap-mode","copy-state","is-editable"]),g("div",{class:b(["code-scrollbar-container",{"code-wrap":a.value}])},[v(h(ae),{"x-scrollable":!a.value,"y-scrollable":!1,trigger:"hover",size:8},{default:p((()=>[v(h(Ge),{as:"code",class:b([`language-${s.value}`,{"code-wrap-enabled":a.value,"code-selectable":!o.value}]),style:k(c.value)},null,8,["class","style"])])),_:1},8,["x-scrollable"])],2)])),_:1},8,["class","data-language"]))}}),[["__scopeId","data-v-2743c4fd"]]),Xa=Ze.extend({selectable:!1,atom:!1,draggable:!1,addOptions(){var e;return{...null==(e=this.parent)?void 0:e.call(this),HTMLAttributes:{class:"code-block-readonly","data-selectable":"false"},exitOnTripleEnter:!1,exitOnArrowDown:!1}},addNodeView:()=>Xe(Ka),addKeyboardShortcuts(){var e;return{...(null==(e=this.parent)?void 0:e.call(this))||{},Enter:({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:l,empty:n}=a;return!(!n||l.parent.type!==this.type)&&e.commands.first((({commands:e})=>[()=>e.newlineInCode()]))},"Shift-Enter":({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:l,empty:n}=a;if(!n||l.parent.type!==this.type)return!1;const o=l.before();return e.chain().insertContentAt(o,{type:"paragraph"}).command((({tr:e})=>{const t=o;return e.setSelection($t.near(e.doc.resolve(t))),!0})).run()},"Ctrl-Enter":({editor:e})=>{const{state:t}=e,{selection:a}=t,{$from:l,empty:n}=a;if(!n||l.parent.type!==this.type)return!1;const o=l.after();return e.chain().insertContentAt(o,{type:"paragraph"}).command((({tr:e})=>{const t=o+1;return e.setSelection($t.near(e.doc.resolve(t))),!0})).run()}}}}),Ga=Qe.create({name:"fullscreen"}),Za={passive:!0},Qa=new Set(["wheel","touchstart","touchmove","touchend","touchcancel","scroll"]),el=new Set(["wheel"]);function tl(e,t){return function(e,t,a,l={},n=!1){const o=!n&&Qa.has(t),i={...Za,...l,passive:o};(n||el.has(t))&&(i.passive=!1);try{e.addEventListener(t,a,i)}catch(r){e.addEventListener(t,a,i.passive)}}(e,"wheel",t,{},!0),()=>{!function(e,t,a,l={}){try{e.removeEventListener(t,a,l)}catch(n){}}(e,"wheel",t)}}function al(e,t,a){const l=tl(e,a),n=a=>{a.target===e&&t()};e.addEventListener("click",n);const o=e=>{"Escape"===e.key&&t()};return document.addEventListener("keydown",o),()=>{l(),e.removeEventListener("click",n),document.removeEventListener("keydown",o)}}const ll={minScale:.5,maxScale:3};function nl(e,t){const a=e.clientX-t.clientX,l=e.clientY-t.clientY;return Math.sqrt(a*a+l*l)}function ol(e,t){return{x:(e.clientX+t.clientX)/2,y:(e.clientY+t.clientY)/2}}function il(e,t={},a,l){const n=function(e={}){const{minScale:t=ll.minScale,maxScale:a=ll.maxScale}=e;return{state:{scale:1,translateX:0,translateY:0,isDragging:!1,isZooming:!1},dragVars:{dragStartX:0,dragStartY:0,startTranslateX:0,startTranslateY:0,hasDragged:!1,dragThreshold:5},scaleVars:{isScaling:!1,scaleStartDistance:0,scaleStartScale:1,scaleCenterX:0,scaleCenterY:0},performanceVars:{animationFrameId:null,pendingTransform:!1},options:{minScale:t,maxScale:a}}}(t),o=function(e,t){const{state:a,performanceVars:l,options:n}=t,{minScale:o,maxScale:i}=n,r=()=>{l.pendingTransform||(l.pendingTransform=!0,l.animationFrameId=requestAnimationFrame((()=>{e.style.transform=`translate3d(${a.translateX}px, ${a.translateY}px, 0) scale(${a.scale})`,l.pendingTransform=!1})))};return{updateTransform:r,resetTransform:()=>{l.animationFrameId&&(cancelAnimationFrame(l.animationFrameId),l.animationFrameId=null),a.scale=1,a.translateX=0,a.translateY=0,a.isDragging=!1,a.isZooming=!1,t.scaleVars.isScaling=!1,l.pendingTransform=!1,e.style.transform="",e.style.cursor="",e.style.willChange="",e.classList.remove("dragging","zooming")},handleWheelZoom:t=>{t.preventDefault();const l=t.deltaY>0?.9:1.1,n=Math.max(o,Math.min(i,a.scale*l));if(Math.abs(n-a.scale)>.01){const l=e.getBoundingClientRect(),o=t.clientX-l.left,i=t.clientY-l.top,s=(o-l.width/2-a.translateX)/a.scale,c=(i-l.height/2-a.translateY)/a.scale;a.scale=n,a.translateX=o-l.width/2-s*a.scale,a.translateY=i-l.height/2-c*a.scale,r()}}}}(e,n),i=function(e,t,a,l){const{state:n,dragVars:o}=t,{updateTransform:i}=a,r=e=>{0===e.button&&(e.preventDefault(),e.stopPropagation(),n.isDragging=!0,o.hasDragged=!1,o.dragStartX=e.clientX,o.dragStartY=e.clientY,o.startTranslateX=n.translateX,o.startTranslateY=n.translateY,document.addEventListener("mousemove",s,{passive:!1}),document.addEventListener("mouseup",c,{passive:!0}))},s=t=>{if(!n.isDragging)return;t.preventDefault();const a=t.clientX-o.dragStartX,l=t.clientY-o.dragStartY;!o.hasDragged&&(Math.abs(a)>o.dragThreshold||Math.abs(l)>o.dragThreshold)&&(o.hasDragged=!0,e.style.cursor="grabbing",e.classList.add("dragging")),o.hasDragged&&(n.translateX=o.startTranslateX+a,n.translateY=o.startTranslateY+l,i())},c=()=>{const t=!o.hasDragged;n.isDragging=!1,e.style.cursor="grab",e.classList.remove("dragging"),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",c),t&&l&&l()};return{handleMouseDown:r,handleMouseMove:s,handleMouseUp:c,addMouseEventListeners:()=>{e.addEventListener("mousedown",r,{passive:!1}),e.style.cursor="grab"},removeMouseEventListeners:()=>{e.removeEventListener("mousedown",r),document.removeEventListener("mousemove",s),document.removeEventListener("mouseup",c)}}}(e,n,o,a),r=function(e,t,a,l){const{state:n,dragVars:o,scaleVars:i,options:r}=t,{updateTransform:s}=a,{minScale:c,maxScale:d}=r,u=t=>{if(1===t.touches.length){t.preventDefault(),n.isDragging=!0,o.hasDragged=!1;const e=t.touches[0];o.dragStartX=e.clientX,o.dragStartY=e.clientY,o.startTranslateX=n.translateX,o.startTranslateY=n.translateY}else if(2===t.touches.length){t.preventDefault(),n.isDragging=!1,n.isZooming=!0,i.isScaling=!0,i.scaleStartDistance=nl(t.touches[0],t.touches[1]),i.scaleStartScale=n.scale;const a=ol(t.touches[0],t.touches[1]),l=e.getBoundingClientRect(),o=a.x-l.left,r=a.y-l.top;i.scaleCenterX=(o-l.width/2-n.translateX)/n.scale,i.scaleCenterY=(r-l.height/2-n.translateY)/n.scale,e.classList.remove("dragging"),e.classList.add("zooming")}},m=t=>{if(2===t.touches.length){t.preventDefault(),n.isDragging=!1,n.isZooming=!0,i.isScaling=!0,i.scaleStartDistance=nl(t.touches[0],t.touches[1]),i.scaleStartScale=n.scale;const a=ol(t.touches[0],t.touches[1]),l=e.getBoundingClientRect(),o=a.x-l.left,r=a.y-l.top;i.scaleCenterX=(o-l.width/2-n.translateX)/n.scale,i.scaleCenterY=(r-l.height/2-n.translateY)/n.scale,e.classList.remove("dragging"),e.classList.add("zooming")}},p=t=>{if(1===t.touches.length&&n.isDragging&&!n.isZooming){t.preventDefault();const a=t.touches[0],l=a.clientX-o.dragStartX,i=a.clientY-o.dragStartY;!o.hasDragged&&(Math.abs(l)>o.dragThreshold||Math.abs(i)>o.dragThreshold)&&(o.hasDragged=!0,e.classList.add("dragging")),o.hasDragged&&(n.translateX=o.startTranslateX+l,n.translateY=o.startTranslateY+i,s())}else if(2===t.touches.length&&n.isZooming&&i.isScaling){t.preventDefault();const a=nl(t.touches[0],t.touches[1])/i.scaleStartDistance;let l=i.scaleStartScale*a;if(l=Math.max(c,Math.min(d,l)),Math.abs(l-n.scale)>.001){const t=e.getBoundingClientRect(),a=t.left+t.width/2+i.scaleCenterX*n.scale+n.translateX,o=t.top+t.height/2+i.scaleCenterY*n.scale+n.translateY;n.scale=l,n.translateX=a-t.left-t.width/2-i.scaleCenterX*n.scale,n.translateY=o-t.top-t.height/2-i.scaleCenterY*n.scale,s()}}},v=t=>{if(0===t.touches.length){const t=n.isDragging&&!o.hasDragged;n.isDragging=!1,n.isZooming=!1,i.isScaling=!1,e.classList.remove("dragging","zooming"),t&&l&&l()}else 1===t.touches.length&&(n.isZooming=!1,i.isScaling=!1,e.classList.remove("zooming"))};return{handleTouchStart:u,handleModalTouchStart:m,handleTouchMove:p,handleTouchEnd:v,addTouchEventListeners:t=>{e.addEventListener("touchstart",u,{passive:!1}),t?(t.addEventListener("touchstart",m,{passive:!1}),t.addEventListener("touchmove",p,{passive:!1}),t.addEventListener("touchend",v,{passive:!0}),t.addEventListener("touchcancel",v,{passive:!0})):(e.addEventListener("touchmove",p,{passive:!1}),e.addEventListener("touchend",v,{passive:!0}),e.addEventListener("touchcancel",v,{passive:!0}))},removeTouchEventListeners:t=>{e.removeEventListener("touchstart",u),t?(t.removeEventListener("touchstart",m),t.removeEventListener("touchmove",p),t.removeEventListener("touchend",v),t.removeEventListener("touchcancel",v)):(e.removeEventListener("touchmove",p),e.removeEventListener("touchend",v),e.removeEventListener("touchcancel",v))}}}(e,n,o,a);return{state:n.state,handleWheelZoom:o.handleWheelZoom,initialize:()=>{e.style.willChange="transform",e.style.backfaceVisibility="hidden",e.style.perspective="1000px",i.addMouseEventListeners(),r.addTouchEventListeners(l)},cleanup:()=>{o.resetTransform(),i.removeMouseEventListeners(),r.removeTouchEventListeners(l)},resetTransform:o.resetTransform,updateTransform:o.updateTransform}}const rl=["src","alt","data-relative-src","data-original-src"],sl=Ua(o({__name:"ImageElement",props:{src:{},alt:{},width:{},height:{},isResizing:{type:Boolean},resizeWidth:{},resizeHeight:{},useThumbnail:{type:Boolean}},emits:["doubleClick","click","load","error"],setup(e,{expose:t}){const a=e,l=n(),o=i((()=>(e=>{if(!e)return"";if(!e.startsWith("http"))return e;try{return new URL(e).pathname}catch{return e}})(a.src))),r=i((()=>{return(e=o.value)?e.startsWith("http")?e:Ra.getResourceURL(e):"";var e})),s=i((()=>({display:"block",maxWidth:"100%",margin:"0",padding:"0",verticalAlign:"baseline",transform:"translateY(0)",transition:"none",willChange:"transform",lineHeight:"normal",fontSize:"inherit",width:a.isResizing?a.resizeWidth:a.width||"",height:a.isResizing?a.resizeHeight:a.height||"",imageRendering:"auto",objectFit:"contain"})));return t({imageElement:l}),(e,t)=>(m(),f("img",{ref_key:"imageElement",ref:l,src:r.value,alt:e.alt||"",style:k(s.value),"data-relative-src":o.value,"data-original-src":o.value,contenteditable:"false",loading:"eager",class:"cursor-pointer",onDblclick:t[0]||(t[0]=t=>e.$emit("doubleClick",t)),onClick:t[1]||(t[1]=t=>e.$emit("click",t)),onLoad:t[2]||(t[2]=t=>e.$emit("load",t)),onError:t[3]||(t[3]=t=>e.$emit("error",t))},null,44,rl))}}),[["__scopeId","data-v-39865b3e"]]),cl=["data-handle-position","onMousedown","onTouchstart"],dl=["data-handle-position","onMousedown","onTouchstart"],ul=Ua(o({__name:"ImageResizeHandles",props:{isEditable:{type:Boolean},isSelected:{type:Boolean}},emits:["resizeStart","touchStart"],setup(e){const t=e,a=x(["top-left","top-right","bottom-left","bottom-right"]),l=x(["top","right","bottom","left"]),n=e=>({"top-left":"nw-resize","top-right":"ne-resize","bottom-left":"sw-resize","bottom-right":"se-resize",top:"n-resize",bottom:"s-resize",left:"w-resize",right:"e-resize"}[e]||"default"),o=e=>{const o={position:"absolute",background:"#2d8cf0",border:"1px solid white",borderRadius:"50%",zIndex:"100",display:t.isSelected&&t.isEditable?"block":"none",visibility:t.isSelected&&t.isEditable?"visible":"hidden",opacity:t.isSelected&&t.isEditable?"1":"0"};if(a.includes(e)){const t={width:"8px",height:"8px",cursor:n(e)},a={"top-left":{top:"-1px",left:"-1px"},"top-right":{top:"-1px",right:"-1px"},"bottom-left":{bottom:"-1px",left:"-1px"},"bottom-right":{bottom:"-1px",right:"-1px"}};return{...o,...t,...a[e]}}if(l.includes(e)){const t={width:"6px",height:"6px",cursor:n(e)},a={top:{top:"-1px",left:"50%",transform:"translateX(-50%)"},bottom:{bottom:"-1px",left:"50%",transform:"translateX(-50%)"},left:{left:"-1px",top:"50%",transform:"translateY(-50%)"},right:{right:"-1px",top:"50%",transform:"translateY(-50%)"}};return{...o,...t,...a[e]}}return o};return(e,t)=>e.isEditable&&e.isSelected?(m(),f(L,{key:0},[(m(!0),f(L,null,S(h(a),(t=>(m(),f("div",{key:t,class:b(["resize-handle",`handle-${t}`]),"data-handle-position":t,"data-center-handle":!1,style:k(o(t)),onMousedown:a=>e.$emit("resizeStart",a,t),onTouchstart:a=>e.$emit("touchStart",a,t)},null,46,cl)))),128)),(m(!0),f(L,null,S(h(l),(t=>(m(),f("div",{key:t,class:b(["resize-handle",`handle-${t}`]),"data-handle-position":t,"data-center-handle":!0,style:k(o(t)),onMousedown:a=>e.$emit("resizeStart",a,t),onTouchstart:a=>e.$emit("touchStart",a,t)},null,46,dl)))),128))],64)):C("",!0)}}),[["__scopeId","data-v-e8fc578d"]]);const ml={key:0,class:"resize-info"},pl=Ua(o({__name:"ImageNodeView",props:{node:{},updateAttributes:{type:Function},selected:{type:Boolean},editor:{},useThumbnail:{type:Boolean},decorations:{},deleteNode:{type:Function},view:{},getPos:{type:Function},innerDecorations:{},extension:{},HTMLAttributes:{}},setup(e,{expose:t}){const a=e,l=n(),o=n(),s=i((()=>a.editor.isEditable)),c=i((()=>a.selected)),d=T(!1),g=T(""),y=T(""),{handleResizeStart:x,handleTouchStart:L,updateResizeState:S}=function(e){const{isResizing:t,resizeWidth:a,resizeHeight:l,updateAttributes:o,node:i}=e,r=n(0),s=n(0),c=n(0),d=n(0),u=n(null),m=n(1),p=e=>{if(!t.value||!u.value)return;const a=e.clientX-r.value,l=e.clientY-s.value;h(a,l)},v=e=>{if(!t.value||!u.value)return;const a=e.touches[0];if(!a)return;const l=a.clientX-r.value,n=a.clientY-s.value;h(l,n)},h=(e,t)=>{if(!u.value)return;let n=c.value,o=d.value;switch(u.value){case"top-left":n=c.value-e,o=d.value-t;break;case"top-right":n=c.value+e,o=d.value-t;break;case"bottom-left":n=c.value-e,o=d.value+t;break;case"bottom-right":n=c.value+e,o=d.value+t;break;case"top":o=d.value-t,n=o*m.value;break;case"bottom":o=d.value+t,n=o*m.value;break;case"left":n=c.value-e,o=n/m.value;break;case"right":n=c.value+e,o=n/m.value}n=Math.max(50,n),o=Math.max(50,o),n=Math.min(800,n),o=Math.min(600,o),a.value=`${n}px`,l.value=`${o}px`},g=()=>{w()},f=()=>{w()},w=()=>{t.value&&(a.value&&l.value&&o({width:a.value,height:l.value}),t.value=!1,u.value=null,a.value="",l.value="",document.removeEventListener("mousemove",p),document.removeEventListener("mouseup",g),document.removeEventListener("touchmove",v),document.removeEventListener("touchend",f),document.body.style.overflow="")};return{handleResizeStart:(e,a)=>{e.preventDefault(),e.stopPropagation(),t.value=!0,u.value=a,r.value=e.clientX,s.value=e.clientY;const l=parseInt(i.attrs.width)||200,n=parseInt(i.attrs.height)||150;c.value=l,d.value=n,m.value=l/n,document.addEventListener("mousemove",p),document.addEventListener("mouseup",g),document.body.style.overflow="hidden"},handleTouchStart:(e,a)=>{e.preventDefault(),e.stopPropagation();const l=e.touches[0];if(!l)return;t.value=!0,u.value=a,r.value=l.clientX,s.value=l.clientY;const n=parseInt(i.attrs.width)||200,o=parseInt(i.attrs.height)||150;c.value=n,d.value=o,m.value=n/o,document.addEventListener("touchmove",v),document.addEventListener("touchend",f),document.body.style.overflow="hidden"},updateResizeState:(e,n,o)=>{a.value=e,l.value=n,t.value=o}}}({isResizing:d,resizeWidth:g,resizeHeight:y,updateAttributes:a.updateAttributes,node:a.node}),R=()=>{var e;const t=null==(e=o.value)?void 0:e.imageElement;t&&a.node.attrs.src&&function(e,t){const a=document.createElement("div");a.classList.add("modal-overlay");const l=document.createElement("img");l.alt="图片预览",a.appendChild(l),document.body.appendChild(a),a.classList.add("modal-overlay-active");const n="true"===e.dataset.isOriginal,o=e.dataset.thumbnailSrc||t,i=t.startsWith("http://")||t.startsWith("https://");if(i||!o.includes(Aa)||n)l.src=e.src,l.style.opacity="1";else{l.src=e.src,l.style.opacity="0.5";const n=document.createElement("div");n.classList.add("loading-spinner"),a.appendChild(n);const o=new Image,i=Date.now(),r=500;o.onload=()=>{const e=Date.now()-i,t=()=>{n.style.display="none",l.src=o.src,l.style.opacity="1",l.dataset.originalFullUrl=o.src};e<r?setTimeout(t,r-e):t()},o.src=Ra.getResourceURL(t.replace(Aa,""))}const r=()=>{a.classList.remove("modal-overlay-active"),a.addEventListener("transitionend",(()=>{if(!a.classList.contains("modal-overlay-active")){if(!i&&l.dataset.originalFullUrl&&o.includes(Aa)&&!n){e.src=l.dataset.originalFullUrl,e.dataset.isOriginal="true";const a=t.replace(Aa,"");e.dataset.originalSrc=a}document.body.removeChild(a),c(),s.cleanup()}}),{once:!0})},s=il(l,{minScale:.5,maxScale:3},r,a);s.initialize();const c=al(a,r,s.handleWheelZoom)}(t,a.node.attrs.src)},A=()=>{s.value&&R()},E=()=>{s.value||R()},_=()=>{},I=e=>{},z=i((()=>({display:"inline-block",position:"relative",margin:"0",padding:"4px",verticalAlign:"baseline",lineHeight:"1",transform:"translateY(0)",transition:"none",zIndex:"1",willChange:"transform",boxSizing:"border-box",width:"fit-content",maxWidth:"100%",border:"0",userSelect:"none",pointerEvents:"auto"}))),B=e=>{if(e.stopPropagation(),!s.value)return;const{view:t}=a.editor;if(t){const e=t.posAtDOM(l.value,0);null!==e&&t.dispatch(t.state.tr.setSelection($t.near(t.state.doc.resolve(e))))}};return r((()=>{})),M((()=>{})),t({updateNode:e=>{var t,l;if(e.attrs.src,a.node.attrs.src,e.attrs.width!==a.node.attrs.width){const a=null==(t=o.value)?void 0:t.imageElement;a&&(a.style.width=e.attrs.width||"")}if(e.attrs.height!==a.node.attrs.height){const t=null==(l=o.value)?void 0:l.imageElement;t&&(t.style.height=e.attrs.height||"")}},updateResizeState:(e,t,a)=>{S(e,t,a)}}),(e,t)=>(m(),u(h(Je),{ref_key:"imageWrapper",ref:l,class:b(["image-wrapper",{"readonly-image":!s.value,"ProseMirror-selectednode":c.value&&s.value,resizing:d.value}]),style:k(z.value),onClick:B},{default:p((()=>[v(sl,{ref_key:"imageElementRef",ref:o,src:e.node.attrs.src,alt:e.node.attrs.alt,width:e.node.attrs.width,height:e.node.attrs.height,"is-resizing":d.value,"resize-width":g.value,"resize-height":y.value,"use-thumbnail":e.useThumbnail,onDoubleClick:A,onClick:E,onLoad:_,onError:I},null,8,["src","alt","width","height","is-resizing","resize-width","resize-height","use-thumbnail"]),v(ul,{"is-editable":s.value,"is-selected":c.value,onResizeStart:h(x),onTouchStart:h(L)},null,8,["is-editable","is-selected","onResizeStart","onTouchStart"]),d.value&&g.value&&y.value?(m(),f("div",ml,w(Math.round(parseFloat(g.value)))+" × "+w(Math.round(parseFloat(y.value))),1)):C("",!0)])),_:1},8,["class","style"]))}}),[["__scopeId","data-v-2e1577b4"]]),vl=(e=!1)=>et.extend({draggable:!0,inline:!0,group:"inline",atom:!0,addOptions(){var t;return{...null==(t=this.parent)?void 0:t.call(this),transformSrc:e=>_a(e),getFullUrl:t=>Ia(t,e)}},addAttributes(){var e;return{...null==(e=this.parent)?void 0:e.call(this),width:{default:null,renderHTML:e=>e.width?{width:e.width}:{}},height:{default:null,renderHTML:e=>e.height?{height:e.height}:{}},src:{default:null,parseHTML:e=>{const t=e.getAttribute("src")||"";return _a(t)},renderHTML:e=>({src:e.src})}}},addNodeView:()=>Xe(pl),addStorage:()=>({transformSrc:_a,getFullUrl:t=>Ia(t,e)})}).configure({allowBase64:!1}),hl=vl(!0);const gl=()=>{const e=n(),t=(e,t,a,l)=>{Zt.debug("files: ",e),e.length&&e.forEach((e=>{e.type.startsWith("image/")&&za(e,t,a,l).then((e=>{Zt.debug("Image uploaded, relative path:",e);const{handleImageUploadSuccess:a}=function(){const e=()=>{document.querySelectorAll(".image-wrapper.ProseMirror-selectednode").forEach((e=>{e.classList.remove("ProseMirror-selectednode")}))},t=e=>!!(e&&e.view&&e.view.dom)||(Zt.error("Editor view dom not found"),!1),a=(e,t)=>{var a;const l=e.view.dom.querySelectorAll(".image-wrapper img");Zt.debug("Found image elements:",l.length);let n=null;const o=null==(a=e.storage.image)?void 0:a.transformSrc;return Array.from(l).filter((e=>e instanceof HTMLImageElement)).forEach((e=>{const a=e.dataset.relativeSrc,l=e.getAttribute("src");Zt.debug("Checking img:",{dataRelativeSrc:a,src:l,lookingFor:t});let i=l||"";if(o&&l)i=o(l);else if(l&&l.startsWith("http"))try{i=new URL(l).pathname}catch{Zt.warn("Failed to parse URL:",l)}(a===t||i===t||t.endsWith(i))&&(Zt.debug("Found matching image element"),n=e.closest(".image-wrapper"))})),n},l=e=>{var t,a;Zt.debug("Found target wrapper, adding visual feedback"),null==(t=e.classList)||t.add("ProseMirror-selectednode"),null==(a=e.scrollIntoView)||a.call(e,{behavior:"smooth",block:"center"})},n=e=>{try{const t=document.createElement("div");t.className="upload-success-effect",t.style.cssText="\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        border-radius: 0.25rem;\n        background-color: rgba(90, 214, 150, 0.2);\n        z-index: 5;\n        pointer-events: none;\n        opacity: 0;\n      ",e.appendChild(t),t.animate([{opacity:.7},{opacity:0}],{duration:800,easing:"ease-out"}).onfinish=()=>{t.remove()},e.animate([{outline:"2px solid #5ad696",outlineOffset:"3px"},{outline:"2px solid #2d8cf0",outlineOffset:"2px"}],{duration:800,easing:"ease-out"})}catch(t){Zt.error("Error adding upload success animation:",t)}},o=e=>{var t,a;Zt.debug("No matching image found, selecting last wrapper");const l=e.view.dom.querySelectorAll(".image-wrapper");if(l.length>0){const e=l[l.length-1];null==(t=e.classList)||t.add("ProseMirror-selectednode"),null==(a=e.scrollIntoView)||a.call(e,{behavior:"smooth",block:"center"})}};return{handleImageUploadSuccess:(i,r)=>{setTimeout((()=>{try{if(e(),!t(i))return;const s=a(i,r);s?(l(s),n(s)):o(i)}catch(s){Zt.error("Error selecting image after upload:",s)}}),300)},clearSelectedImages:e,validateEditor:t,findTargetImageWrapper:a,selectAndScrollToImage:l,addUploadSuccessAnimation:n,selectLastImage:o}}();a(t,e)}))}))},{imageHandleCallback:a}=function(){const e=e=>e.filter((e=>e.type.startsWith("image/"))),t=e=>!(!e||!e.src),a=(e,t,a,l)=>{if(Zt.debug("Editor is already handling one image, skipping duplicate processing"),e.length>1){Zt.debug("Processing additional images:",e.length-1);for(let n=1;n<e.length;n++)za(e[n],t,a,l)}},l=(e,t,a,l)=>{Zt.debug("Processing all images:",e.length),e.forEach((e=>{za(e,t,a,l)}))};return{imageHandleCallback:(n,o,i,r,s)=>{if(Zt.debug("imageHandleCallback called with:",{filesCount:o.length,hasAttrs:!!i}),!o.length)return;const c=e(o);0!==c.length?t(i)?a(c,n,r,s):l(c,n,r,s):Zt.debug("No image files found in event")},filterImageFiles:e,shouldSkipFirstImage:t,processAdditionalImages:a,processAllImages:l}}();return{imageInputRef:e,handleImageChange:async(e,a,l,n)=>{const o=e.target;if(o.files&&o.files.length>0){const e=Array.from(o.files);t(e,a,l,n)}},handleImageChangeCallback:t,imageHandleCallback:a}},fl={URL:"/core/users",online:async()=>(await ka(fl.URL+"/online")).data,info:async()=>(await ka(fl.URL+"/me")).data,changeAvatar:async e=>{const t=new FormData;t.append("file",e);return(await Sa(fl.URL+"/me/avatar",t,{headers:{"Content-Type":"multipart/form-data"}})).data},updateNotificationReceiveType:async e=>(await La(fl.URL+"/me/notification-settings",{type:e})).data,searchUser:async e=>(await ka(fl.URL,{username:e})).data};class wl{constructor(e){t(this,"editor"),t(this,"options"),t(this,"element"),t(this,"index"),t(this,"nodes"),t(this,"items"),t(this,"command"),this.editor=e.editor,this.options=e}static create(e){return()=>new wl(e)}onStart(e){this.index=0,this.nodes=[],this.items=[],this.command=e.command,this.element=document.createElement("div"),this.element.classList.add("dropdown-menu");for(const t of this.options.classes??[])this.element.classList.add(t);for(const[t,a]of Object.entries(this.options.attributes??{}))this.element.setAttribute(t,a);document.body.appendChild(this.element),this.onUpdate(e)}onUpdate(e){this.element&&void 0!==this.index&&this.nodes&&this.items&&(this.items=e.items,this.command=e.command,this.render(),e.clientRect&&this.updatePosition(e.clientRect))}onKeyDown(e){if(!this.element||void 0===this.index||!this.nodes||!this.items)return!1;if("Escape"===e.event.key)return this.hide(),!0;if("Enter"===e.event.key){const e=this.items[this.index];return e&&this.command&&this.command({id:e.id,label:e.username,avatar:e.avatar}),!0}return"ArrowUp"===e.event.key?(this.selectItem(this.index-1<0?this.items.length-1:this.index-1,!0),!0):"ArrowDown"===e.event.key&&(this.selectItem(this.index+1>=this.items.length?0:this.index+1,!0),!0)}onExit(){this.hide(),this.cleanup()}selectItem(e,t){if(this.element&&void 0!==this.index&&this.nodes&&this.items){this.index=Math.max(0,Math.min(e,this.items.length-1));for(let e=0;e<this.nodes.length;e++)e===this.index?this.nodes[e].classList.add("is-selected"):this.nodes[e].classList.remove("is-selected");t&&this.nodes[this.index]&&Vt(this.nodes[this.index],{behavior:"smooth",block:"nearest",inline:"nearest",boundary:e=>e!==this.element})}}render(){var e;if(this.element&&void 0!==this.index&&this.items){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);if(this.index=Math.max(0,Math.min(this.index,Math.max(0,this.items.length-1))),this.items.length){this.nodes=[];for(let e=0;e<this.items.length;e++){const t=this.items[e],a=document.createElement("button");if(a.setAttribute("type","button"),t.avatar){const e=document.createElement("img");e.classList.add("dropdown-avatar"),e.src=Ra.getResourceURL(t.avatar),e.alt=t.username,e.loading="lazy",a.appendChild(e)}const l=document.createElement("span");l.textContent=t.username,a.appendChild(l),e===this.index&&a.classList.add("is-selected"),a.addEventListener("click",(()=>{this.command&&this.command({id:t.id,label:t.username,avatar:t.avatar})})),this.nodes.push(a)}for(const e of this.nodes)this.element.appendChild(e);this.show()}else{const t=document.createElement("div");t.classList.add("item"),t.textContent=(null==(e=this.options.dictionary)?void 0:e.empty)??"...",this.element.appendChild(t),this.show()}}}updatePosition(e){if(!this.element)return;const t=e();if(!t)return;const a=window.innerHeight,l=window.innerWidth;this.element.style.position="fixed",this.element.style.zIndex="10000",this.element.style.left=`${t.left}px`,this.element.style.top=`${t.bottom+8}px`,this.element.style.visibility="hidden",this.element.style.display="block";const n=this.element.getBoundingClientRect(),o=n.height,i=n.width;this.element.style.visibility="visible";let r=t.left,s=t.bottom+8,c=!1;const d=a-t.bottom-8,u=t.top-8;d<o&&u>o?(s=t.top-o-8,c=!0):d<o&&u<o&&(u>d&&(s=t.top-o-8,c=!0),(c&&u<o||!c&&d<o)&&(this.element.style.maxHeight=Math.max(u,d)-16+"px",this.element.style.overflowY="auto")),r+i>l&&(r=Math.max(8,l-i-8)),r<8&&(r=8),this.element.style.left=`${r}px`,this.element.style.top=`${s}px`,this.element.classList.toggle("dropdown-menu-above",c),this.element.classList.toggle("dropdown-menu-below",!c)}show(){this.element&&(this.element.style.display="block")}hide(){this.element&&(this.element.style.display="none")}cleanup(){this.element&&this.element.parentNode&&this.element.parentNode.removeChild(this.element),this.element=void 0,this.nodes=void 0,this.items=void 0,this.command=void 0}}const bl=tt.extend({name:"mention",addAttributes(){var e;return{...null==(e=this.parent)?void 0:e.call(this),avatar:{default:null,parseHTML:e=>e.getAttribute("data-avatar"),renderHTML:e=>e.avatar?{"data-avatar":e.avatar}:{}}}},renderHTML({node:e,HTMLAttributes:t}){const a=e.attrs,l=[["span",{class:"mention-name"},`@${a.label}`]];if(a.avatar){const e=Ra.getResourceURL(a.avatar);l.push(["img",{class:"mention-avatar",src:e,alt:a.label,loading:"lazy"},""])}return["span",{...t,"data-type":"mention","data-id":a.id,"data-label":a.label,"data-avatar":a.avatar||"",class:"mention",contenteditable:"false"},...l]}}).configure({suggestion:{items:async({query:e})=>{if(!e)return[];return(await fl.searchUser(e)).data||[]},render:()=>{let e;return{onStart:t=>{e=wl.create({editor:t.editor,dictionary:{empty:"..."}})(),e.onStart(t)},onUpdate:t=>{null==e||e.onUpdate(t)},onKeyDown:t=>(null==e?void 0:e.onKeyDown(t))??!1,onExit:()=>{null==e||e.onExit()}}}}});class yl{constructor(e){t(this,"editor"),t(this,"options"),t(this,"element"),t(this,"index"),t(this,"nodes"),t(this,"items"),this.editor=e.editor,this.options=e}static create(e){return()=>new yl(e)}onStart(e){this.index=0,this.nodes=[],this.items=[],this.element=document.createElement("div"),this.element.classList.add("slash-menu");for(const t of this.options.classes??[])this.element.classList.add(t);for(const[t,a]of Object.entries(this.options.attributes??{}))this.element.setAttribute(t,a);document.body.appendChild(this.element),this.onUpdate(e)}onUpdate(e){this.element&&void 0!==this.index&&this.nodes&&this.items&&(this.items=e.items,this.render(),e.clientRect&&this.updatePosition(e.clientRect))}onKeyDown(e){if(!this.element||void 0===this.index||!this.nodes||!this.items)return!1;if("Escape"===e.event.key)return this.hide(),!0;if("Enter"===e.event.key){const e=this.items[this.index];return e&&"string"!=typeof e&&e.action&&e.action(this.editor),!0}if("ArrowUp"===e.event.key){const e=this.index-1,t=this.items[e]&&"string"==typeof this.items[e]?e-1:e;return this.selectItem(t<0?this.items.length-1:t,!0),!0}if("ArrowDown"===e.event.key){const e=this.index+1,t=this.items[e]&&"string"==typeof this.items[e]?e+1:e;return this.selectItem(t>=this.items.length?0:t,!0),!0}return!1}onExit(){this.hide(),this.cleanup()}selectItem(e,t){if(this.element&&void 0!==this.index&&this.nodes&&this.items){this.index=Math.max(0,Math.min(e,this.items.length-1));for(let e=0;e<this.nodes.length;e++)e===this.index?this.nodes[e].setAttribute("data-active","true"):this.nodes[e].removeAttribute("data-active");t&&this.nodes[this.index]&&Vt(this.nodes[this.index],{block:"center",scrollMode:"if-needed",boundary:e=>e!==this.element})}}render(){var e;if(this.element&&void 0!==this.index&&this.items){for(;this.element.firstChild;)this.element.removeChild(this.element.firstChild);if(this.index=Math.max(0,Math.min(this.index,Math.max(0,this.items.length-1))),this.items.length){this.nodes=[];for(let e=0;e<this.items.length;e++){const t=this.items[e];if("|"===t){const e=document.createElement("div");e.classList.add("slash-menu-divider"),this.nodes.push(e)}else{const a=document.createElement("button");if(a.classList.add("slash-menu-button"),a.setAttribute("type","button"),t.icon){const e=document.createElement("div");e.classList.add("slash-menu-button-icon"),e.innerHTML=t.icon,a.appendChild(e)}const l=document.createElement("div");if(l.classList.add("slash-menu-button-name"),l.textContent=t.name,a.appendChild(l),t.shortcut){const e=document.createElement("div");e.classList.add("slash-menu-button-shortcut"),e.textContent=t.shortcut.replace(/mod/i,navigator.userAgent.includes("Mac")?"⌘":"Ctrl").replace(/ctrl|control/i,"Ctrl").replace(/cmd|command/i,"⌘").replace(/shift/i,"Shift").replace(/alt|option/i,"Alt"),a.appendChild(e)}e===this.index&&a.setAttribute("data-active","true"),a.addEventListener("click",(()=>{t.action&&t.action(this.editor)})),a.addEventListener("mouseover",(()=>{this.index!==e&&this.selectItem(e)})),this.nodes.push(a)}}this.element.append(...this.nodes),this.nodes[this.index]&&Vt(this.nodes[this.index],{block:"center",scrollMode:"if-needed",boundary:e=>e!==this.element})}else{const t=document.createElement("div");t.classList.add("slash-menu-empty"),t.textContent=(null==(e=this.options.dictionary)?void 0:e.empty)||"未找到结果",this.element.appendChild(t)}this.show()}}updatePosition(e){if(!this.element)return;const t=e();if(!t)return;const a=window.innerHeight,l=window.innerWidth;this.element.style.position="fixed",this.element.style.zIndex="10000",this.element.style.left=`${t.left}px`,this.element.style.top=`${t.bottom+8}px`,this.element.style.visibility="hidden",this.element.style.display="block";const n=this.element.getBoundingClientRect(),o=n.height,i=n.width;this.element.style.visibility="visible";let r=t.left,s=t.bottom+8,c=!1;const d=a-t.bottom-8,u=t.top-8;d<o&&u>o?(s=t.top-o-8,c=!0):d<o&&u<o&&(u>d&&(s=t.top-o-8,c=!0),(c&&u<o||!c&&d<o)&&(this.element.style.maxHeight=Math.max(u,d)-16+"px",this.element.style.overflowY="auto")),r+i>l&&(r=Math.max(8,l-i-8)),r<8&&(r=8),this.element.style.left=`${r}px`,this.element.style.top=`${s}px`,this.element.classList.toggle("slash-menu-above",c),this.element.classList.toggle("slash-menu-below",!c)}show(){this.element&&(this.element.style.display="block")}hide(){this.element&&(this.element.style.display="none")}cleanup(){this.element&&(this.element.remove(),this.element=void 0),this.index=void 0,this.items=void 0,this.nodes=void 0}}const kl=Qe.create({name:"slashMenu",addOptions:()=>({items:[],dictionary:{lineEmpty:"",lineSlash:" ...",queryEmpty:"..."},imageUploadTrigger:void 0,modalTrigger:void 0}),addProseMirrorPlugins(){return[at({editor:this.editor,pluginKey:new Ft(`${this.name}-suggestion`),char:"/",allow:({editor:e,state:t,range:a})=>{const{$from:l}=t.selection,n=l.parent;return"codeBlock"!==n.type.name&&"paragraph"===n.type.name},items:({query:e})=>{const t=[];for(const l of this.options.items)if("|"!==l){if(""!==e){const t=e.toLowerCase();if(!l.name.toLowerCase().includes(t)&&!l.keywords.toLowerCase().includes(t))continue}t.push({...l,action:t=>{const{state:a,dispatch:n}=t.view,o=a.selection.$from,i=o.pos-(e.length+1);n(a.tr.deleteRange(i,o.pos)),"image"===l.id&&this.options.imageUploadTrigger?this.options.imageUploadTrigger():l.action(t),t.view.focus()}})}else t.push(l);const a=[];for(let l=0;l<t.length;l++){const e=t[l];if("|"===e){if(0===l||l===t.length-1)continue;if("|"===t[l+1])continue;if(0===a.length)continue;if("|"===a[a.length-1])continue}a.push(e)}return a},render:yl.create({editor:this.editor,dictionary:{empty:this.options.dictionary.queryEmpty}})}),new Ut({key:new Ft(`${this.name}-placeholder`),props:{decorations:e=>{const{$from:t}=e.selection,a=t.parent;if("paragraph"!==a.type.name)return null;const l=[],n=0===a.content.size,o="/"===a.textContent;return 1===t.depth?(n&&l.push(Ot.node(t.start()-1,t.end()+1,{class:"slash-menu-placeholder","data-placeholder":this.options.dictionary.lineEmpty})),o&&l.push(Ot.node(t.start()-1,t.end()+1,{class:"slash-menu-placeholder","data-placeholder":` ${this.options.dictionary.lineSlash}`})),Ht.create(e.doc,l)):null}}})]}}),xl={TextHeader120Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M16.573 3.823a.75.75 0 0 0-1.058.53c-.255 1.138-1.308 2.608-2.681 3.523a.75.75 0 1 0 .832 1.248A8.769 8.769 0 0 0 15.5 7.47V15.5a.75.75 0 0 0 1.5 0V4.516a.75.75 0 0 0-.427-.693zM3.5 4.5a.75.75 0 1 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 1 0-1.5 0V9h-5V4.5z" fill="currentColor"></path></g></svg>',TextHeader220Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm11.25.75c-1.292 0-2.25 1.124-2.25 2.25a.75.75 0 0 1-1.5 0c0-1.874 1.551-3.75 3.75-3.75c1.403 0 2.475.793 2.973 1.915c.49 1.106.41 2.488-.33 3.72c-.385.643-.958 1.16-1.527 1.607c-.265.209-.545.414-.816.613l-.067.049c-.295.217-.582.43-.858.65c-.892.715-1.569 1.449-1.794 2.446h4.919a.75.75 0 0 1 0 1.5H11.5a.75.75 0 0 1-.75-.75c0-2.099 1.226-3.396 2.437-4.366c.303-.243.614-.473.909-.69l.062-.045c.276-.202.535-.393.78-.586c.534-.42.929-.799 1.169-1.199c.51-.85.52-1.718.244-2.341c-.27-.608-.822-1.023-1.601-1.023z" fill="currentColor"></path></g></svg>',TextHeader320Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm8.97 1.958c.086-.295.216-.573.467-.784c.245-.206.693-.424 1.563-.424c.777 0 1.257.3 1.555.648c.32.374.445.825.445 1.102c0 .356-.091.92-.448 1.38c-.327.423-.965.87-2.302.87a.75.75 0 0 0 0 1.5c.446 0 1.198.11 1.81.42c.59.298.94.711.94 1.33c0 .84-.258 1.385-.593 1.72c-.338.338-.824.53-1.407.53c-.68 0-1.152-.116-1.458-.3c-.275-.164-.47-.414-.557-.847a.75.75 0 1 0-1.47.294c.163.817.593 1.442 1.255 1.84c.632.379 1.41.513 2.23.513c.917 0 1.806-.308 2.468-.97c.665-.665 1.032-1.62 1.032-2.78c0-1.234-.695-2.034-1.481-2.512c.283-.201.522-.434.72-.689C17.868 8.485 18 7.551 18 7c0-.63-.25-1.428-.805-2.077c-.577-.675-1.472-1.173-2.695-1.173c-1.13 0-1.95.29-2.528.776c-.571.48-.816 1.078-.942 1.516a.75.75 0 0 0 1.44.416z" fill="currentColor"></path></g></svg>',TextBold20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5 4.5A1.5 1.5 0 0 1 6.5 3h3.88c2.364 0 4.12 1.934 4.12 4.12c0 .819-.247 1.606-.68 2.269c.842.749 1.427 1.849 1.427 3.241c0 2.775-2.318 4.37-4.367 4.37H6.5A1.5 1.5 0 0 1 5 15.5v-11zM8 6v2.25h2.38c.625 0 1.12-.516 1.12-1.13A1.12 1.12 0 0 0 10.38 6H8zm0 5.25V14h2.88c.691 0 1.367-.537 1.367-1.37c0-.84-.684-1.38-1.367-1.38H8z" fill="currentColor"></path></g></svg>',TextItalic20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M8 3.25a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-3.235L8.592 15.5h2.658a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3.235L11.408 4H8.75A.75.75 0 0 1 8 3.25z" fill="currentColor"></path></g></svg>',TextUnderline24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6 4.5a1 1 0 0 1 2 0v6.001c-.003 3.463 1.32 4.999 4.247 4.999c2.928 0 4.253-1.537 4.253-5v-6a1 1 0 1 1 2 0v6c0 4.54-2.18 7-6.253 7S5.996 15.039 6 10.5v-6zM7 21a1 1 0 1 1 0-2h10.5a1 1 0 1 1 0 2H7z" fill="currentColor"></path></g></svg>',TextStrikethrough20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M6.252 3.702A6.56 6.56 0 0 1 10 2.5c2.783 0 4.489 1.485 5.1 2.3a.75.75 0 0 1-1.2.9C13.511 5.182 12.217 4 10 4a5.06 5.06 0 0 0-2.877.923C6.331 5.489 6 6.105 6 6.5c0 .78.376 1.285 1.11 1.71c.18.105.377.2.586.29H5.162c-.408-.523-.662-1.178-.662-2c0-1.105.794-2.114 1.752-2.798zM16.5 10a.75.75 0 0 1 0 1.5h-1.662c.408.523.662 1.178.662 2c0 1.358-.874 2.376-1.912 3.014c-1.042.641-2.367.986-3.588.986c-1.142 0-2.133-.129-2.992-.498c-.877-.378-1.563-.982-2.132-1.836a.75.75 0 1 1 1.248-.832c.43.646.901 1.042 1.477 1.29c.594.255 1.354.376 2.4.376c.966 0 2.015-.28 2.801-.764C13.593 14.75 14 14.141 14 13.5c0-.78-.376-1.285-1.11-1.71c-.18-.105-.377-.2-.586-.29H3.5a.75.75 0 0 1 0-1.5h13z" fill="currentColor"></path></g></svg>',Code20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M12.937 4.052a.75.75 0 0 0-1.373-.604l-5.5 12.5a.75.75 0 1 0 1.372.604l5.5-12.5zm1.356 9.793a.75.75 0 0 1-.137-1.052L16.303 10l-2.148-2.793a.75.75 0 0 1 1.188-.914l2.5 3.25a.75.75 0 0 1 0 .915l-2.5 3.25a.75.75 0 0 1-1.051.137zm-8.586-7.69a.75.75 0 0 1 .137 1.053L3.696 10l2.148 2.793a.75.75 0 1 1-1.188.915l-2.5-3.25a.75.75 0 0 1 0-.915l2.5-3.25a.75.75 0 0 1 1.051-.137z" fill="currentColor"></path></g></svg>',Image28Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 28 28"><g fill="none"><path d="M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z" fill="currentColor"></path></g></svg>',ArrowUndo16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3 2.75a.75.75 0 0 1 1.5 0v3.095l2.673-2.673a4 4 0 0 1 5.657 5.656l-4.95 4.95a.75.75 0 1 1-1.06-1.06l4.95-4.95a2.5 2.5 0 0 0-3.536-3.536L5.966 6.5H8.25a.75.75 0 0 1 0 1.5h-4.4A.85.85 0 0 1 3 7.15v-4.4z" fill="currentColor"></path></g></svg>',ArrowRedo16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M13.002 2.75a.75.75 0 0 0-1.5 0v3.095L8.828 3.172a4 4 0 0 0-5.656 5.656l4.95 4.95a.75.75 0 1 0 1.06-1.06l-4.95-4.95a2.5 2.5 0 0 1 3.536-3.536L10.036 6.5H7.75a.75.75 0 0 0 0 1.5h4.4c.47 0 .85-.38.85-.85v-4.4z" fill="currentColor"></path></g></svg>',LineHorizontal120Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M2 9.75A.75.75 0 0 1 2.75 9h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 9.75z" fill="currentColor"></path></g></svg>',VideoClip24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6.25A3.25 3.25 0 0 1 5.25 3h13.5A3.25 3.25 0 0 1 22 6.25v11.5A3.25 3.25 0 0 1 18.75 21H5.25A3.25 3.25 0 0 1 2 17.75V6.25zm7.5 3.134v5.231c0 .57.61.932 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.658z" fill="currentColor"></path></g></svg>',FullScreenMaximize16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M4 3.5a.5.5 0 0 0-.5.5v1.614a.75.75 0 0 1-1.5 0V4a2 2 0 0 1 2-2h1.614a.75.75 0 0 1 0 1.5H4zm5.636-.75a.75.75 0 0 1 .75-.75H12a2 2 0 0 1 2 2v1.614a.75.75 0 0 1-1.5 0V4a.5.5 0 0 0-.5-.5h-1.614a.75.75 0 0 1-.75-.75zM2.75 9.636a.75.75 0 0 1 .75.75V12a.5.5 0 0 0 .5.5h1.614a.75.75 0 0 1 0 1.5H4a2 2 0 0 1-2-2v-1.614a.75.75 0 0 1 .75-.75zm10.5 0a.75.75 0 0 1 .75.75V12a2 2 0 0 1-2 2h-1.614a.75.75 0 1 1 0-1.5H12a.5.5 0 0 0 .5-.5v-1.614a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',ResizeSmall20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zm3 3A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zm6-3A1.5 1.5 0 0 1 16 5.5v1a.5.5 0 0 0 1 0v-1A2.5 2.5 0 0 0 14.5 3h-1a.5.5 0 0 0 0 1h1zm0 12a1.5 1.5 0 0 0 1.5-1.5v-1a.5.5 0 0 1 1 0v1a2.5 2.5 0 0 1-2.5 2.5h-1a.5.5 0 0 1 0-1h1zm-9 0A1.5 1.5 0 0 1 4 14.5v-1.25a.5.5 0 0 0-1 0v1.25A2.5 2.5 0 0 0 5.5 17h1.25a.5.5 0 0 0 0-1H5.5z" fill="currentColor"></path></g></svg>',TextBulletListLtr16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M2.25 5a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm0 4.25a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm1.25 3a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0zM5.75 3a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5zM5 8a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5A.75.75 0 0 1 5 8zm.75 3.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5z" fill="currentColor"></path></g></svg>',TextNumberListLtr16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3.684 1.01c.193.045.33.21.33.402v3.294a.42.42 0 0 1-.428.412a.42.42 0 0 1-.428-.412V2.58a3.11 3.11 0 0 1-.664.435a.436.436 0 0 1-.574-.184a.405.405 0 0 1 .192-.552c.353-.17.629-.432.82-.661a2.884 2.884 0 0 0 .27-.388a.44.44 0 0 1 .482-.22zm-1.53 6.046a.401.401 0 0 1 0-.582l.002-.001V6.47l.004-.002l.008-.008a1.12 1.12 0 0 1 .103-.084a2.2 2.2 0 0 1 1.313-.435h.007c.32.004.668.084.947.283c.295.21.485.536.485.951c0 .452-.207.767-.488.992c-.214.173-.49.303-.714.409c-.036.016-.07.033-.103.049c-.267.128-.468.24-.61.39a.763.763 0 0 0-.147.22h1.635a.42.42 0 0 1 .427.411a.42.42 0 0 1-.428.412H2.457a.42.42 0 0 1-.428-.412c0-.51.17-.893.446-1.184c.259-.275.592-.445.86-.574c.043-.02.085-.04.124-.06c.231-.11.4-.19.529-.293c.12-.097.18-.193.18-.36c0-.148-.057-.23-.14-.289a.816.816 0 0 0-.448-.122a1.32 1.32 0 0 0-.818.289l-.005.005a.44.44 0 0 1-.602-.003zm.94 5.885a.42.42 0 0 1 .427-.412c.294 0 .456-.08.537-.15a.303.303 0 0 0 .11-.246c-.006-.16-.158-.427-.647-.427c-.352 0-.535.084-.618.137a.349.349 0 0 0-.076.062l-.003.004a.435.435 0 0 0 .01-.018v.001l-.002.002l-.002.004l-.003.006l-.005.008l.002-.003a.436.436 0 0 1-.563.165a.405.405 0 0 1-.191-.552v-.002l.002-.003l.003-.006l.008-.013a.71.71 0 0 1 .087-.12c.058-.067.142-.146.259-.22c.238-.153.59-.276 1.092-.276c.88 0 1.477.556 1.502 1.22c.012.303-.1.606-.339.84c.238.232.351.535.34.838c-.026.664-.622 1.22-1.503 1.22c-.502 0-.854-.122-1.092-.275a1.19 1.19 0 0 1-.326-.308a.71.71 0 0 1-.02-.033l-.008-.013l-.003-.005l-.001-.003v-.001l-.001-.001a.405.405 0 0 1 .19-.553a.436.436 0 0 1 .564.165l.003.004c.01.01.033.035.076.063c.083.053.266.137.618.137c.489 0 .641-.268.648-.428a.303.303 0 0 0-.11-.245c-.082-.072-.244-.151-.538-.151a.42.42 0 0 1-.427-.412zM7.75 3a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5z" fill="currentColor"></path></g></svg>',TaskListLtr24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6.707 3.293a1 1 0 0 0-1.414 0L4 4.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm14.296 13.7H10L9.883 17A1 1 0 0 0 10 18.993h11.003l.117-.006a1 1 0 0 0-.117-1.994zm0-5.993H10l-.117.007A1 1 0 0 0 10 13h11.003l.117-.007A1 1 0 0 0 21.003 11zm0-6H10l-.117.007A1 1 0 0 0 10 7h11.003l.117-.007A1 1 0 0 0 21.003 5zM6.707 16.293a1 1 0 0 0-1.414 0L4 17.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm-1.414-6.5a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 1 1 1.414-1.414l.293.293l1.293-1.293z" fill="currentColor"></path></g></svg>',TextAlignLeft24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignCenter24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M4 6a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1zm2 12a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1zm-3-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignRight24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M5 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1zm4 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H10a1 1 0 0 1-1-1zm-6-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',TextAlignJustify24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',Search24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.5a7.5 7.5 0 0 1 5.964 12.048l4.743 4.745a1 1 0 0 1-1.32 1.497l-.094-.083l-4.745-4.743A7.5 7.5 0 1 1 10 2.5zm0 2a5.5 5.5 0 1 0 0 11a5.5 5.5 0 0 0 0-11z" fill="currentColor"></path></g></svg>',Add24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.883 3.007L12 3a1 1 0 0 1 .993.883L13 4v7h7a1 1 0 0 1 .993.883L21 12a1 1 0 0 1-.883.993L20 13h-7v7a1 1 0 0 1-.883.993L12 21a1 1 0 0 1-.993-.883L11 20v-7H4a1 1 0 0 1-.993-.883L3 12a1 1 0 0 1 .883-.993L4 11h7V4a1 1 0 0 1 .883-.993L12 3l-.117.007z" fill="currentColor"></path></g></svg>',DocumentEdit16Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M8 1v3.5A1.5 1.5 0 0 0 9.498 6h3.5v1.035a2.548 2.548 0 0 0-1.37.712l-4.287 4.287a3.777 3.777 0 0 0-.994 1.755l-.302 1.209H4.5a1.5 1.5 0 0 1-1.5-1.5V2.5A1.5 1.5 0 0 1 4.5 1H8zm4.998 7.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.73 1.29l-.303 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6zm-4-6.81V4.5a.5.5 0 0 0 .5.5h3.25l-3.75-3.75z" fill="currentColor"></path></g></svg>',Star48Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832z" fill="currentColor"></path></svg>',CommentNote20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v4.5a2.5 2.5 0 0 0 2.5 2.5h6c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',ArrowRight20Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.265 3.205a.75.75 0 0 0-1.03 1.09l5.239 4.955H2.75a.75.75 0 0 0 0 1.5h12.726l-5.241 4.957a.75.75 0 1 0 1.03 1.09l6.418-6.07a.995.995 0 0 0 .3-.566a.753.753 0 0 0-.002-.329a.995.995 0 0 0-.298-.557l-6.418-6.07z" fill="currentColor"></path></g></svg>',LinkOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0c-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0c59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z" fill="currentColor"></path></svg>',RollbackOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M793 242H366v-74c0-6.7-7.7-10.4-12.9-6.3l-142 112a8 8 0 0 0 0 12.6l142 112c5.2 4.1 12.9.4 12.9-6.3v-74h415v470H175c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h618c35.3 0 64-28.7 64-64V306c0-35.3-28.7-64-64-64z" fill="currentColor"></path></svg>',LikeOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7c0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 0 0-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 0 0 471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.5-5.4c47.6-20.3 78.3-66.8 78.3-118.4c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c-.2-12.6-2-25.1-5.6-37.1zM184 852V568h81v284h-81zm636.4-353l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 22.4-13.2 42.6-33.6 51.8H329V564.8l99.5-360.5a44.1 44.1 0 0 1 42.2-32.3c7.6 0 15.1 2.2 21.1 6.7c9.9 7.4 15.2 18.6 14.6 30.5l-9.6 198.4h314.4C829 418.5 840 436.9 840 456c0 16.5-7.2 32.1-19.6 43z" fill="currentColor"></path></svg>',DislikeOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 490.3c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-51.6-30.7-98.1-78.3-118.4a66.1 66.1 0 0 0-26.5-5.4H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h129.3l85.8 310.8C372.9 889 418.9 924 470.9 924c29.7 0 57.4-11.8 77.9-33.4c20.5-21.5 31-49.7 29.5-79.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3c40.4-23.5 65.5-66.1 65.5-111c0-28.3-9.3-55.5-26.1-77.7zM184 456V172h81v284h-81zm627.2 160.4H496.8l9.6 198.4c.6 11.9-4.7 23.1-14.6 30.5c-6.1 4.5-13.6 6.8-21.1 6.7a44.28 44.28 0 0 1-42.2-32.3L329 459.2V172h415.4a56.85 56.85 0 0 1 33.6 51.8c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-14 25.5l21.9 19a56.76 56.76 0 0 1 19.6 43c0 19.1-11 37.5-28.8 48.4z" fill="currentColor"></path></svg>',CommentOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path><path d="M894 345c-48.1-66-115.3-110.1-189-130v.1c-17.1-19-36.4-36.5-58-52.1c-163.7-119-393.5-82.7-513 81c-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4c5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6c17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5l-99 31l-1-104l-8-9c-84.6-103.2-90.2-251.9-11-361c96.4-132.2 281.2-161.4 413-66c132.2 96.1 161.5 280.6 66 412c-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10l1 104l-98-33l-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1C613.7 788.2 680.7 742.2 729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62c72.6 99.6 68.5 235.2-8 330z" fill="currentColor"></path><path d="M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path></svg>',IosCode:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g fill="currentColor"><path d="M332,142.7c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-13.8,13.2c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l95.8,91.5l-95.8,91.5c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l13.8,13.2c1.2,1.1,2.6,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l114.2-109c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L332,142.7z"></path><path d="M204,160.2c0-1.6-0.7-3.2-1.9-4.3l-13.8-13.2c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-114.2,109c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l114.2,109c1.2,1.1,2.7,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l13.8-13.2c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L106.3,256l95.8-91.5C203.3,163.4,204,161.8,204,160.2z"></path></g></svg>',IosNotificationsOutline:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g fill="currentColor"><path d="M289.7,403c-6.1,0-11.4,4.2-12.7,10.2c-1,4.5-2.7,8.2-5,10.9c-1.3,1.5-5.1,5.9-16.1,5.9c-11,0-14.8-4.5-16.1-5.9c-2.3-2.7-4-6.4-5-10.9c-1.3-6-6.6-10.2-12.7-10.2h0c-8.4,0-14.5,7.8-12.7,15.9c5,22.3,21,37.1,46.5,37.1s41.5-14.7,46.5-37.1C304.2,410.8,298,403,289.7,403L289.7,403z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9c0-13.3-10.8-24.6-24-24.6c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-13.2,0-24,11.3-24,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-38.5,9.1-79.6,46.5-79.6,139.8c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8h140.4h139.7C412.5,384,421.8,365.2,412,352.2z M373,358H139.8c-3.8,0-5.8-4.4-3.3-7.3c7-8,14.7-18.5,21-33.4c9.6-22.6,14.3-51.5,14.3-88.2c0-37.3,7-66.5,20.9-86.8c12.4-18.2,27.9-25.1,38.7-27.6c8.4-2,14.4-5.8,18.6-10.5c3.2-3.6,8.7-3.8,11.9-0.2c5.1,5.7,12,9.1,18.8,10.7c10.8,2.5,26.3,9.4,38.7,27.6c13.9,20.3,20.9,49.5,20.9,86.8c0,36.7,4.7,65.6,14.3,88.2c6.5,15.2,14.4,25.9,21.5,33.9C378.3,353.9,376.5,358,373,358z"></path></g></svg>',IosNotificationsOff:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g fill="currentColor"><path d="M255.9,456c31.1,0,48.1-22,48.1-53h-96.3C207.7,434,224.7,456,255.9,456z"></path><path d="M154.5,55c-2.5-4.3-7-6.8-11.6-7c0.1,0,0.2,0,0.3,0c-0.3,0-0.6,0-0.9,0c-0.1,0-0.2,0-0.3,0c-2.3,0-4.7,0.7-6.9,1.9c-6.8,3.9-9.1,12.6-5.1,19.3L357.5,457c2.6,4.5,7.4,7,12.3,7c2.4,0,4.9-0.6,7.2-1.9c6.8-3.9,9.1-12.6,5.1-19.3L154.5,55z"></path><path d="M296.1,384L159,150.5c-8.2,20.2-13.3,46-13.3,78.6c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8l140.4,0H296.1z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9c0-13.4-11-24.7-24.4-24.6c-13.4-0.2-24.4,11.2-24.4,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-8.7,2-17.5,5.5-25.9,10.8L366.1,384h29.9C412.5,384,421.9,365.2,412,352.2z"></path></g></svg>',IosClose:'<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><path fill="currentColor" d="M278.6,256l68.2-68.2c6.2-6.2,6.2-16.4,0-22.6c-6.2-6.2-16.4-6.2-22.6,0L256,233.4l-68.2-68.2c-6.2-6.2-16.4-6.2-22.6,0c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3l68.2,68.2l-68.2,68.2c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3c6.2,6.2,16.4,6.2,22.6,0l68.2-68.2l68.2,68.2c6.2,6.2,16.4,6.2,22.6,0c6.2-6.2,6.2-16.4,0-22.6L278.6,256z"></path></svg>',LockOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',UnlockOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v68c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-68c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zm-40 376H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',TextColor24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M17.75 14.5A2.25 2.25 0 0 1 20 16.75v3A2.25 2.25 0 0 1 17.75 22H5.25A2.25 2.25 0 0 1 3 19.75v-3a2.25 2.25 0 0 1 2.25-2.25h12.5zm0 1.5H5.25a.75.75 0 0 0-.75.75v3c0 .415.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-3a.75.75 0 0 0-.75-.75zM7.053 11.97l3.753-9.496c.236-.595 1.043-.63 1.345-.104l.05.105l3.747 9.5a.75.75 0 0 1-1.352.643l-.044-.092L13.556 10H9.443l-.996 2.52a.75.75 0 0 1-.876.454l-.097-.031a.75.75 0 0 1-.453-.876l.032-.098l3.753-9.495l-3.753 9.495zm4.45-7.178L10.036 8.5h2.928l-1.461-3.708z" fill="currentColor"></path></g></svg>',Color24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M3.839 5.858c2.94-3.916 9.03-5.055 13.364-2.36c4.28 2.66 5.854 7.777 4.1 12.577c-1.655 4.533-6.016 6.328-9.159 4.048c-1.177-.854-1.634-1.925-1.854-3.664l-.106-.987l-.045-.398c-.123-.934-.311-1.352-.705-1.572c-.535-.298-.892-.305-1.595-.033l-.351.146l-.179.078c-1.014.44-1.688.595-2.541.416l-.2-.047l-.164-.047c-2.789-.864-3.202-4.647-.565-8.157zm.984 6.716l.123.037l.134.03c.439.087.814.015 1.437-.242l.602-.257c1.202-.493 1.985-.54 3.046.05c.917.512 1.275 1.298 1.457 2.66l.053.459l.055.532l.047.422c.172 1.361.485 2.09 1.248 2.644c2.275 1.65 5.534.309 6.87-3.349c1.516-4.152.174-8.514-3.484-10.789c-3.675-2.284-8.899-1.306-11.373 1.987c-2.075 2.763-1.82 5.28-.215 5.816zm11.225-1.994a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm.494 3.488a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zM14.07 7.577a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zm-.028 8.998a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm-3.497-9.97a1.25 1.25 0 1 1 2.415-.646a1.25 1.25 0 0 1-2.415.646z" fill="currentColor"></path></g></svg>',FormatPainterOutlined:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M840 192h-56v-72c0-13.3-10.7-24-24-24H168c-13.3 0-24 10.7-24 24v272c0 13.3 10.7 24 24 24h592c13.3 0 24-10.7 24-24V256h32v200H465c-22.1 0-40 17.9-40 40v136h-44c-4.4 0-8 3.6-8 8v228c0 .6.1 1.3.2 1.9c-.1 2-.2 4.1-.2 6.1c0 46.4 37.6 84 84 84s84-37.6 84-84c0-2.1-.1-4.1-.2-6.1c.1-.6.2-1.2.2-1.9V640c0-4.4-3.6-8-8-8h-44V520h351c22.1 0 40-17.9 40-40V232c0-22.1-17.9-40-40-40zM720 352H208V160h512v192zM477 876c0 11-9 20-20 20s-20-9-20-20V696h40v180z" fill="currentColor"></path></svg>',Blockquote:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 15h15"></path><path d="M21 19H6"></path><path d="M15 11h6"></path><path d="M21 7h-6"></path><path d="M9 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path><path d="M3 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path></g></svg>',UserMultiple:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 30h-2v-5a5.006 5.006 0 0 0-5-5v-2a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M22 30h-2v-5a5.006 5.006 0 0 0-5-5H9a5.006 5.006 0 0 0-5 5v5H2v-5a7.008 7.008 0 0 1 7-7h6a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M20 2v2a5 5 0 0 1 0 10v2a7 7 0 0 0 0-14z" fill="currentColor"></path><path d="M12 4a5 5 0 1 1-5 5a5 5 0 0 1 5-5m0-2a7 7 0 1 0 7 7a7 7 0 0 0-7-7z" fill="currentColor"></path></svg>',Sun24Filled:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><circle cx="12" cy="12" r="5" fill="#FFD700" /><path fill="none" d="M12,3V5M12,19V21M21,12H19M5,12H3M18.364,5.636L16.95,7.05M7.05,16.95L5.636,18.364M18.364,18.364L16.95,16.95M7.05,7.05L5.636,5.636" stroke="#FFD700" stroke-width="2" stroke-linecap="round" /></svg>',Moon24Filled:'<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M283.211 512c78.962 0 151.079-35.925 198.857-94.792 7.068-8.708-.639-21.43-11.562-19.35-124.203 23.654-238.262-71.576-238.262-196.954 0-72.222 38.662-138.635 101.498-174.394 9.686-5.512 7.25-20.197-3.756-22.23A258.156 258.156 0 0 0 283.211 0c-141.309 0-256 114.511-256 256 0 141.309 114.511 256 256 256z" /></svg>',Search24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.75a7.25 7.25 0 0 1 5.63 11.819l4.9 4.9a.75.75 0 0 1-.976 1.134l-.084-.073l-4.901-4.9A7.25 7.25 0 1 1 10 2.75zm0 1.5a5.75 5.75 0 1 0 0 11.5a5.75 5.75 0 0 0 0-11.5z" fill="currentColor"></path></g></svg>',Add24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.75 3a.75.75 0 0 1 .743.648l.007.102l.001 7.25h7.253a.75.75 0 0 1 .102 1.493l-.102.007h-7.253l.002 7.25a.75.75 0 0 1-1.493.101l-.007-.102l-.002-7.249H3.752a.75.75 0 0 1-.102-1.493L3.752 11h7.25L11 3.75a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',ArrowReplyDown24Filled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M9.704 8.706A1 1 0 1 0 8.29 7.292l-4.997 5.004a1 1 0 0 0 0 1.413l4.997 4.998a1 1 0 1 0 1.415-1.414L6.41 14H13a8 8 0 0 0 7.996-7.75L21 6a1 1 0 1 0-2 0a6 6 0 0 1-5.775 5.996L13 12H6.414l3.29-3.294z" fill="currentColor"></path></g></svg>',SendAltFilled:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M27.71 4.29a1 1 0 0 0-1.05-.23l-22 8a1 1 0 0 0 0 1.87l8.59 3.43L19.59 11L21 12.41l-6.37 6.37l3.44 8.59A1 1 0 0 0 19 28a1 1 0 0 0 .92-.66l8-22a1 1 0 0 0-.21-1.05z" fill="currentColor"></path></svg>',DocumentDownload:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 25l-1.414-1.414L26 26.172V18h-2v8.172l-2.586-2.586L20 25l5 5l5-5z" fill="currentColor"></path><path d="M18 28H8V4h8v6a2.006 2.006 0 0 0 2 2h6v3h2v-5a.91.91 0 0 0-.3-.7l-7-7A.909.909 0 0 0 18 2H8a2.006 2.006 0 0 0-2 2v24a2.006 2.006 0 0 0 2 2h10zm0-23.6l5.6 5.6H18z" fill="currentColor"></path></svg>',FileUpload:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M14 3v4a1 1 0 0 0 1 1h4"></path><path d="M17 21H7a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h7l5 5v11a2 2 0 0 1-2 2z"></path><path d="M12 11v6"></path><path d="M9 14l3-3l3 3"></path></g></svg>',DocumentEdit16Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M5 1a2 2 0 0 0-2 2v9.998a2 2 0 0 0 2 2h1.046l.25-1H5a1 1 0 0 1-1-1V3a1 1 0 0 1 1-1h3v2.5A1.5 1.5 0 0 0 9.498 6h2.5v1.44c.306-.209.647-.344 1-.405V5.413a1.5 1.5 0 0 0-.44-1.06L9.645 1.439A1.5 1.5 0 0 0 8.585 1H5zm6.791 4H9.5a.5.5 0 0 1-.5-.5V2.206l2.792 2.792zm1.207 3.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.722 1.257l-.009.033l-.302 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6z" fill="currentColor"></path></g></svg>',Star48Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832zM24 7.283l-4.82 9.764a2.45 2.45 0 0 1-1.844 1.34L6.56 19.954l7.798 7.601a2.45 2.45 0 0 1 .704 2.169l-1.84 10.732l9.638-5.067a2.45 2.45 0 0 1 2.28 0l9.638 5.067l-1.84-10.732a2.45 2.45 0 0 1 .704-2.169l7.798-7.6l-10.776-1.566a2.45 2.45 0 0 1-1.845-1.34L24 7.282z" fill="currentColor"></path></svg>',CommentNote20Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v1H4.6C3.704 4 3 4.713 3 5.566v6.71c0 .853.704 1.566 1.6 1.566h1.6V17h.003l.002-.001l4.276-3.157H15.4c.896 0 1.6-.713 1.6-1.566V10h.5c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',VideoClip24Regular:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M9.5 9.385v5.231c0 .57.61.931 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.659zM5.25 3A3.25 3.25 0 0 0 2 6.25v11.5A3.25 3.25 0 0 0 5.25 21h13.5A3.25 3.25 0 0 0 22 17.75V6.25A3.25 3.25 0 0 0 18.75 3H5.25zM3.5 6.25c0-.966.784-1.75 1.75-1.75h13.5c.966 0 1.75.784 1.75 1.75v11.5a1.75 1.75 0 0 1-1.75 1.75H5.25a1.75 1.75 0 0 1-1.75-1.75V6.25z" fill="currentColor"></path></g></svg>',Italic:'<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M11 5h6"></path><path d="M7 19h6"></path><path d="M14 5l-4 14"></path></g></svg>'};function Cl(e){return o({name:`Icon${e}`,props:{size:{type:[String,Number],default:20},color:{type:String,default:"var(--black)"}},setup:t=>()=>{const a=xl[e];if(!a)return R("span",{style:{color:"red"}},"?");const l="number"==typeof t.size?`${t.size}px`:t.size,n=function(e,t){const a=(new DOMParser).parseFromString(e,"image/svg+xml").documentElement;if("svg"!==a.tagName)return R("span",{style:{color:"red"}},"?");const l="number"==typeof t.size?`${t.size}px`:t.size;return function e(t){const a=t.tagName.toLowerCase(),n={};for(let l=0;l<t.attributes.length;l++){const e=t.attributes[l];let a=e.value;"fill"===e.name&&"none"===a&&(a="none"),n[e.name]=a}"svg"===a&&(n.width=l,n.height=l,delete n.width,delete n.height,n.width=l,n.height=l);const o=[];for(let l=0;l<t.children.length;l++)o.push(e(t.children[l]));return t.textContent&&0===t.children.length?R(a,n,t.textContent.trim()):R(a,n,o)}(a)}(a,t);return R("span",{class:"icon-wrapper",style:{display:"inline-flex",alignItems:"center",justifyContent:"center",width:l,height:l,lineHeight:1,color:t.color,transition:"color 0.3s ease"}},[n])}})}const Ll=Cl("TextHeader120Filled"),Sl=Cl("TextHeader220Filled"),Tl=Cl("TextHeader320Filled"),Ml=Cl("TextBold20Filled"),Rl=Cl("TextUnderline24Filled"),Al=Cl("TextStrikethrough20Filled"),El=Cl("Code20Filled"),_l=Cl("Image28Regular"),Il=Cl("ArrowUndo16Filled"),zl=Cl("ArrowRedo16Filled"),Bl=Cl("LineHorizontal120Filled"),Pl=Cl("FullScreenMaximize16Filled"),Dl=Cl("ResizeSmall20Filled"),$l=Cl("TextBulletListLtr16Filled"),Ul=Cl("TextNumberListLtr16Filled"),Fl=Cl("TaskListLtr24Filled"),Vl=Cl("TextAlignLeft24Filled"),Ol=Cl("TextAlignCenter24Filled"),Hl=Cl("TextAlignRight24Filled"),jl=Cl("TextAlignJustify24Filled"),Nl=Cl("DocumentEdit16Filled"),ql=Cl("ArrowRight20Filled"),Yl=Cl("LinkOutlined"),Wl=Cl("RollbackOutlined"),Jl=Cl("LikeOutlined"),Kl=Cl("DislikeOutlined"),Xl=Cl("CommentOutlined"),Gl=Cl("IosCode"),Zl=Cl("IosNotificationsOutline"),Ql=Cl("IosNotificationsOff"),en=Cl("IosClose"),tn=Cl("LockOutlined"),an=Cl("UnlockOutlined"),ln=Cl("TextColor24Regular"),nn=Cl("Color24Regular"),on=Cl("FormatPainterOutlined"),rn=Cl("Blockquote"),sn=Cl("UserMultiple"),cn=Cl("Sun24Filled"),dn=Cl("Moon24Filled"),un=Cl("Search24Regular"),mn=Cl("Add24Regular"),pn=Cl("ArrowReplyDown24Filled"),vn=Cl("SendAltFilled"),hn=Cl("DocumentDownload"),gn=Cl("FileUpload"),fn=Cl("DocumentEdit16Regular"),wn=Cl("Star48Regular"),bn=Cl("CommentNote20Regular"),yn=Cl("VideoClip24Regular"),kn=Cl("Italic"),xn=xl.TextHeader120Filled,Cn=xl.TextHeader220Filled,Ln=xl.TextHeader320Filled,Sn=xl.TextBold20Filled,Tn=xl.TextUnderline24Filled,Mn=xl.TextStrikethrough20Filled,Rn=xl.Code20Filled,An=xl.Image28Regular,En=xl.LineHorizontal120Filled,_n=xl.TextBulletListLtr16Filled,In=xl.TextNumberListLtr16Filled,zn=xl.TaskListLtr24Filled,Bn=xl.LinkOutlined,Pn=xl.Blockquote,Dn=xl.VideoClip24Regular,$n=xl.Italic,Un=(e,t)=>[{id:"heading1",name:"标题 1",icon:xn,keywords:"h1 标题 heading",shortcut:"Ctrl+Alt+1",action:e=>e.chain().focus().toggleHeading({level:1}).run()},{id:"heading2",name:"标题 2",icon:Cn,keywords:"h2 标题 heading",shortcut:"Ctrl+Alt+2",action:e=>e.chain().focus().toggleHeading({level:2}).run()},{id:"heading3",name:"标题 3",icon:Ln,keywords:"h3 标题 heading",shortcut:"Ctrl+Alt+3",action:e=>e.chain().focus().toggleHeading({level:3}).run()},"|",{id:"bold",name:"加粗",icon:Sn,keywords:"bold 加粗 粗体 b",shortcut:"Ctrl+B",action:e=>e.chain().focus().toggleBold().run()},{id:"italic",name:"斜体",icon:$n,keywords:"italic 斜体 i",shortcut:"Ctrl+I",action:e=>e.chain().focus().toggleItalic().run()},{id:"strike",name:"删除线",icon:Mn,keywords:"strike 删除线 strikethrough",action:e=>e.chain().focus().toggleStrike().run()},{id:"underline",name:"下划线",icon:Tn,keywords:"underline 下划线 u",shortcut:"Ctrl+U",action:e=>e.chain().focus().toggleUnderline().run()},{id:"code",name:"行内代码",icon:Rn,keywords:"code 代码 行内代码 inline",shortcut:"Ctrl+E",action:e=>e.chain().focus().toggleCode().run()},"|",{id:"bulletList",name:"无序列表",icon:_n,keywords:"ul 列表 list bullet",action:e=>e.chain().focus().toggleBulletList().run()},{id:"orderedList",name:"有序列表",icon:In,keywords:"ol 列表 list ordered number",action:e=>e.chain().focus().toggleOrderedList().run()},{id:"taskList",name:"任务列表",icon:zn,keywords:"todo 任务 task checklist",action:e=>e.chain().focus().toggleTaskList().run()},"|",{id:"blockquote",name:"引用",icon:Pn,keywords:"quote 引用 blockquote",action:e=>e.chain().focus().toggleBlockquote().run()},{id:"codeBlock",name:"代码块",icon:Rn,keywords:"code 代码 codeblock",shortcut:"Ctrl+Alt+C",action:e=>e.chain().focus().toggleCodeBlock().run()},"|",{id:"image",name:"图片",icon:An,keywords:"image 图片 img picture",action:e=>{}},{id:"link",name:"链接",icon:Bn,keywords:"link 链接 url",action:e=>{t&&t("插入链接",(()=>{}),!1)}},{id:"bilibili",name:"B站视频",icon:Dn,keywords:"bilibili b站 视频 video",action:e=>{t&&t("插入bilibili视频链接",(()=>{}),!0)}},{id:"horizontalRule",name:"分割线",icon:En,keywords:"hr 分割线 divider line",action:e=>e.chain().focus().setHorizontalRule().run()}],Fn=lt.extend({addNodeView(){return e=>{const t=document.createElement("li");t.setAttribute("data-type","taskItem"),t.dataset.checked=e.node.attrs.checked;const a=document.createElement("label");a.className="cst-task-label",a.style.display="flex",a.style.alignItems="flex-start",a.style.marginTop="0.2rem";const l=document.createElement("input");l.type="checkbox",l.checked=e.node.attrs.checked,l.style.display="none",l.className="cst-task-checkbox";const n=document.createElement("span");n.className="cst-task-checkbox-wrapper",n.style.display="inline-flex",n.style.justifyContent="center",n.style.alignItems="center",n.style.width="1rem",n.style.height="1rem",n.style.border="1px solid #ccc",n.style.borderRadius="0.25rem",n.style.marginRight="0.5rem",n.style.position="relative",n.style.cursor=e.editor.isEditable?"pointer":"default",n.style.transition=e.editor.isEditable?"background-color 0.2s ease":"none",n.style.flexShrink="0",n.style.marginTop="0.2rem",e.editor.isEditable||(n.style.cursor="not-allowed",n.setAttribute("aria-disabled","true"),n.title="只读模式下不可更改",a.style.transition="none",a.style.transform="none");const o=document.createElement("span");function i(){n.style.backgroundColor=l.checked?"var(--purple-contrast)":"white",o.style.opacity=l.checked?"1":"0",!e.editor.isEditable&&l.checked&&(n.style.backgroundColor="var(--purple-contrast-disabled, #a095c3)")}o.className="cst-task-checkmark",o.textContent="✓",o.style.color="white",o.style.fontSize="0.75rem",o.style.opacity=l.checked?"1":"0",o.style.transition=e.editor.isEditable?"opacity 0.2s ease":"none",i(),n.appendChild(o),n.addEventListener("mousedown",(a=>{if(a.preventDefault(),a.stopPropagation(),e.editor.isEditable)l.checked=!l.checked,i(),"function"==typeof e.getPos&&e.editor.commands.command((({tr:t})=>(t.setNodeMarkup(e.getPos(),void 0,{...e.node.attrs,checked:l.checked}),!0))),t.dataset.checked=l.checked.toString();else if(this.options.onReadOnlyChecked){const a=!l.checked;if(!1===this.options.onReadOnlyChecked(e.node,a))return;l.checked=a,i(),"function"==typeof e.getPos&&e.editor.commands.command((({tr:t})=>(t.setNodeMarkup(e.getPos(),void 0,{...e.node.attrs,checked:a}),!0))),t.dataset.checked=a.toString()}}));const r=document.createElement("div");return r.className="cst-task-content",r.style.flex="1",a.appendChild(l),a.appendChild(n),t.appendChild(a),t.appendChild(r),{dom:t,contentDOM:r}}}}),Vn=jt(Nt),On=[["document",nt],["paragraph",ot],["text",it],["image",hl],["dropcursor",rt],["bold",st],["italic",ct],["strike",dt],["underline",ut],["code",mt],["heading",pt],["bulletList",vt.configure({keepMarks:!0})],["orderedList",ht.configure({keepMarks:!0})],["listItem",gt],["taskList",ft.configure({itemTypeName:"taskItem"})],["taskItem",Fn.configure({nested:!0,onReadOnlyChecked:(e,t)=>!1})],["blockquote",wt],["textStyle",bt],["color",yt.configure({types:["textStyle"]})],["backgroundColor",kt.configure({multicolor:!0})],["codeBlockLowlight",Xa.configure({lowlight:Vn})],["horizontalRule",xt],["link",Ct.configure({defaultProtocol:"https"})],["history",Lt],["typography",St],["markdown",Dt.configure({transformPastedText:!0})],["focus",Tt.configure({mode:"deepest"})],["gapcursor",Mt],["mention",bl],["bilibili",Oa],["floatingMenu",Rt],["bubbleMenu",At],["align",Et.configure({types:["heading","paragraph","blockquote"],alignments:["left","center","right","justify"],defaultAlignment:"left"})],["fullscreen",Ga],["slashMenu",kl.configure({items:Un()})]];const Hn={extensionMap:new Map(On),replaceImageUrls:Ba.replaceImageUrls,toJsonString:Ba.toJsonString,toJsonObject:Ba.toJsonObject,serializeContent:Ba.serializeContent},jn={URL:"/core/articles",search:async(e,t)=>(await ka(jn.URL,e,{signal:t})).data,title:async e=>(await ka(jn.URL+"/"+e+"/title")).data,detail:async e=>(await ka(jn.URL+"/"+e)).data,save:async e=>(await xa(jn.URL,e)).data,edit:async e=>(await La(jn.URL+"/"+(null==e?void 0:e.id),e)).data,togglePublishedScope:async e=>(await Ta(`${jn.URL}/${e}/published-scope`)).data,getHotTags:async(e=5)=>(await ka(`${jn.URL}/hot-tags`,{limit:e})).data,md:async(e,t)=>{try{const a=await ka(jn.URL+"/"+e+"/file");if(a&&a.data){const l=a.data.split(/\r?\n/),n=l.pop()||"";t.setContent(Hn.toJsonObject(n));const o=t.getMarkdown(),i=new Blob([l.join("\n")+"\n"+o],{type:"text/markdown"}),r=window.URL.createObjectURL(i),s=document.createElement("a");s.href=r;const c=a.headers["content-disposition"];let d=decodeURIComponent((null==c?void 0:c.split("filename=")[1])||`article-${e}`);d+=".md",document.body.appendChild(s),s.download=d,s.click(),document.body.removeChild(s),window.URL.revokeObjectURL(r)}}catch(a){throw a}},delete:async e=>(await Ma(`${jn.URL}/${e}`)).data},Nn=a("article",{state:()=>({id:""}),actions:{setId(e){this.id=e}},getters:{getId:e=>e.id}}),qn=a("comment",{state:()=>({id:""}),actions:{setId(e){this.id=e}},getters:{getId:e=>e.id}}),Yn=Qe.create({name:"characterCount",addOptions:()=>({limit:void 0}),addStorage:()=>({characters:0}),addCommands:()=>({getCharacterCount:()=>({editor:e})=>(e.storage.characterCount.characters,!0)}),addProseMirrorPlugins(){const e=this;return[new Ut({key:new Ft("characterCount"),view:()=>({update:t=>{const{doc:a}=t.state,l=(e=>{let t=0;return e.descendants((e=>{var a;e.isText?t+=(null==(a=e.text)?void 0:a.length)||0:"image"===e.type.name?t+=100:"bilibili"===e.type.name&&(t+=800)})),t})(a);e.storage.characters=l}}),props:{handleKeyDown:(t,a)=>{if(!e.options.limit)return!1;if(e.storage.characters<e.options.limit)return!1;return!(e=>{const t=e.ctrlKey&&"a"===e.key.toLowerCase()||e.ctrlKey&&"c"===e.key.toLowerCase()||e.ctrlKey&&"x"===e.key.toLowerCase()||e.ctrlKey&&"z"===e.key.toLowerCase()||e.ctrlKey&&"y"===e.key.toLowerCase()||e.shiftKey&&["ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End"].includes(e.key);return["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Home","End","Tab","Escape","Enter"].includes(e.key)||t})(a)&&(a.preventDefault(),!0)},handlePaste:(t,a)=>{if(!e.options.limit)return!1;const l=e.storage.characters;if(l>=e.options.limit)return a.preventDefault(),!0;const n=a.clipboardData;if(!n)return!1;const o=n.getData("text/plain");if(!o)return!1;return l+o.length>e.options.limit&&(a.preventDefault(),!0)},handleTextInput:(t,a,l,n)=>{if(!e.options.limit)return!1;const o=e.storage.characters;if(o>=e.options.limit)return!0;return o+n.length-(l-a)>e.options.limit}},appendTransaction:(t,a,l)=>{if(!e.options.limit)return null;if(e.storage.characters>=e.options.limit){const e=l.tr;let t=!1;if(e.steps.forEach((e=>{if(e instanceof Object&&"from"in e&&"to"in e&&"slice"in e){const a=e,l=a.to-a.from;a.slice.content.size>l&&(t=!0)}})),t)return null}return null}})]}}),Wn=Qe.create({name:"formatPainter",addOptions:()=>({enabled:!0}),addStorage:()=>({formatPainter:{isActive:!1,sourceMarks:null,sourceNode:null}}),addCommands:()=>({toggleFormatPainter:()=>({editor:e,state:t})=>{const{formatPainter:a}=e.storage,{selection:l}=t,{from:n,to:o}=l;if(a.isActive){if(a.sourceMarks){const l=t.tr;t.doc.nodesBetween(n,o,((e,t)=>{e.isText&&l.removeMark(t,t+e.nodeSize,null)})),a.sourceMarks.forEach((e=>{l.addMark(n,o,e)})),e.view.dispatch(l)}return a.isActive=!1,a.sourceNode=null,a.sourceMarks=null,!0}{const e=t.doc.nodeAt(n);return!!e&&(a.isActive=!0,a.sourceNode=e,a.sourceMarks=e.marks,!0)}}}),addProseMirrorPlugins:()=>[new Ut({key:new Ft("formatPainter"),props:{handleClick:(e,t)=>{const a=e.editor;if(!a)return!1;const{formatPainter:l}=a.storage;return!!(null==l?void 0:l.isActive)&&(a.commands.toggleFormatPainter(),!0)}}})]}),Jn=(e,t,a,l)=>((e,t,a,l)=>{const o=n(),i=_t.configure({placeholder:e.placeholder}),r=Yn.configure({limit:e.characterLimit}),c=Wn.configure({enabled:!0});return{editor:o,initEditor:()=>{var n;Hn.extensionMap.set("placeholder",i),Hn.extensionMap.set("characterCount",r),Hn.extensionMap.set("formatPainter",c),void 0!==e.useThumbnail&&Hn.extensionMap.set("image",vl(e.useThumbnail)),(a||l)&&Hn.extensionMap.set("slashMenu",kl.configure({items:Un(0,l),imageUploadTrigger:a,modalTrigger:l}));const s=[];let d=new Set(["document","paragraph","text",...e.extensions]);return e.allExtensions?(s.push(...Hn.extensionMap.values()),d=new Set([...Hn.extensionMap.keys()])):d.forEach((e=>{const t=Hn.extensionMap.get(e);t?s.push(t):ba.warning(`Unknown extension: ${e}`)})),Zt.debug("extensions: ",s),o.value=new It({extensions:s,content:e.modelValue&&Object.keys(e.modelValue||{}).length?e.modelValue:"",editable:e.editable,editorProps:e.editorProps}),null==(n=o.value)||n.on("update",(()=>{var e;const a=(null==(e=o.value)?void 0:e.getJSON())||null;Zt.debug("update content: ",a),t("update:modelValue",a)})),d},watchEditable:()=>{s((()=>e.editable),(e=>{o.value&&(o.value.setEditable(e),requestAnimationFrame((()=>{try{document.querySelectorAll(".cst-task-checkbox-wrapper").forEach((t=>{if(e){t.classList.remove("readonly-checkbox"),t.removeAttribute("aria-disabled"),t.removeAttribute("title"),t.style.cursor="pointer",t.style.transition="background-color 0.2s ease",t.style.transform="";const e=t.closest("label");e&&(e.style.transition="transform 0.15s ease",e.style.transform="",e.style.pointerEvents="")}else{t.classList.add("readonly-checkbox"),t.setAttribute("aria-disabled","true"),t.setAttribute("title","只读模式下不可更改"),t.style.cursor="not-allowed",t.style.transition="none",t.style.transform="none";const e=t.closest("label");e&&(e.style.transition="none",e.style.transform="none")}}))}catch(t){Zt.error("Error updating task checkboxes in readonly mode:",t)}})))}))},clearContent:()=>{var e;null==(e=o.value)||e.commands.clearContent()},setContent:e=>{var t;null==(t=o.value)||t.commands.setContent(e)},getMarkdown:()=>{var e,t;return null==(t=null==(e=o.value)?void 0:e.storage.markdown)?void 0:t.getMarkdown()},cleanupEditor:()=>{setTimeout((()=>{var e;null==(e=o.value)||e.destroy()}),1e3)}}})(e,t,a,l),Kn=(e,t,a,l)=>{const{selectBubbleMenu:n,setupEditorEvents:o}=((e,t,a,l)=>{const n=A({image:!1,bilibili:!1});return{selectBubbleMenu:n,setupEditorEvents:()=>{if(!e.value)return;if(e.value.on("update",(()=>{var t;(null==(t=e.value)?void 0:t.isEditable)||Promise.resolve().then((()=>{try{document.querySelectorAll(".ProseMirror-selectednode").forEach((e=>{e.classList.remove("ProseMirror-selectednode")}))}catch(e){Zt.error("Error removing selection in readonly mode:",e)}}))})),!e.value.isEditable){const t=e.value.view.dom,a=e=>{(e.ctrlKey||e.metaKey)&&(e.preventDefault(),e.stopPropagation())};t.addEventListener("mousedown",a,!0);const l=()=>{t.removeEventListener("mousedown",a,!0)};t.dataset.cleanupRegistered||(t.dataset.cleanupRegistered="true",e.value.on("destroy",l))}e.value.on("selectionUpdate",(()=>{var t;const a=null==(t=e.value)?void 0:t.state.selection,l=null==a?void 0:a.node;n.image=!1,n.bilibili=!1,(null==l?void 0:l.type)&&(n.image=!1,n.bilibili="bilibili"===l.type.name,"image"===l.type.name&&fa("tiptap-selection-update",(()=>{const e=document.querySelector(".ProseMirror-selectednode");if(e){const t=e.getBoundingClientRect();t.top>=0&&t.bottom<=window.innerHeight||e.scrollIntoView({behavior:"auto",block:"nearest"})}}),150))})),e.value.on("paste",(n=>{var o,i;if(!e.value)return;const r=(null==(o=n.slice.content.firstChild)?void 0:o.attrs)||{},s=Array.from((null==(i=n.event.clipboardData)?void 0:i.files)||[]);s.some((e=>e.type.startsWith("image/")))&&(Zt.debug("Detected image files in clipboard"),t(e.value,s,r,a,l))})),e.value.on("drop",(n=>{var o,i;if(!e.value)return;const r=(null==(o=n.slice.content.firstChild)?void 0:o.attrs)||{},s=Array.from((null==(i=n.event.dataTransfer)?void 0:i.files)||[]);s.some((e=>e.type.startsWith("image/")))&&(Zt.debug("Detected image files in drop event"),t(e.value,s,r,a,l))}));let o=null;e.value.on("transaction",(t=>{var a,l;const n=t;if(!(null==(a=e.value)?void 0:a.isEditable)||!(null==(l=n.transaction)?void 0:l.selectionSet))return;const i=e.value.state.selection.from+"-"+e.value.state.selection.to;i!==o&&(o=i,fa("tiptap-image-update",(()=>{var t,a;try{const l=null==(a=null==(t=e.value)?void 0:t.view)?void 0:a.dom;if(!l)return;l.querySelectorAll(".image-wrapper").forEach((e=>{const t=e.classList.contains("ProseMirror-selectednode");e.querySelectorAll(".resize-handle").forEach((e=>{const a=e,l=t;l!==("none"!==a.style.display)&&(l?(a.style.display="block",a.style.visibility="visible",a.style.opacity="1",a.style.pointerEvents="all"):(a.style.display="none",a.style.visibility="hidden",a.style.opacity="0",a.style.pointerEvents="none"))}))}))}catch(l){Zt.error("Error updating image handles:",l)}}),100))}))}}})(e,t,a,l);return{selectBubbleMenu:n,setupEditorEvents:o}},Xn={class:"tiptap-btn-wrapper"},Gn=Ua(o({__name:"TiptapBtn",props:{show:{type:Boolean,default:!0},trigger:{type:Function,default:()=>{}},icon:{type:Object,required:!0},size:{type:String,default:"20"},isActive:{type:Boolean,default:!1},tooltip:{type:String,default:""},disabled:{type:Boolean,default:!1}},setup:e=>(t,a)=>(m(),u(h(le),{trigger:"hover","theme-overrides":{padding:"4px 8px",textColor:"#333",color:"#fff",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.15)"}},{trigger:p((()=>[g("div",Xn,[e.show?(m(),u(h(ne),{key:0,class:b(["padding-4",{"is-active":e.isActive}]),quaternary:"",size:"small",onClick:a[0]||(a[0]=t=>e.trigger(t)),disabled:e.disabled},{default:p((()=>[(m(),u(I(e.icon),{size:e.size},null,8,["size"])),_(t.$slots,"content",{},void 0,!0)])),_:3},8,["class","disabled"])):C("",!0)])])),default:p((()=>[E(" "+w(e.tooltip),1)])),_:3}))}),[["__scopeId","data-v-bf9b98c8"]]),Zn=Ua(o({__name:"ColorPicker",props:{show:{type:Boolean,default:!0},type:{type:String,required:!0},editor:{type:Object,required:!0},colorType:{type:String,default:"color"},tooltip:{type:String,default:""}},setup(e){const t=e,a=i((()=>"color"===t.colorType?ln:nn)),l=i((()=>t.tooltip?t.tooltip:"color"===t.colorType?"文字颜色":"背景色")),o=i((()=>{if(!t.editor)return!1;try{if("color"===t.colorType){const e=t.editor.isActive("textStyle"),a=t.editor.getAttributes("textStyle");return e&&!!a.color}return t.editor.isActive("highlight")}catch(e){return!1}})),r=n({visible:!1,value:"#000000FF",type:""}),s=i({get:()=>r.value.value,set:e=>{r.value.value=e}}),d=i((()=>r.value.visible&&r.value.type===t.type)),g=()=>"",f=["#FFFFFF","#18A058","#2080F0","#F0A020","rgba(208, 48, 80, 1)"];let w=null;const b=()=>{var e,a;k();const l=null==(e=t.editor)?void 0:e.state.selection;l&&!l.empty&&(null==(a=t.editor)||a.chain().setTextSelection(l.to).run())},y=e=>{const t=e.target,a=document.querySelectorAll(".n-color-picker, .n-color-picker-panel, .n-popover");let l=!1;a.forEach((e=>{e.contains(t)&&(l=!0)})),w&&w.contains(t)||l||(r.value.visible=!1,b())},k=()=>{document.removeEventListener("click",y,!0),document.removeEventListener("mousedown",y,!0),w=null},x=e=>{var a,l;"color"===t.colorType?null==(a=t.editor)||a.chain().setColor(e).run():null==(l=t.editor)||l.chain().setHighlight({color:e}).run()};return z((()=>{k()})),(t,n)=>(m(),u(Gn,{icon:a.value,show:e.show,trigger:t=>((e,t)=>{t.stopPropagation(),w=t.currentTarget;const a=r.value.visible&&r.value.type===e;r.value.visible=!a,r.value.type=e,r.value.visible?c((()=>{setTimeout((()=>{document.addEventListener("click",y,!0),document.addEventListener("mousedown",y,!0)}),0)})):b()})(e.type,t),"is-active":o.value,tooltip:l.value},{content:p((()=>[v(h(oe),{"show-preview":!1,size:"small",placement:"top",to:"body",value:s.value,"onUpdate:value":[n[0]||(n[0]=e=>s.value=e),x],"popover-style":"min-width: 220px; z-index: 10001; pointer-events: auto;","render-label":g,swatches:f,show:d.value},null,8,["value","show"])])),_:1},8,["icon","show","trigger","is-active","tooltip"]))}}),[["__scopeId","data-v-93352d2c"]]),Qn=o({__name:"ToolbarButtonGroup",props:{buttons:{},editor:{},extensionsSet:{},showModal:{type:Function},modal:{}},emits:["image-upload","toggle-fullscreen"],setup(e,{emit:t}){const a=e,l=t,n=i((()=>a.buttons.filter((e=>a.extensionsSet.has(e.extensionName)))));return(e,t)=>(m(!0),f(L,null,S(n.value,(t=>{var n;return m(),u(Gn,{key:t.tooltip,icon:t.icon,show:e.extensionsSet.has(t.extensionName),trigger:()=>(e=>{e.emit?"image-upload"===e.emit?l("image-upload"):"toggle-fullscreen"===e.emit&&l("toggle-fullscreen"):e.trigger(a.editor,a.showModal,a.modal)})(t),"is-active":(null==(n=t.isActive)?void 0:n.call(t,e.editor))||!1,tooltip:t.tooltip},null,8,["icon","show","trigger","is-active","tooltip"])})),128))}}),eo={textFormat:[{icon:Ml,extensionName:"bold",trigger:e=>null==e?void 0:e.chain().focus().toggleBold().run(),isActive:e=>null==e?void 0:e.isActive("bold"),tooltip:"加粗"},{icon:kn,extensionName:"italic",trigger:e=>null==e?void 0:e.chain().focus().toggleItalic().run(),isActive:e=>null==e?void 0:e.isActive("italic"),tooltip:"斜体"},{icon:Al,extensionName:"strike",trigger:e=>null==e?void 0:e.chain().focus().toggleStrike().run(),isActive:e=>null==e?void 0:e.isActive("strike"),tooltip:"删除线"},{icon:Rl,extensionName:"underline",trigger:e=>null==e?void 0:e.chain().focus().toggleUnderline().run(),isActive:e=>null==e?void 0:e.isActive("underline"),tooltip:"下划线"},{icon:Gl,extensionName:"code",trigger:e=>null==e?void 0:e.chain().focus().toggleCode().run(),isActive:e=>null==e?void 0:e.isActive("code"),tooltip:"行内代码"}],heading:[{icon:Ll,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:1}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:1}),tooltip:"标题1"},{icon:Sl,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:2}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:2}),tooltip:"标题2"},{icon:Tl,extensionName:"heading",trigger:e=>null==e?void 0:e.chain().focus().toggleHeading({level:3}).run(),isActive:e=>null==e?void 0:e.isActive("heading",{level:3}),tooltip:"标题3"}],list:[{icon:$l,extensionName:"bulletList",trigger:e=>null==e?void 0:e.chain().focus().toggleBulletList().run(),isActive:e=>null==e?void 0:e.isActive("bulletList"),tooltip:"无序列表"},{icon:Ul,extensionName:"orderedList",trigger:e=>null==e?void 0:e.chain().focus().toggleOrderedList().run(),isActive:e=>null==e?void 0:e.isActive("orderedList"),tooltip:"有序列表"},{icon:Fl,extensionName:"taskList",trigger:e=>null==e?void 0:e.chain().focus().toggleTaskList().run(),isActive:e=>null==e?void 0:e.isActive("taskList"),tooltip:"任务列表"}],align:[{icon:Vl,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("left").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"left"}),tooltip:"左对齐"},{icon:Ol,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("center").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"center"}),tooltip:"居中对齐"},{icon:Hl,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("right").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"right"}),tooltip:"右对齐"},{icon:jl,extensionName:"align",trigger:e=>null==e?void 0:e.chain().focus().setTextAlign("justify").run(),isActive:e=>null==e?void 0:e.isActive({textAlign:"justify"}),tooltip:"两端对齐"}],other:[{icon:rn,extensionName:"blockquote",trigger:e=>null==e?void 0:e.chain().focus().toggleBlockquote().run(),isActive:e=>null==e?void 0:e.isActive("blockquote"),tooltip:"引用"},{icon:El,extensionName:"codeBlockLowlight",trigger:e=>null==e?void 0:e.chain().focus().toggleCodeBlock().run(),isActive:e=>null==e?void 0:e.isActive("codeBlock"),tooltip:"代码块"},{icon:Bl,extensionName:"horizontalRule",trigger:e=>null==e?void 0:e.chain().focus().setHorizontalRule().run(),tooltip:"分割线"},{icon:Yl,extensionName:"link",trigger:(e,t)=>null==t?void 0:t("插入链接",(()=>{})),isActive:e=>null==e?void 0:e.isActive("link"),tooltip:"链接"},{icon:_l,extensionName:"image",trigger:()=>{},tooltip:"图片",emit:"image-upload"},{icon:yn,extensionName:"bilibili",trigger:(e,t,a)=>null==t?void 0:t("插入bilibili视频链接",(()=>null==e?void 0:e.commands.setBilibiliVideo({src:null==a?void 0:a.inputValue})),!0),tooltip:"B站视频"},{icon:Il,extensionName:"history",trigger:e=>null==e?void 0:e.chain().focus().undo().run(),tooltip:"撤销"},{icon:zl,extensionName:"history",trigger:e=>null==e?void 0:e.chain().focus().redo().run(),tooltip:"重做"}]},to=100,ao="parent",lo=["image","bilibili","codeBlock"],no=e=>lo.includes(e),oo=(e,t,a)=>{let l=!1;return e.doc.nodesBetween(t,a,(e=>{if("codeBlock"===e.type.name)return l=!0,!1})),l},io=(e,t)=>!t.empty&&!!e.isEditable,ro=e=>{var t;const{editor:a,state:l}=e,{selection:n}=l;if(!io(a,n))return!1;const o=n.node;if((null==(t=null==o?void 0:o.type)?void 0:t.name)&&no(o.type.name))return!1;const{from:i,to:r}=n;return!oo(l,i,r)};const so={class:"editor-bubble-menu"},co=o({__name:"EditorBubbleMenu",props:{editor:{},extensionsSet:{},selectBubbleMenu:{}},emits:["show-modal"],setup(e,{emit:t}){const a={textFormat:eo.textFormat,align:eo.align,other:[eo.other[0]]},l=t,{tippyOptions:n,shouldShow:o}={tippyOptions:{duration:to,appendTo:ao},shouldShow:ro,isExcludedNodeType:no,hasCodeBlockInSelection:oo,checkBasicConditions:io},i=(e,t,a=!1)=>{l("show-modal",{title:e,trigger:t,onlyInputValue:a})};return(e,t)=>(m(),u(h(zt),{"tippy-options":h(n),editor:e.editor,"should-show":h(o)},{default:p((()=>{var t;return[g("div",so,[e.selectBubbleMenu.image?(m(),f(L,{key:0},[],64)):e.selectBubbleMenu.bilibili?(m(),f(L,{key:1},[],64)):(m(),f(L,{key:2},[v(Qn,{buttons:a.textFormat,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":i},null,8,["buttons","editor","extensions-set"]),v(Qn,{buttons:[a.other[0]],editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":i},null,8,["buttons","editor","extensions-set"]),v(Gn,{icon:h(Yl),show:e.extensionsSet.has("link"),trigger:()=>i("设置链接",(()=>{var t;null==(t=e.editor)||t.chain().focus().extendMarkRange("link").run()}),!0),"is-active":null==(t=e.editor)?void 0:t.isActive("link"),tooltip:"链接"},null,8,["icon","show","trigger","is-active"]),v(Zn,{show:e.extensionsSet.has("color"),type:"bubble-menu",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),v(Zn,{show:e.extensionsSet.has("backgroundColor"),type:"bubble-menu",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),v(Qn,{buttons:a.align,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":i},null,8,["buttons","editor","extensions-set"])],64))])]})),_:1},8,["tippy-options","editor","should-show"]))}}),uo={class:"editor-floating-menu"},mo=o({__name:"EditorFloatingMenu",props:{editor:{type:Object,required:!0},extensionsSet:{type:Object,required:!0}},emits:["image-upload","show-modal"],setup(e,{emit:t}){const a=e,l=t,n={duration:100,appendTo:"parent",placement:"right"},o=()=>{var e;null==(e=a.editor)||e.chain().focus().setLink({href:""}).run()},i=e=>{const{editor:t,state:a}=e,{selection:l}=a,{$anchor:n}=l,o="paragraph"===n.parent.type.name,i=0===n.parent.content.size,r="codeBlock"===n.parent.type.name;let s=!1;const c=n.pos,d=a.doc;if(c>0){"codeBlock"===d.resolve(c-1).parent.type.name&&(s=!0)}if(c<d.content.size){"codeBlock"===d.resolve(c+1).parent.type.name&&(s=!0)}return o&&i&&t.isEditable&&!r&&!s};return(t,a)=>(m(),u(h(Bt),{"tippy-options":n,editor:e.editor,"should-show":i},{default:p((()=>{var a,n,i,r,s,c;return[g("div",uo,[v(Gn,{icon:h(Tl),show:e.extensionsSet.has("heading"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().toggleHeading({level:3}).run()},"is-active":null==(a=e.editor)?void 0:a.isActive("heading",{level:3}),tooltip:"标题3"},null,8,["icon","show","trigger","is-active"]),v(Gn,{icon:h(Yl),show:e.extensionsSet.has("link"),trigger:()=>((e,t,a=!1)=>{l("show-modal",{title:e,trigger:t,onlyInputValue:a})})("插入链接",o),"is-active":null==(n=e.editor)?void 0:n.isActive("link"),tooltip:"链接"},null,8,["icon","show","trigger","is-active"]),v(Gn,{icon:h(_l),show:e.extensionsSet.has("image"),trigger:()=>t.$emit("image-upload"),tooltip:"图片"},null,8,["icon","show","trigger"]),v(Zn,{show:e.extensionsSet.has("color"),type:"floating-menu",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),v(Zn,{show:e.extensionsSet.has("backgroundColor"),type:"floating-menu",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),v(Gn,{icon:h(Vl),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("left").run()},"is-active":null==(i=e.editor)?void 0:i.isActive({textAlign:"left"}),tooltip:"左对齐"},null,8,["icon","show","trigger","is-active"]),v(Gn,{icon:h(Ol),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("center").run()},"is-active":null==(r=e.editor)?void 0:r.isActive({textAlign:"center"}),tooltip:"居中对齐"},null,8,["icon","show","trigger","is-active"]),v(Gn,{icon:h(Hl),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("right").run()},"is-active":null==(s=e.editor)?void 0:s.isActive({textAlign:"right"}),tooltip:"右对齐"},null,8,["icon","show","trigger","is-active"]),v(Gn,{icon:h(jl),show:e.extensionsSet.has("align"),trigger:()=>{var t;return null==(t=e.editor)?void 0:t.chain().focus().setTextAlign("justify").run()},"is-active":null==(c=e.editor)?void 0:c.isActive({textAlign:"justify"}),tooltip:"两端对齐"},null,8,["icon","show","trigger","is-active"])])]})),_:1},8,["editor"]))}}),po=()=>{const{modal:e,showModal:t}=(()=>{const e=n({visible:!1,title:"",inputTitle:"",inputValue:"",onlyInputValue:!1,trigger:()=>{}}),t=()=>{e.value.visible=!1};return{modal:e,showModal:t=>{const{title:a,trigger:l,onlyInputValue:n=!1}=t;e.value.inputTitle="",e.value.inputValue="",e.value.visible=!0,e.value.title=a,e.value.trigger=l,e.value.onlyInputValue=n},closeModal:t,handleConfirm:a=>{a?a(e.value):e.value.trigger(),t()},handleCancel:()=>{t()}}})();return{modal:e,handleShowModal:t}},vo={class:"flex-column-gap12"},ho=Ua(o({__name:"EditorModalHandler",props:{modal:{},editor:{}},emits:["update:modal"],setup(e,{emit:t}){const a=e,l=t,n=()=>{if(a.editor)if("插入链接"===a.modal.title){const e=!a.editor.state.selection.empty;a.modal.inputValue&&(e?a.editor.chain().focus().setLink({href:a.modal.inputValue}).run():a.modal.inputTitle?a.editor.chain().focus().insertContent(`<a href="${a.modal.inputValue}">${a.modal.inputTitle}</a>`).run():a.editor.chain().focus().insertContent(`<a href="${a.modal.inputValue}">${a.modal.inputValue}</a>`).run())}else"设置链接"===a.modal.title?a.modal.inputValue&&a.editor.chain().focus().extendMarkRange("link").setLink({href:a.modal.inputValue}).run():"插入bilibili视频链接"===a.modal.title?a.modal.inputValue&&a.editor.commands.setBilibiliVideo({src:a.modal.inputValue}):a.modal.trigger();else a.modal.trigger();const e={...a.modal,visible:!1};l("update:modal",e)},o=()=>{const e={...a.modal,visible:!1};l("update:modal",e)};return(e,t)=>(m(),u(h(re),{show:e.modal.visible,"onUpdate:show":t[2]||(t[2]=t=>e.modal.visible=t),title:e.modal.title,preset:"dialog","positive-text":"确认","negative-text":"取消",onPositiveClick:n,onNegativeClick:o,"auto-focus":!1},{default:p((()=>[g("div",vo,[e.modal.onlyInputValue?C("",!0):(m(),u(h(ie),{key:0,value:e.modal.inputTitle,"onUpdate:value":t[0]||(t[0]=t=>e.modal.inputTitle=t),placeholder:"请输入标题"},null,8,["value"])),v(h(ie),{value:e.modal.inputValue,"onUpdate:value":t[1]||(t[1]=t=>e.modal.inputValue=t),placeholder:"请输入链接"},null,8,["value"])])])),_:1},8,["show","title"]))}}),[["__scopeId","data-v-7bcec345"]]),go=Ua(o({__name:"LongPress",props:{duration:{type:Number,default:500}},emits:["long-press","click"],setup(e,{emit:t}){const a=e,l=t;let o=null,i=null,r=null;const s=n(!1),c=()=>{i=Date.now(),o=setTimeout((()=>{s.value=!0,l("long-press")}),a.duration)},d=()=>{if(null!==o){clearTimeout(o);Date.now()-(i??0)<a.duration&&(null!==r&&clearTimeout(r),r=setTimeout((()=>{l("click")}),200))}s.value=!1},u=()=>{null!==o&&clearTimeout(o),s.value=!1};return(e,t)=>(m(),f("div",{class:"long-press-wrapper",onMousedown:c,onMouseup:d,onMouseleave:u,onTouchstartPassive:c,onTouchendPassive:d,onTouchcancelPassive:u},[g("div",{class:b(["slot-content",{"long-press-active":s.value}])},[_(e.$slots,"default",{},void 0,!0)],2)],32))}}),[["__scopeId","data-v-be38eebc"]]),fo=o({__name:"FormatPainterBtn",props:{editor:{}},setup(e){const t=e,a=i((()=>{var e;const{selection:a}=(null==(e=t.editor)?void 0:e.state)??{};return!!a&&a.from!==a.to})),l=()=>{var e;const{selection:a}=null==(e=t.editor)?void 0:e.state,{from:l,to:n}=a;if(l!==n){const e=t.editor.state.tr;t.editor.state.doc.nodesBetween(l,n,((t,a)=>{t.isText&&e.removeMark(a,a+t.nodeSize,null)})),t.editor.view.dispatch(e),t.editor.commands.focus()}},n=()=>{var e,a;const{formatPainter:l}=null==(e=t.editor)?void 0:e.storage,{selection:n}=null==(a=t.editor)?void 0:a.state,{from:o,to:i}=n;if(l.isActive){if(l.sourceMarks&&o!==i){const e=t.editor.state.tr;t.editor.state.doc.nodesBetween(o,i,((t,a)=>{t.isText&&e.removeMark(a,a+t.nodeSize,null)})),l.sourceMarks.forEach((t=>{e.addMark(o,i,t)})),t.editor.view.dispatch(e),t.editor.commands.focus(),l.isActive=!1,l.sourceNode=null,l.sourceMarks=null}}else{const e=t.editor.state.doc.nodeAt(o);e&&(l.isActive=!0,l.sourceNode=e,l.sourceMarks=[...e.marks],t.editor.commands.focus())}};return r((()=>{t.editor&&t.editor.on("selectionUpdate",(()=>{var e,a;const{formatPainter:l}=(null==(e=t.editor)?void 0:e.storage)||{},{selection:n}=(null==(a=t.editor)?void 0:a.state)||{},{from:o,to:i}=n||{};if(o===i&&(null==l?void 0:l.isActive)){const e=t.editor.state.tr;e.setMeta("addToHistory",!1),t.editor.view.dispatch(e)}}))})),z((()=>{t.editor&&t.editor.off("selectionUpdate")})),(e,t)=>(m(),u(go,{duration:500,onLongPress:l,onClick:n},{default:p((()=>{var l,n,o,i,r,s;return[v(Gn,{"is-active":(null==(o=null==(n=null==(l=e.editor)?void 0:l.storage)?void 0:n.formatPainter)?void 0:o.isActive)??!1,icon:h(on),tooltip:(null==(s=null==(r=null==(i=e.editor)?void 0:i.storage)?void 0:r.formatPainter)?void 0:s.isActive)?"点击应用格式":"格式刷",show:!0,trigger:()=>{},disabled:!a.value,onMousedown:t[0]||(t[0]=B((()=>{}),["prevent"])),onClick:t[1]||(t[1]=B((()=>{}),["prevent"]))},null,8,["is-active","icon","tooltip","disabled"])]})),_:1}))}}),wo=o({__name:"EditorToolbar",props:{editor:{},extensionsSet:{},toolbarClass:{default:"editor-toolbar"},externalFullscreenState:{type:Boolean,default:void 0},modal:{default:()=>({inputValue:"",inputTitle:""})}},emits:["image-upload","show-modal","toggle-fullscreen"],setup(e,{emit:t}){const a=e,l=t,o=n(a.modal),i=n(!1);s((()=>a.externalFullscreenState),(e=>{void 0!==e&&(i.value=e)}));const r=(e,t,a=!1)=>{l("show-modal",{title:e,trigger:t,onlyInputValue:a})},c=()=>{i.value=!i.value,l("toggle-fullscreen",i.value)};return(e,t)=>(m(),f("div",{class:b(e.toolbarClass)},[v(Qn,{buttons:h(eo).textFormat,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:o.value,onImageUpload:t[0]||(t[0]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Qn,{buttons:h(eo).heading,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:o.value,onImageUpload:t[1]||(t[1]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Qn,{buttons:h(eo).list,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:o.value,onImageUpload:t[2]||(t[2]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Qn,{buttons:[h(eo).other[0],h(eo).other[1]],editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:o.value,onImageUpload:t[3]||(t[3]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Zn,{show:e.extensionsSet.has("color"),type:"toolbar",editor:e.editor,colorType:"color",tooltip:"文字颜色"},null,8,["show","editor"]),v(Zn,{show:e.extensionsSet.has("backgroundColor"),type:"toolbar",editor:e.editor,colorType:"backgroundColor",tooltip:"背景色"},null,8,["show","editor"]),v(Qn,{buttons:h(eo).other.slice(2),editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:o.value,onImageUpload:t[4]||(t[4]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(fo,{show:e.extensionsSet.has("formatPainter"),editor:e.editor},null,8,["show","editor"]),v(Qn,{buttons:h(eo).align,editor:e.editor,"extensions-set":e.extensionsSet,"show-modal":r,modal:o.value,onImageUpload:t[5]||(t[5]=t=>e.$emit("image-upload")),onToggleFullscreen:c},null,8,["buttons","editor","extensions-set","modal"]),v(Gn,{icon:i.value?h(Dl):h(Pl),show:e.extensionsSet.has("fullscreen"),trigger:c,"is-active":i.value,tooltip:i.value?"退出全屏":"全屏"},null,8,["icon","show","is-active","tooltip"])],2))}}),bo=["image","dropcursor","placeholder","link","textStyle","color","backgroundColor","mention","bilibili","align","history"],yo=1e4,ko={key:0,class:"character-count"},xo={key:6,class:"character-count-fullscreen"},Co=["accept"],Lo=Ua(o({__name:"TipTapEditor",props:{modelValue:{type:[Object,String],default:""},extensions:{type:Array,default:()=>[]},allExtensions:{type:Boolean,default:!1},toolbar:{type:Boolean,default:!1},placeholder:{type:String,default:"..."},editable:{type:Boolean,default:!0},fileBucket:{type:String,required:!0},bubbleMenu:{type:Boolean,default:!1},floatingMenu:{type:Boolean,default:!1},editorProps:{type:Object,default:()=>({attributes:{class:"ProseMirror"}})},toolbarClass:{type:Object,default:["editor-toolbar"]},showCharacterCount:{type:Boolean,default:!1},characterLimit:{type:Number,default:1e3},useThumbnail:{type:Boolean,default:!0}},emits:["update:modelValue","save"],setup(e,{expose:t,emit:a}){const l=e,o=a,{imageInputRef:i,handleImageChange:s,imageHandleCallback:c}=gl(),{modal:d,handleShowModal:y}=po(),{editor:k,initEditor:x,watchEditable:L,clearContent:S,setContent:T,getMarkdown:M,cleanupEditor:R}=Jn({...l,modelValue:"string"==typeof l.modelValue?{}:l.modelValue},o,(()=>{var e;return null==(e=i.value)?void 0:e.click()}),((e,t,a)=>{y({title:e,trigger:t,onlyInputValue:a})})),{isFullscreen:A,toolbarFullscreenState:E,handleToggleFullscreen:_,handleCloseFullscreen:I,cleanupFullscreen:B}=(e=>{const t=n(!1),a=n(void 0);let l=null,o=null,i=null;const r=n=>{if(t.value=n,a.value=n,t.value){if(document.body.style.overflow="hidden",window.innerWidth<=768){if(i&&window.removeEventListener("resize",i),o){const e=window.visualViewport;e&&e.removeEventListener("resize",o)}l=()=>{const e=document.querySelector(".tiptap-fullscreen"),a=null==e?void 0:e.querySelector(".editor-content");if(e&&a&&t.value){const e=window.innerHeight;a.style.maxHeight=e-128+"px"}},i=e=>{l&&l()};const e=window.visualViewport;if(e&&(o=a=>{const l=document.querySelector(".tiptap-fullscreen");if(l&&t.value){const t=e.offsetTop;l.style.transform=t?`translateY(-${t}px)`:"";const a=l.querySelector(".editor-content");if(a){const t=e.height-128;a.style.maxHeight=`${t}px`}}},e.addEventListener("resize",o)),l)try{l()}catch(r){Zt.error("Error initializing resize handler",r)}i&&window.addEventListener("resize",i)}const e=document.documentElement.classList.contains("dark-theme");setTimeout((()=>{const t=document.querySelector(".tiptap-fullscreen");if(t){e&&t.classList.add("dark-theme");const a=document.querySelector(".tiptap-editor-wrapper:not(.tiptap-fullscreen)")||document.querySelector(".article-modal-content");if(a){const l=window.getComputedStyle(a),n="rgba(0, 0, 0, 0)"!==l.backgroundColor&&"transparent"!==l.backgroundColor?l.backgroundColor:e?"var(--dark-gray)":"white";t.style.backgroundColor=n;const o=t.querySelector(".editor-content"),i=t.querySelector(".editor-content-fullscreen");o&&(o.style.backgroundColor=n),i&&(i.style.backgroundColor=n);const r=t.querySelector(".editor-toolbar");r&&(r.style.backgroundColor=n);const s=t.querySelector(".ProseMirror");s&&(s.style.backgroundColor=n)}else{const a=e?"var(--dark-gray)":"white";t.style.backgroundColor=a;const l=t.querySelector(".editor-content"),n=t.querySelector(".editor-content-fullscreen");l&&(l.style.backgroundColor=a),n&&(n.style.backgroundColor=a);const o=t.querySelector(".editor-toolbar");o&&(o.style.backgroundColor=a);const i=t.querySelector(".ProseMirror");i&&(i.style.backgroundColor=a)}}}),0)}else{document.body.style.overflow="",i&&(window.removeEventListener("resize",i),i=null);const e=window.visualViewport;o&&e&&(e.removeEventListener("resize",o),o=null),l=null;const t=document.querySelector(".tiptap-fullscreen");t&&(t.style.transform="")}requestAnimationFrame((()=>{var t;null==(t=e.value)||t.commands.focus()}))};return{isFullscreen:t,toolbarFullscreenState:a,handleToggleFullscreen:r,handleCloseFullscreen:()=>{r(!1),a.value=!1},cleanupFullscreen:()=>{document.body.style.overflow="",i&&(window.removeEventListener("resize",i),i=null);const e=window.visualViewport;o&&e&&(e.removeEventListener("resize",o),o=null),l=null}}})(k);let D,$={image:!1,bilibili:!1};r((()=>{D=x();const e=Kn(k,c,l.fileBucket,l.useThumbnail);e.selectBubbleMenu&&($=e.selectBubbleMenu),e.setupEditorEvents(),L()}));return t({setContent:T,clearContent:S,getMarkdown:M,editor:k,handleSave:()=>{o("save")}}),z((()=>{R(),B()})),(t,a)=>h(k)?(m(),f("div",{key:0,class:b(["tiptap-editor-wrapper",{"tiptap-fullscreen":h(A)}]),style:{width:"100%"}},[h(A)?(m(),f("div",{key:0,class:"fullscreen-close-button",onClick:a[0]||(a[0]=(...e)=>h(I)&&h(I)(...e))},[v(h(en),{size:24})])):C("",!0),l.toolbar?(m(),u(wo,{key:1,editor:h(k),"extensions-set":h(D),"toolbar-class":e.toolbarClass,"external-fullscreen-state":h(E),modal:h(d),onImageUpload:a[1]||(a[1]=e=>{var t;return null==(t=h(i))?void 0:t.click()}),onShowModal:h(y),onToggleFullscreen:h(_)},null,8,["editor","extensions-set","toolbar-class","external-fullscreen-state","modal","onShowModal","onToggleFullscreen"])):C("",!0),h(k)&&l.bubbleMenu?(m(),u(co,{key:2,editor:h(k),"extensions-set":h(D),"select-bubble-menu":h($),onShowModal:h(y)},null,8,["editor","extensions-set","select-bubble-menu","onShowModal"])):C("",!0),h(k)&&l.floatingMenu?(m(),u(mo,{key:3,editor:h(k),"extensions-set":h(D),onShowModal:h(y),onImageUpload:a[2]||(a[2]=e=>{var t;return null==(t=h(i))?void 0:t.click()})},null,8,["editor","extensions-set","onShowModal"])):C("",!0),h(A)?(m(),u(h(ae),{key:4,class:b(["editor-content-fullscreen",{"editor-readonly":!l.editable}])},{default:p((()=>[v(h(Pt),{editor:h(k)},null,8,["editor"])])),_:1},8,["class"])):(m(),f("div",{key:5,class:b(["editor-content",{"editor-readonly":!l.editable}]),style:{width:"100%"}},[v(h(Pt),{editor:h(k)},null,8,["editor"]),l.showCharacterCount&&h(k)?(m(),f("div",ko,w(h(k).storage.characterCount.characters)+" / "+w(l.characterLimit),1)):C("",!0)],2)),h(A)&&h(k)?(m(),f("div",xo,w(h(k).storage.characterCount.characters)+" / "+w(l.characterLimit),1)):C("",!0),v(ho,{modal:h(d),"onUpdate:modal":a[3]||(a[3]=e=>P(d)?d.value=e:null),editor:h(k)},null,8,["modal","editor"]),g("input",{type:"file",accept:h(Ra).imageTypes.join(","),ref_key:"imageInputRef",ref:i,onChange:a[4]||(a[4]=e=>h(k)&&h(s)(e,h(k),l.fileBucket,l.useThumbnail)),class:"display-none"},null,40,Co)],2)):C("",!0)}}),[["__scopeId","data-v-5e0ad95f"]]),So=o({__name:"SearchUserSelect",props:{modelValue:{},placeholder:{}},emits:["update:modelValue"],setup(e,{expose:t,emit:a}){const l=e,o=a,r=n(!1),s=n([]),c=i((()=>{const e=[...s.value];return l.modelValue.forEach((t=>{e.some((e=>e.id===t.id))||e.push(t)})),e})),d=e=>{const t=Ra.getResourceURL(e.avatar)||"";return Zt.debug("渲染用户标签: ",e),R("div",{style:{display:"flex",alignItems:"center"}},[R(se,{size:"small",round:!0,objectFit:"cover",src:t,fallbackSrc:"/avatar/avatar.png",style:{marginRight:"8px",verticalAlign:"middle"}}),R("span",e.username)])},p=i({get:()=>l.modelValue.map((e=>e.id)),set:e=>{const t=l.modelValue.filter((t=>e.includes(t.id))),a=c.value.filter((a=>e.includes(a.id)&&!t.some((e=>e.id===a.id))));o("update:modelValue",[...t,...a])}}),v=e=>{e.trim()?(r.value=!0,fl.searchUser(e).then((e=>{e.data&&(s.value=e.data)})).finally((()=>{r.value=!1}))):s.value=[]};return t({reset:()=>{s.value=[]}}),(e,t)=>(m(),u(h(te),{value:p.value,"onUpdate:value":t[0]||(t[0]=e=>p.value=e),filterable:"",clearable:"",multiple:"",remote:"","value-field":"id","label-field":"username",options:c.value,loading:r.value,placeholder:e.placeholder,"render-label":d,onSearch:v},null,8,["value","options","loading","placeholder"]))}});var To=(e=>(e[e.PUBLIC=0]="PUBLIC",e[e.PERSONAL=1]="PERSONAL",e))(To||{});const Mo={0:"公开",1:"个人"};function Ro(){const e=n(null);return{articleFileInputRef:e,handleArticleFileClick:()=>{var t;null==(t=e.value)||t.click()},handleArticleFileChange:(t,a,l)=>{var n;const o=null==(n=t.target.files)?void 0:n[0];o&&(((e,t,a)=>{const l=new FileReader;l.readAsText(e,"UTF-8"),l.onload=e=>{var l,n;const o=(null==(l=e.target)?void 0:l.result).split("\n");Zt.debug("import lines: ",o);const i=(o[0]?o[0].substring(1).trim():"")||"",r=o[1]?o[1].replace(">","").split(",").filter(Boolean).map((e=>e.trim())):[],s=null==(n=o[9])?void 0:n.split("|"),c=parseInt(s[0].replace(">","").trim())||0,d=parseInt(s[1].trim())||To.PERSONAL,u=o.slice(12).join("\n"),m=a.value.editor;if(m)try{if(m.storage.markdown){m.commands.clearContent(!1);const e=m.storage.markdown.parser.parse(u);m.commands.setContent(e||u,!0)}else m.commands.setContent(u,!0),ba.warning("Markdown 格式可能无法完全解析");t.value={...t.value,title:i,tags:r,operationLevel:c,publishedScope:d,contentObj:m.getJSON()||{}}}catch(p){Zt.error("Error parsing or setting markdown content:",p),ba.error("解析 Markdown 内容时出错"),m.commands.setContent(u,!0),t.value={...t.value,title:i,tags:r,operationLevel:c,publishedScope:d,contentObj:m.getJSON()||{}}}else ba.error("编辑器尚未准备好")},l.onerror=()=>{ba.warning("文件貌似有问题~")}})(o,a,l),e.value&&(e.value.value=""))}}}function Ao(e,t,a="内容不能为空哦~"){var l;if(!e){const e="编辑器初始化失败，请刷新后重试";return ba.warning(e),{isValid:!1,message:e}}const n="editor"in e?e.editor:e,o=n.isEmpty,i=!t.value;if(o)return ba.warning(a),{isValid:!1,message:a};if(i&&!o){const e=null==(l=n.getJSON)?void 0:l.call(n);return e&&"object"==typeof e?(t.value=e,{isValid:!0,content:e}):(ba.warning(a),{isValid:!1,message:a})}return{isValid:!0,content:t.value}}function Eo(e,t,a="正在处理中，请稍候..."){let l;if(e instanceof Map&&t)l=e.get(t)||!1;else{if(!("value"in e))return!0;l=e.value}return!l||(ba.warning(a),!1)}function _o(e,t,a){e instanceof Map&&a?e.set(a,t):"value"in e&&(e.value=t)}const Io={autoFocus:!1,closeOnEsc:!0,maskClosable:!1};function zo(e){return{...Io,...e}}const{dialog:Bo}=ee(["dialog"]),Po=e=>Bo.warning(zo(e));function Do(){const e=n(!1),t=n(!1),a=n(!1),l=n(!1);return{isArticleDialogVisible:e,isEditingArticle:t,submitLoading:a,quickSaveLoading:l,openCreateArticleDialog:a=>{t.value=!1,a(),e.value=!0},openEditArticleDialog:(a,l)=>{t.value=!0,l(a),e.value=!0},handleClose:t=>(Po(zo({title:"提示",content:"你确定关闭？",positiveText:"确定",negativeText:"不确定",onPositiveClick:()=>{t(),e.value=!1},onNegativeClick:()=>{}})),!1),setupKeyboardListener:t=>{const a=(t=>a=>{e.value&&a.ctrlKey&&"s"===a.key&&(a.preventDefault(),a.stopPropagation(),t())})(t);r((()=>{window.addEventListener("keydown",a)})),z((()=>{window.removeEventListener("keydown",a)}))}}}const $o="article",Uo={class:"flex-between-center",style:{width:"min(16rem, 100%)"}},Fo={class:"article-editor-wrapper"},Vo={class:"article-editor-container"},Oo={key:0,class:"character-count-external"},Ho=o({__name:"ArticleModal",emits:["success"],setup(e,{expose:t,emit:a}){const l=a,o=Do(),r=function(){const e=n(o()),t=n(null),a=n(null),l=n(null);function o(){return{id:"",title:"",tags:[],operationLevel:0,publishedScope:To.PERSONAL,shareUsers:[],contentObj:{}}}const r=i((()=>{var e;const t=[],a=(null==(e=aa.getLoginUser())?void 0:e.level)??0;for(let l=0;l<=a;l++)t.push({label:"Lv"+l,value:l});return t})),s=(l,n,o,i,r)=>{var s;null==(s=t.value)||s.validate().then((()=>{var t;if(Zt.debug("表单验证通过"),!Eo(o))return;const s=null==(t=a.value)?void 0:t.editor;if(!Ao(s,{value:e.value.contentObj},"文章内容不能为空哦~").isValid)return;_o(o,!0);const d=null==s?void 0:s.getJSON(),u={title:e.value.title,tag:e.value.tags.join(","),operationLevel:e.value.operationLevel,publishedScope:e.value.publishedScope,content:d?Hn.toJsonString(d):"",shareUserIds:e.value.shareUsers.map((e=>e.id))};n.value&&e.value.id&&Object.assign(u,{id:e.value.id}),(n.value&&e.value.id?jn.edit:jn.save)(u).then((t=>{(null==t?void 0:t.success)&&(!n.value&&t.data&&(n.value=!0,e.value.id=t.data.id,c((()=>{Zt.debug("Article state updated:",{isEditing:n.value,articleId:e.value.id})}))),l?(i.value=!1,ba.success(n.value?"修改成功":"创建成功")):ba.success("保存成功"),r("success"))})).catch((t=>{n.value||(n.value=!1,e.value.id=""),ba.error(t.message||"保存失败")})).finally((()=>{o.value=!1}))})).catch((e=>{Zt.debug("表单验证失败:",e)}))};return{articleForm:e,articleFormRef:t,articleTiptapEditorRef:a,shareUserSelectRef:l,generateCommentLevel:r,resetArticleForm:()=>{var t,n;e.value=o(),null==(t=l.value)||t.reset();const i=null==(n=a.value)?void 0:n.editor;i&&i.commands.clearContent(!0)},setFormData:t=>{e.value={id:t.id,title:t.title,tags:t.tags,operationLevel:t.operationLevel,publishedScope:t.publishedScope,contentObj:t.contentObj,shareUsers:t.shareUsers||[]},Zt.debug("edit article form: ",e.value)},submitArticleForm:(e,t,l,n)=>{var o;if(t.value)return;const i=null==(o=a.value)?void 0:o.editor;fa("article-submit",(()=>{var o;!(null==(o=a.value)?void 0:o.editor)&&i&&(a.value={editor:i,setContent:()=>{},clearContent:()=>{},getMarkdown:()=>{},handleSave:()=>{}}),s(!0,e,t,l,n)}),300)},quickSaveArticleForm:(e,t,a,l)=>{t.value||fa("article-quick-save",(()=>{s(!1,e,t,a,l)}),300)}}}(),s=Ro(),{articleFormRef:d,articleTiptapEditorRef:b,shareUserSelectRef:y}=r,k={title:[{required:!0,message:"请输入文章标题",trigger:"blur"},{min:1,max:100,message:"标题长度在 1 到 100 个字符之间",trigger:"blur"}]},x=e=>{var t,a;const l={value:{editor:{isEmpty:(null==(t=r.articleTiptapEditorRef.value)?void 0:t.editor.isEmpty)||!1,getJSON:()=>{var e;return(null==(e=r.articleTiptapEditorRef.value)?void 0:e.editor.getJSON())||{}},commands:{clearContent:e=>{var t;return null==(t=r.articleTiptapEditorRef.value)?void 0:t.clearContent()},setContent:(e,t)=>{var a;return null==(a=r.articleTiptapEditorRef.value)?void 0:a.setContent(e)}},storage:(null==(a=r.articleTiptapEditorRef.value)?void 0:a.editor.storage)||{}}}};s.handleArticleFileChange(e,r.articleForm,l)};o.setupKeyboardListener((()=>M()));const T=()=>(r.submitArticleForm(o.isEditingArticle,o.submitLoading,o.isArticleDialogVisible,l),!1),M=()=>{r.quickSaveArticleForm(o.isEditingArticle,o.quickSaveLoading,o.isArticleDialogVisible,l)};return t({openCreateArticleDialog:()=>o.openCreateArticleDialog(r.resetArticleForm),openEditArticleDialog:e=>o.openEditArticleDialog(e,r.setFormData)}),(e,t)=>(m(),u(h(re),{show:h(o).isArticleDialogVisible.value,"onUpdate:show":t[6]||(t[6]=e=>h(o).isArticleDialogVisible.value=e),preset:"dialog","negative-text":"算了","positive-text":"确认",onNegativeClick:t[7]||(t[7]=()=>h(o).handleClose(h(r).resetArticleForm)),onPositiveClick:T,showIcon:!1,onClose:t[8]||(t[8]=()=>h(o).handleClose(h(r).resetArticleForm)),onMaskClick:t[9]||(t[9]=()=>h(o).handleClose(h(r).resetArticleForm)),"mask-closable":!1,"auto-focus":!1,"close-on-esc":!1,onEsc:t[10]||(t[10]=()=>h(o).handleClose(h(r).resetArticleForm)),class:"article-modal","positive-button-props":{loading:h(o).submitLoading.value}},{header:p((()=>[v(h(he),{type:"primary",size:20},{default:p((()=>[E(w(h(o).isEditingArticle.value?"是得再改改":"想点什么呢"),1)])),_:1}),v(h(gn),{size:24,class:"cursor-pointer",onClick:h(s).handleArticleFileClick},null,8,["onClick"]),g("input",{type:"file",ref:"articleFile.articleFileInputRef",accept:".md",onChange:x,class:"display-none"},null,544)])),default:p((()=>[v(h(ce),{model:h(r).articleForm.value,rules:k,ref_key:"articleFormRef",ref:d,"label-placement":"left"},{default:p((()=>[v(h(de),{label:"标题",path:"title",style:{width:"min(30rem, 100%)"}},{default:p((()=>[v(h(ie),{value:h(r).articleForm.value.title,"onUpdate:value":t[0]||(t[0]=e=>h(r).articleForm.value.title=e),placeholder:"请输入文章标题"},null,8,["value"])])),_:1}),v(h(de),{label:"标签",path:"tag",style:{width:"min(30rem, 100%)"}},{default:p((()=>[v(h(ue),{value:h(r).articleForm.value.tags,"onUpdate:value":t[1]||(t[1]=e=>h(r).articleForm.value.tags=e),"input-props":{maxlength:20},max:3,type:"primary",placeholder:"请输入标签"},null,8,["value"])])),_:1}),g("div",Uo,[v(h(de),{label:"等级 | 范围",path:"allowCommentLevel",style:{width:"6rem"}},{default:p((()=>[v(h(me),{value:h(r).articleForm.value.operationLevel,"onUpdate:value":t[2]||(t[2]=e=>h(r).articleForm.value.operationLevel=e),options:h(r).generateCommentLevel.value,size:"small",trigger:"click"},{default:p((()=>[v(h(ne),{size:"small"},{default:p((()=>[E(" Lv"+w(h(r).articleForm.value.operationLevel||"0"),1)])),_:1})])),_:1},8,["value","options"])])),_:1}),v(h(de),{path:"scope"},{default:p((()=>[v(h(pe),{value:h(r).articleForm.value.publishedScope,"onUpdate:value":t[3]||(t[3]=e=>h(r).articleForm.value.publishedScope=e),size:"small","default-value":h(To).PERSONAL},{default:p((()=>[(m(!0),f(L,null,S([{value:h(To).PUBLIC,label:h(Mo)[h(To).PUBLIC]},{value:h(To).PERSONAL,label:h(Mo)[h(To).PERSONAL]}],(e=>(m(),u(h(ve),{class:"flex-between-center",key:e.value,value:e.value,label:e.label},null,8,["value","label"])))),128))])),_:1},8,["value","default-value"])])),_:1})]),h(r).articleForm.value.publishedScope===h(To).PERSONAL?(m(),u(h(de),{key:0,label:"分享给",path:"shareUsers",style:{width:"min(30rem, 100%)"}},{default:p((()=>[v(So,{modelValue:h(r).articleForm.value.shareUsers,"onUpdate:modelValue":t[4]||(t[4]=e=>h(r).articleForm.value.shareUsers=e),placeholder:"请搜索并选择用户",ref_key:"shareUserSelectRef",ref:y},null,8,["modelValue"])])),_:1})):C("",!0),v(h(de),{path:"content"},{default:p((()=>{var e;return[g("div",Fo,[g("div",Vo,[v(h(ae),{class:"article-modal-content"},{default:p((()=>[v(Lo,{ref_key:"articleTiptapEditorRef",ref:b,modelValue:h(r).articleForm.value.contentObj,"onUpdate:modelValue":t[5]||(t[5]=e=>h(r).articleForm.value.contentObj=e),"editor-props":{attributes:{class:"ProseMirrorNoneOutline"}},"bubble-menu":!0,"floating-menu":!0,"file-bucket":h($o),"all-extensions":!0,toolbar:!0,"toolbar-class":["editor-toolbar","editor-toolbar-bgc"],placeholder:"尽情发挥！","show-character-count":!1,"character-limit":h(yo),"save-loading":h(o).quickSaveLoading.value,onSave:M},null,8,["modelValue","file-bucket","character-limit","save-loading"])])),_:1})]),(null==(e=h(b))?void 0:e.editor)?(m(),f("div",Oo,w(h(b).editor.storage.characterCount.characters)+" / "+w(h(yo)),1)):C("",!0)])]})),_:1})])),_:1},8,["model"])])),_:1},8,["show","positive-button-props"]))}}),jo={class:"article-info-container"},No={class:"article-header"},qo={class:"article-header-content-wrapper",style:{"max-width":"80%"}},Yo={class:"article-header-content"},Wo={class:"article-tag-container"},Jo={class:"flex-column-center",style:{width:"80%",gap:"0.25rem"}},Ko={class:"action-buttons-container"},Xo={class:"interaction-container"},Go={class:"comment-count-container",style:{"margin-right":"0"}},Zo={class:"article-content flex-column-gap24"},Qo=o({__name:"ArticleSkeleton",props:{show:{type:Boolean,default:!1}},setup:e=>(t,a)=>D((m(),f("div",jo,[g("div",No,[g("div",qo,[g("div",Yo,[v(h(ge),{width:600,text:"",size:"large",style:{"max-width":"100%",margin:"1.25rem 0"}}),g("div",Wo,[v(h(ge),{style:{width:"60%","margin-bottom":"0.5rem"},round:"",text:"",size:"small"})])])]),g("div",Jo,[v(h(ge),{style:{width:"30%","margin-bottom":"0.25rem"},text:"",height:10,size:"small",repeat:3})]),g("div",Ko,[g("div",Xo,[v(h(ge),{width:60,round:"",style:{"max-width":"100%"},text:"",size:"small"})]),g("div",Go,[v(h(ge),{height:20,width:100,round:"",style:{"max-width":"100%"},text:"",size:"small"})])])]),g("div",Zo,[v(h(ge),{style:{width:"100%"},round:"",text:"",size:"large",repeat:8})])],512)),[[$,e.show]])}),ei={URL:"/core/notifications",load:async e=>(await ka(ei.URL,e)).data,read:async e=>(await Ta(ei.URL+"/"+e+"/read-status")).data,readAll:async()=>(await Ta(ei.URL+"/read-status")).data,unreadCount:async()=>(await ka(ei.URL+"/total-unread")).data};var ti=(e=>(e[e.CLOSE=0]="CLOSE",e[e.ALL=1]="ALL",e[e.PUBLISH=2]="PUBLISH",e[e.MODIFY=3]="MODIFY",e[e.FAVORITE=4]="FAVORITE",e[e.SHARE=5]="SHARE",e))(ti||{});const ai={0:"关闭",1:"全部",2:"发布",3:"修改",4:"收藏",5:"分享"},li=Ua(o({__name:"NotificationButton",props:{unreadCount:{type:Number,default:0},notificationReceiveType:{type:Number,required:!0}},emits:["click","long-press"],setup(e,{emit:t}){const a=n(null);return(t,l)=>(m(),f("div",{class:"notification-btn",style:{cursor:"pointer"},ref_key:"buttonRef",ref:a},[v(h(fe),{max:99,value:e.unreadCount,"show-zero":!1,show:e.notificationReceiveType!==h(ti).CLOSE},{default:p((()=>[e.notificationReceiveType!==h(ti).CLOSE?(m(),u(h(Zl),{key:0,size:24})):(m(),u(h(Ql),{key:1,size:24}))])),_:1},8,["value","show"])],512))}}),[["__scopeId","data-v-ccf792c3"]]),ni="comment";class oi{static toTimeString(e,t="YYYY-MM-DD HH:mm:ss"){const a=new Date(parseInt(e)),l={YYYY:a.getFullYear().toString(),MM:(a.getMonth()+1).toString().padStart(2,"0"),DD:a.getDate().toString().padStart(2,"0"),HH:a.getHours().toString().padStart(2,"0"),mm:a.getMinutes().toString().padStart(2,"0"),ss:a.getSeconds().toString().padStart(2,"0")};return t.replace(/YYYY|MM|DD|HH|mm|ss/g,(e=>l[e]))}static getCurrentTimestamp(){return Date.now()}static dateToTimestamp(e,t="YYYY-MM-DD HH:mm:ss"){const a={"YYYY-MM-DD HH:mm:ss":/^(\d{4})-(\d{2})-(\d{2}) (\d{2}):(\d{2}):(\d{2})$/g,"YYYY-MM-DD":/^(\d{4})-(\d{2})-(\d{2})$/g}[t];if(!a)return Zt.warn(`不支持的日期格式: ${t}`),null;const l=e.match(a);if(!l)return Zt.warn(`日期字符串 ${e} 不符合格式 ${t}`),null;const n=parseInt(l[1],10),o=parseInt(l[2],10)-1,i=parseInt(l[3],10);let r=0,s=0,c=0;"YYYY-MM-DD HH:mm:ss"===t&&(r=parseInt(l[4],10),s=parseInt(l[5],10),c=parseInt(l[6],10));return new Date(n,o,i,r,s,c).getTime()}static formatDate(e,t="YYYY-MM-DD HH:mm:ss"){const a={YYYY:e.getFullYear().toString(),MM:(e.getMonth()+1).toString().padStart(2,"0"),DD:e.getDate().toString().padStart(2,"0"),HH:e.getHours().toString().padStart(2,"0"),mm:e.getMinutes().toString().padStart(2,"0"),ss:e.getSeconds().toString().padStart(2,"0")};return t.replace(/YYYY|MM|DD|HH|mm|ss/g,(e=>a[e]))}static getRelativeTime(e){const t=Date.now()-parseInt(e);if(t<1e3)return"刚刚";if(t<6e4)return Math.floor(t/1e3)+"秒前";if(t<36e5)return Math.floor(t/6e4)+"分钟前";if(t<864e5)return Math.floor(t/36e5)+"小时前";if(t<2592e6)return Math.floor(t/864e5)+"天前";if(t<31104e6){const e=Math.floor(t/2592e6);return 6===e?"半年前":e+"个月前"}return Math.floor(t/31104e6)+"年前"}}const ii={class:"notification-list-container"},ri="评论了：",si=Ua(o({__name:"NotificationList",props:{visible:{type:Boolean}},emits:["notification-click"],setup(e,{expose:t,emit:a}){const l=e,o=a,i=n(!1),c=n([]),d=[{label:"",value:5},{label:"",value:10},{label:"",value:15}];d.forEach((e=>{e.label=`${e.value}/页`}));const u=n({page:1,pageSize:5,showSizePicker:!0,showQuickJumper:!1,pageSlot:5,pageSizes:d,size:"medium",showQuickJumpDropdown:!1,prefix:e=>R("span",`第 ${e.page} 页 `),suffix:e=>R("span",`共 ${e.itemCount} 条`),onUpdatePage:e=>{u.value.page=e,b()},onUpdatePageSize:e=>{u.value.pageSize=e,u.value.page=1,b()}}),p=[{title:"通知时间",key:"ctTm",width:162},{title:"通知内容",key:"content",ellipsis:!0,render(e){const t=e.commentId,a=e.content,l=a.indexOf(ri);return R("span",[R(fe,{style:"position: absolute;",dot:0===e.isRead,offset:[-4,0]}),R(ye,{trigger:"click",placement:"top-start",style:"max-width:min(555px,84vw); margin-left:min(-180px,40vw)",flip:!1},{trigger:()=>R("span",{class:"cursor-pointer notification-content",onClick:e=>e.stopPropagation()},t?a.substring(0,l+4)+Hn.serializeContent(y(a.substring(l+4))):e.content),default:()=>R("div",{class:"notification-popover-content"},[t?R("div",{style:"margin: 10px"},[a.substring(0,l+4),R(Lo,{fileBucket:ni,modelValue:y(a.substring(l+4)),extensions:[...bo],editable:!1})]):R("div",e.content),R(ne,{style:"margin-left:auto",class:"flex-column-end",text:!0,type:"primary",onClick:()=>{w(e)}},["让我看看",R(ql,{size:16})])])})])}}],g=e=>({}),w=e=>{o("notification-click",e)},b=()=>{i.value=!0,ei.load({pageNum:u.value.page,pageSize:u.value.pageSize}).then((e=>{var t;const a=null==e?void 0:e.data;if(a){const e=a;null==(t=e.rows)||t.forEach((e=>{e.ctTm=oi.toTimeString(e.ctTm)})),u.value.itemCount=e.totalRows||0,u.value.pageCount=e.totalPage||0,c.value=e.rows||[]}})).finally((()=>{i.value=!1}))};s((()=>l.visible),(e=>{e&&b()}),{immediate:!0}),r((()=>{l.visible&&b()})),t({loadNotificationPage:b,resetPagination:()=>{u.value.page=1,u.value.pageSize=5}});const y=e=>{if(!e)return{type:"doc",content:[{type:"paragraph",content:[]}]};try{return Hn.toJsonObject(e)}catch(t){return Zt.error("通知内容JSON解析失败:",t),{type:"doc",content:[{type:"paragraph",content:[{type:"text",text:"string"==typeof e?e:"内容无法显示"}]}]}}};return(e,t)=>(m(),f("div",ii,[v(h(we),{class:"notification-table",remote:!0,loading:i.value,data:c.value,"row-props":g,columns:p,bordered:!1},null,8,["loading","data"]),v(h(be),{class:"notification-pagination",page:u.value.page,"page-size":u.value.pageSize,"show-size-picker":u.value.showSizePicker,"show-quick-jumper":u.value.showQuickJumper,"page-slot":u.value.pageSlot,"page-sizes":u.value.pageSizes,size:u.value.size,"show-quick-jump-dropdown":u.value.showQuickJumpDropdown,prefix:u.value.prefix,suffix:u.value.suffix,itemCount:u.value.itemCount,"onUpdate:page":u.value.onUpdatePage,"onUpdate:pageSize":u.value.onUpdatePageSize},null,8,["page","page-size","show-size-picker","show-quick-jumper","page-slot","page-sizes","size","show-quick-jump-dropdown","prefix","suffix","itemCount","onUpdate:page","onUpdate:pageSize"])]))}}),[["__scopeId","data-v-9bfd203a"]]),ci=Ua(o({__name:"NotificationReceiveTypeSelector",props:{modelValue:{type:Number,required:!0}},emits:["update:modelValue"],setup(e,{emit:t}){const a=t,l=[{label:ai[ti.ALL],value:ti.ALL},{label:ai[ti.PUBLISH],value:ti.PUBLISH},{label:ai[ti.MODIFY],value:ti.MODIFY},{label:ai[ti.FAVORITE],value:ti.FAVORITE},{label:ai[ti.SHARE],value:ti.SHARE},{label:ai[ti.CLOSE],value:ti.CLOSE}],n=e=>{a("update:modelValue",e)};return(t,a)=>(m(),u(h(me),{class:"notification-popselect",value:e.modelValue,options:l,"onUpdate:value":n,trigger:"click"},{default:p((()=>[v(h(ne),{text:"",size:"small"},{default:p((()=>{var t;return[E(" 接收类型： "+w(null==(t=l.find((t=>t.value===e.modelValue)))?void 0:t.label),1)]})),_:1})])),_:1},8,["value"]))}}),[["__scopeId","data-v-6a93ea59"]]),di="/notifications",ui=class{constructor(){t(this,"stompClient",null),t(this,"socketUrl",Jt.backend.wsURL),t(this,"subscriptions",{})}connect(){const e=new qt(this.socketUrl,null,{transports:["websocket","xhr-streaming"]});this.stompClient=new Yt({webSocketFactory:()=>e,debug:e=>{}}),this.stompClient.onConnect=e=>{Zt.info("ws connected: "+e)},this.stompClient.activate()}subscribe(e,t){if(null!==this.stompClient)if(this.stompClient.connected){const a=this.stompClient.subscribe(e,(e=>{e.body&&t(e.body)}));this.subscriptions[e]=a,Zt.info(`Subscribed to: ${e}`)}else Zt.info(`WebSocket is not connected, retrying to subscribe to: ${e}`),setTimeout((()=>{this.subscribe(e,t)}),1e3);else Zt.warn("WebSocket client is not connected")}unsubscribe(e){const t=this.subscriptions[e];t?(t.unsubscribe(),delete this.subscriptions[e],Zt.info(`Unsubscribed from: ${e}`)):Zt.warn(`No subscription found for: ${e}`)}disconnect(){null!==this.stompClient&&this.stompClient.deactivate().then((()=>{Zt.info("ws disconnected")}))}};t(ui,"instance",new ui);const mi=ui.instance,pi=n(null);U((()=>{const e=sa.value===Qt.DARK,{notification:t}=ee(["notification"],{configProviderProps:{theme:e?J:null}});pi.value=t}));const vi=new Proxy({},{get(e,t){if(!pi.value){const e=sa.value===Qt.DARK,{notification:t}=ee(["notification"],{configProviderProps:{theme:e?J:null}});pi.value=t}return pi.value[t]}}),hi={class:"notification-container"},gi={style:{display:"flex","justify-content":"space-between","align-items":"center",width:"100%"}},fi="评论了：",wi=Ua(o({__name:"NotificationBtnModal",emits:["locationComment"],setup(e,{emit:t}){const a=Nn(),l=qn(),o=n(!1),s=n(0),c=n(null),d=i((()=>sa.value===Qt.DARK)),f=aa.getLoginUser(),w=n((null==f?void 0:f.notificationReceiveType)??ti.FAVORITE),b=e=>{w.value=e,fl.updateNotificationReceiveType(e).then((t=>{if(t&&void 0!==t.data){const t=aa.getLoginUser();t&&(t.notificationReceiveType=e,aa.setLoginUser(t)),ba.success("通知接收类型已更新"),e!==ti.CLOSE?S():s.value=0}}))},y=()=>{ei.readAll().then((()=>{ba.success("读完了！"),S()}))},k=()=>{o.value=!0,S()};r((()=>{mi.connect(),S(),x()})),z((()=>{C(),mi.disconnect()}));const x=()=>{(null==f?void 0:f.id)&&mi.subscribe(`/user/${f.id}${di}`,L)},C=()=>{(null==f?void 0:f.id)&&mi.unsubscribe(`/user/${f.id}${di}`)},L=e=>{const t=ta.parse(e);t&&(w.value!==ti.CLOSE&&fa("notification-received:"+t.id,(()=>{const e=t.commentId,a=t.content;let l,n=0;if(e&&a){n=a.indexOf(fi);const e=a.substring(n+4);l=Hn.toJsonObject(e)}const o=vi.create({title:e&&a?a.substring(0,n+4):"发来通知~",content:()=>e?R(Lo,{fileBucket:"comment",modelValue:l,extensions:[...bo],editable:!1}):R("div",{class:d.value?"dark-notification-content":""},t.content),duration:5e3,keepAliveOnHover:!0,closable:!0,avatar:()=>R(se,{size:"small",objectFit:"cover",round:!0,src:Ra.getResourceURL(t.publisherAvatar||""),class:d.value?"dark-notification-avatar":""}),action:()=>R(ne,{text:!0,type:"primary",class:d.value?"dark-notification-button":"",onClick:()=>{o.destroy(),T(t)}},["怎么个事？",R(ql,{size:16})])})})),S())},S=()=>{ei.unreadCount().then((e=>{void 0!==(null==e?void 0:e.data)&&(s.value=w.value===ti.CLOSE?0:Number(e.data))})),o.value&&c.value&&(c.value.resetPagination(),c.value.loadNotificationPage())},T=e=>{const t=e.articleId,n=e.commentId;if(t!==a.getId){const e=Ic.resolve({name:"Article",params:{articleId:t,commentId:n}}),a=window.open(e.href,"_blank");a&&a.focus()}else n&&(l.setId(n),M("locationComment",n));o.value=!1,e.isRead||ei.read(e.id)},M=t;return(e,t)=>(m(),u(h(Q),null,{default:p((()=>[g("div",hi,[v(go,{onLongPress:y,onClick:k},{default:p((()=>[v(li,{"unread-count":Number(s.value),"notification-receive-type":w.value},null,8,["unread-count","notification-receive-type"])])),_:1}),v(h(re),{class:"notification-modal",style:{width:"600px","max-width":"100%"},show:o.value,"onUpdate:show":t[1]||(t[1]=e=>o.value=e),title:"通知列表",preset:"dialog","auto-focus":!1},{header:p((()=>[g("div",gi,[t[2]||(t[2]=g("span",null,"通知列表",-1)),v(ci,{modelValue:w.value,"onUpdate:modelValue":[t[0]||(t[0]=e=>w.value=e),b]},null,8,["modelValue"])])])),default:p((()=>[v(si,{ref_key:"notificationListRef",ref:c,visible:o.value,onNotificationClick:T},null,8,["visible"])])),_:1},8,["show"])])])),_:1}))}}),[["__scopeId","data-v-be894cc9"]]),bi={URL:"/authentication",login:async e=>(await xa(bi.URL+"/sessions",e)).data,register:async e=>(await xa(bi.URL+"/accounts",e)).data,logout:async()=>(await Ma(bi.URL+"/sessions")).data,sendEmailCode:async e=>(await xa(bi.URL+"/email-codes",e)).data,resetPassword:async e=>(await La(bi.URL+"/accounts/password",e)).data},yi={class:"sky"},ki=Ua(o({__name:"ThemeToggle",setup(e){const t=i((()=>sa.value===Qt.DARK)),a=()=>{da()};return(e,l)=>(m(),f("div",{class:b(["theme-toggle-scene",{"is-dark":t.value}]),onClick:a},[g("div",yi,[g("div",{class:b(["sun",{"sun-set":t.value}])},[v(h(cn))],2),g("div",{class:b(["moon",{"moon-rise":t.value}])},[v(h(dn))],2)])],2))}}),[["__scopeId","data-v-1baaf921"]]),xi="homeCard",Ci="homeSearchCondition";function Li(e){return{id:e.id,username:e.username,phone:e.phone||"",email:e.email||"",avatar:e.avatar||"",ipLocation:e.ipLocation||"",job:e.job||"",level:e.level,notificationReceiveType:e.notificationReceiveType||0,config:e.config||{homeCard:!0}}}const Si={class:"avatar-container"},Ti={class:"user-info"},Mi={class:"info-row"},Ri={class:"info-row"},Ai={class:"info-row"},Ei={class:"actions-row"},_i=Ua(o({__name:"UserAvatar",setup(e){const t=n({}),a=n(null);r((()=>{fl.info().then((e=>{if(e.data){const a=Li({id:e.data.id,username:e.data.username,phone:e.data.phone,email:e.data.email,avatar:o(e.data.avatar||""),ipLocation:e.data.ipLocation,job:e.data.job,level:e.data.level,notificationReceiveType:e.data.notificationReceiveType,createdAt:e.data.ctTm});t.value=a,aa.setLoginUser(a)}}))}));const l=()=>{var e;null==(e=a.value)||e.click()},o=e=>Ra.getResourceURL(e),i=async e=>{const a=e.target;if(a.files&&a.files.length>0){const e=a.files[0];fl.changeAvatar(e).then((e=>{e.data&&(t.value.avatar=o(e.data),fl.info().then((e=>{if(e.data){const t=Li({id:e.data.id,username:e.data.username,phone:e.data.phone,email:e.data.email,avatar:o(e.data.avatar||""),ipLocation:e.data.ipLocation,job:e.data.job,level:e.data.level,notificationReceiveType:e.data.notificationReceiveType,createdAt:e.data.ctTm});aa.setLoginUser(t)}})))}))}},s=()=>{bi.logout().then((()=>{Ic.push("/login"),aa.removeLoginUser(),aa.remove(Ci),aa.remove(xi)}))};return(e,n)=>(m(),f("div",Si,[v(h(ye),{trigger:"click",placement:"bottom"},{trigger:p((()=>[v(go,{onLongPress:l},{default:p((()=>[v(h(se),{size:56,src:t.value.avatar,"object-fit":"cover",class:"cursor-pointer"},null,8,["src"])])),_:1})])),default:p((()=>[g("div",Ti,[g("div",Mi,[n[0]||(n[0]=g("strong",null,"手机号：",-1)),g("span",null,w(t.value.phone),1)]),g("div",Ri,[n[1]||(n[1]=g("strong",null,"用户名：",-1)),g("span",null,w(t.value.username),1)]),g("div",Ai,[n[2]||(n[2]=g("strong",null,"职业：",-1)),g("span",null,w(t.value.job),1)]),g("div",Ei,[v(ki),v(h(ne),{type:"error",size:"tiny",onClick:s},{default:p((()=>n[3]||(n[3]=[E("退出登录")]))),_:1})])])])),_:1}),g("input",{type:"file",ref_key:"avatarFileInputRef",ref:a,onChange:i,class:"display-none"},null,544)]))}}),[["__scopeId","data-v-0f2bd4f8"]]),Ii={class:"user-info-group"},zi={class:"online-notification-container"},Bi={class:"online-info"},Pi=Ua(o({__name:"UserInfoGroup",emits:["locationComment"],setup(e,{emit:t}){const a=t;r((()=>{i()}));const l=je();s(l,(()=>{i()}));const o=n(0),i=()=>{fl.online().then((e=>{void 0!==(null==e?void 0:e.data)&&null!==(null==e?void 0:e.data)&&(o.value=e.data||0)}))},c=e=>{a("locationComment",e)};return(e,t)=>(m(),f("div",Ii,[g("div",zi,[v(wi,{onLocationComment:c}),g("div",Bi,[E(w(o.value),1),v(h(sn),{size:20})])]),v(_i)]))}}),[["__scopeId","data-v-671fed77"]]),Di={class:"comment-title-container"},$i={class:"comment-header-top"},Ui={class:"comment-header-bottom"},Fi=Ua(o({__name:"CommentHeader",props:{breadcrumb:{},modelValue:{}},emits:["breadcrumbClick","locationComment","update:modelValue"],setup(e,{emit:t}){const a=t,l=e=>{a("update:modelValue",e)};return(e,t)=>(m(),f("div",Di,[g("div",$i,[v(h(ke),null,{default:p((()=>[(m(!0),f(L,null,S(e.breadcrumb,((e,t)=>(m(),u(h(xe),{key:t,onClick:e=>{a("breadcrumbClick",t)},class:"breadcrumb-item"},{default:p((()=>[v(h(he),{class:"breadcrumb-text cursor-pointer"},{default:p((()=>[E(w(e.publisher)+" ",1),e.id&&e.publisherAvatar?(m(),u(h(se),{key:0,"object-fit":"cover",size:22,round:"",src:h(Ra).getResourceURL(e.publisherAvatar)},null,8,["src"])):C("",!0)])),_:2},1024)])),_:2},1032,["onClick"])))),128))])),_:1}),v(Pi,{onLocationComment:t[0]||(t[0]=t=>e.$emit("locationComment"))})]),g("div",Ui,[v(h(pe),{size:"small",value:e.modelValue,"onUpdate:value":l},{default:p((()=>[v(h(ve),{value:"0"},{default:p((()=>t[1]||(t[1]=[E("热评")]))),_:1}),v(h(ve),{value:"1"},{default:p((()=>t[2]||(t[2]=[E("最新")]))),_:1}),v(h(ve),{value:"2"},{default:p((()=>t[3]||(t[3]=[E("回复")]))),_:1})])),_:1},8,["value"])])]))}}),[["__scopeId","data-v-9ae9fd75"]]),Vi={class:"comment-controls-container"},Oi={class:"comment-reply-info"},Hi={class:"comment-interaction-btn"},ji=Ua(o({__name:"CommentControls",props:{comment:{},showReplyListBtn:{type:Boolean}},emits:["showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn"],setup:(e,{emit:t})=>(e,t)=>(m(),f("div",Vi,[g("div",Oi,[D(v(h(ne),{class:"comment-reply-list-btn",style:{"margin-left":"3%"},text:"",type:"info",onClick:t[0]||(t[0]=t=>e.$emit("showReplyList",e.comment))},{default:p((()=>t[5]||(t[5]=[E(" 回复列表> ")]))),_:1},512),[[$,e.showReplyListBtn&&!e.comment.fixed]]),v(h(bn),{onClick:t[1]||(t[1]=t=>e.$emit("handleCommentReplyClick",e.comment)),class:"cursor-pointer",size:20}),E(" "+w(e.comment.replyCount),1)]),g("div",Hi,[v(h(Jl),{color:e.comment.isLike?"var(--blue)":"",class:"cursor-pointer",size:16,onClick:t[2]||(t[2]=t=>e.$emit("interactionBtn",e.comment,1))},null,8,["color"]),E(" "+w(e.comment.likeCount)+" ",1),v(h(Kl),{color:e.comment.isDislike?"var(--blue)":"",class:"cursor-pointer",size:16,onClick:t[3]||(t[3]=t=>e.$emit("interactionBtn",e.comment,0))},null,8,["color"]),E(" "+w(e.comment.dislikeCount)+" ",1),v(h(wn),{color:e.comment.isFavorite?"var(--blue)":"",class:"cursor-pointer",size:18,onClick:t[4]||(t[4]=t=>e.$emit("favoriteBtn",e.comment))},null,8,["color"]),E(" "+w(e.comment.favoriteCount),1)])]))}),[["__scopeId","data-v-d5ab7a07"]]),Ni={class:"user-info-row"},qi={class:"user-detail-col"},Yi={class:"user-nickname"},Wi={class:"user-extra-info"},Ji={class:"comment-content-row"},Ki={class:"comment-reply-row"},Xi=Ua(o({__name:"CommentListItem",props:{comment:{},flashCommentId:{},showReplyListBtn:{type:Boolean},commentInputVisible:{},quickReplyLoading:{}},emits:["showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn","quickReplyComment","updateEditor"],setup(e,{emit:t}){const a=t,l=[...bo,"characterCount"];return(e,t)=>(m(),f("div",{class:b({"user-comment-container-fixed":e.comment.fixed,"user-comment-container":!e.comment.fixed,"comment-flash":e.flashCommentId===e.comment.id})},[g("div",Ni,[v(h(se),{round:"",size:"large","object-fit":"cover",src:e.comment.publisherAvatar?h(Ra).getResourceURL(e.comment.publisherAvatar):""},null,8,["src"]),g("div",qi,[g("span",Yi,w(e.comment.publisher),1),g("span",Wi,[E(w(e.comment.publisherJob)+" | ",1),g("span",{class:"time-clickable",onClick:t[0]||(t[0]=t=>{var a;void 0===(a=e.comment).showExactTime?a.showExactTime=!0:a.showExactTime=!a.showExactTime})},w(e.comment.showExactTime?e.comment.exactPublishedAt:e.comment.publishedAt),1),E(" | "+w(e.comment.ipLocation),1)])])]),g("div",Ji,[v(Lo,{"file-bucket":h(ni),modelValue:e.comment.contentObj,"onUpdate:modelValue":t[1]||(t[1]=t=>e.comment.contentObj=t),extensions:[...h(bo)],editable:!1},null,8,["file-bucket","modelValue","extensions"])]),v(ji,{comment:e.comment,showReplyListBtn:e.showReplyListBtn,onShowReplyList:t[2]||(t[2]=t=>e.$emit("showReplyList",e.comment)),onHandleCommentReplyClick:t[3]||(t[3]=t=>e.$emit("handleCommentReplyClick",e.comment)),onInteractionBtn:t[4]||(t[4]=(t,a)=>e.$emit("interactionBtn",t,a)),onFavoriteBtn:t[5]||(t[5]=t=>e.$emit("favoriteBtn",e.comment))},null,8,["comment","showReplyListBtn"]),v(F,{name:"comment-reply",appear:""},{default:p((()=>[D(g("div",Ki,[v(Lo,{modelValue:e.comment.quickCommentReply,"onUpdate:modelValue":t[6]||(t[6]=t=>e.comment.quickCommentReply=t),class:"comment-reply-tiptap-editor",ref:t=>t&&((e,t)=>{t&&"editor"in t&&a("updateEditor",e,t.editor)})(e.comment.id,t),"editor-props":{attributes:{class:"ProseMirrorInput","data-comment-id":e.comment.id}},"file-bucket":h(ni),placeholder:"说是你的自由，但是...","show-character-count":!0,extensions:l,toolbar:!0,onKeydown:t[7]||(t[7]=V(B((t=>e.$emit("quickReplyComment",e.comment)),["alt","prevent"]),["enter"]))},null,8,["modelValue","editor-props","file-bucket"]),v(h(ne),{class:"comment-reply-send-btn",text:"",type:"info",loading:!(!e.quickReplyLoading||!e.comment.id)&&e.quickReplyLoading.get(e.comment.id),onClick:t[8]||(t[8]=t=>e.$emit("quickReplyComment",e.comment))},{default:p((()=>[v(h(pn),{size:28})])),_:1},8,["loading"])],512),[[$,e.commentInputVisible===e.comment.id]])])),_:1})],2))}}),[["__scopeId","data-v-8bcd7388"]]),Gi={class:"comment-scroll"},Zi={class:"comment-list-footer"},Qi=Ua(o({__name:"CommentList",props:{commentList:{},flashCommentId:{},showReplyListBtn:{type:Boolean},commentInputVisible:{},commentScrollTrigger:{},commentLoading:{type:Boolean},commentNoMore:{type:Boolean},hasCommentPermission:{type:Boolean},quickReplyLoading:{}},emits:["loadMoreComments","showReplyList","handleCommentReplyClick","interactionBtn","favoriteBtn","quickReplyComment","updateEditor","updateCommentRef"],setup(e,{expose:t,emit:a}){const l=n(null),o=e,i=a,r=(e,t)=>{i("interactionBtn",e,t)},s=(e,t)=>{i("updateEditor",e,t)},c=()=>{0===o.commentList.length&&o.commentNoMore||i("loadMoreComments")};return t({commentListContainerRef:l}),(e,t)=>(m(),f("div",{ref_key:"commentListContainerRef",ref:l,class:b(["comment-list-container",{"has-input-box":"-1"==e.commentInputVisible}])},[v(h(Ce),{onLoad:c,distance:50,trigger:e.commentScrollTrigger},{default:p((()=>[g("div",Gi,[v(O,{name:"smooth",appear:"",tag:"div"},{default:p((()=>[(m(!0),f(L,null,S(e.commentList,(a=>(m(),f("div",{key:a.id,ref_for:!0,ref:e=>{e&&((e,t)=>{i("updateCommentRef",e,t)})(a.id,e)}},[v(Xi,{comment:a,"flash-comment-id":e.flashCommentId,"show-reply-list-btn":e.showReplyListBtn,"comment-input-visible":e.commentInputVisible,"quick-reply-loading":e.quickReplyLoading,onShowReplyList:t[0]||(t[0]=t=>e.$emit("showReplyList",t)),onHandleCommentReplyClick:t[1]||(t[1]=t=>e.$emit("handleCommentReplyClick",t)),onInteractionBtn:r,onFavoriteBtn:t[2]||(t[2]=t=>e.$emit("favoriteBtn",t)),onQuickReplyComment:t[3]||(t[3]=t=>e.$emit("quickReplyComment",t)),onUpdateEditor:s},null,8,["comment","flash-comment-id","show-reply-list-btn","comment-input-visible","quick-reply-loading"])])))),128))])),_:1}),g("div",Zi,[e.commentLoading?(m(),u(h(Le),{key:0,class:"display-flex"})):e.commentLoading||!e.commentNoMore&&0!==e.commentList.length?C("",!0):(m(),u(h(Se),{key:1,description:e.hasCommentPermission?"没有更多评论了...":"您没有权限查看评论"},null,8,["description"]))])])])),_:1},8,["trigger"])],2))}}),[["__scopeId","data-v-abaa15ee"]]),er=Ua(o({__name:"CommentMainInput",props:{commentReply:{},sendCommentLoading:{type:Boolean},disabled:{type:Boolean}},emits:["sendComment","update:commentReply"],setup(e,{expose:t,emit:a}){const l=a,o=n(),i=n(),r=[...bo,"characterCount"];return t({commentInputWrapperRef:o,sendTiptapEditorRef:i}),(e,t)=>(m(),u(h(Te),{bottom:0,style:{"z-index":"1500"},class:"comment-input-affix"},{default:p((()=>[g("div",{class:"comment-input-row",ref_key:"commentInputWrapperRef",ref:o},[v(Lo,{ref_key:"sendTiptapEditorRef",ref:i,"model-value":e.commentReply,"onUpdate:modelValue":t[0]||(t[0]=e=>l("update:commentReply",e)),class:"comment-tiptap-editor","editor-props":{attributes:{class:"ProseMirrorInput","data-main-editor":"true"}},"file-bucket":h(ni),placeholder:"说是你的自由，但是...","show-character-count":!0,extensions:r,toolbar:!0,onKeydown:t[1]||(t[1]=V(B((t=>e.$emit("sendComment")),["alt","prevent"]),["enter"]))},null,8,["model-value","file-bucket"]),v(h(ne),{text:"",type:"info",loading:e.sendCommentLoading,onClick:t[2]||(t[2]=t=>e.$emit("sendComment")),class:"comment-reply-send-btn",size:"small",disabled:e.disabled},{default:p((()=>[v(h(vn),{size:28})])),_:1},8,["loading","disabled"])],512)])),_:1}))}}),[["__scopeId","data-v-0128642d"]]);function tr(e,t){const a=n(0),l=n(5),o=n(!0),c=()=>{if(t.value){const e=t.value.clientHeight,n=Math.ceil(e/250),o=Math.max(3,n);a.value===e&&l.value===o||(a.value=e,l.value=o,Zt.debug("评论容器高度更新:",{containerHeight:a.value,commentsPerScreen:l.value,commentHeight:250,calculatedFromHeight:n}))}},d=i((()=>{let t;return o.value||0===e.value.length?(t=Math.ceil(1.2*l.value),t=Math.max(5,t)):t=l.value<=4?Math.max(3,Math.ceil(.8*l.value)):l.value<=8?Math.max(4,Math.ceil(.6*l.value)):Math.max(6,Math.ceil(.5*l.value)),Zt.debug("计算评论加载量:",{size:t,isInitialLoad:o.value,commentsPerScreen:l.value,currentCommentsCount:e.value.length,containerHeight:a.value}),t})),u=n(null),m=()=>{u.value&&(u.value.disconnect(),u.value=null)},p=()=>{c()};return s(t,(e=>{m(),e&&(c(),t.value&&window.ResizeObserver&&(u.value=new ResizeObserver((()=>{c()})),u.value.observe(t.value)))}),{immediate:!0}),r((()=>{window.addEventListener("resize",p),c()})),z((()=>{window.removeEventListener("resize",p),m()})),{containerHeight:x(a),commentsPerScreen:x(l),isInitialLoad:x(o),calculatedLoadSize:d,updateContainerHeight:c,markInitialLoadComplete:()=>{o.value&&(Zt.debug("评论首次加载完成，切换到后续加载模式"),o.value=!1)},resetToInitialLoad:()=>{Zt.debug("重置评论加载状态为初始加载模式"),o.value=!0},COMMENT_HEIGHT:250}}const ar={URL:"/core/favorites",save:async e=>(await xa(ar.URL,e)).data,toggle:async e=>(await xa(ar.URL,e)).data},lr={URL:"/core/interactions",save:async e=>(await xa(lr.URL,e)).data,toggle:async e=>(await xa(lr.URL,e)).data};const nr={URL:"/core/comments",search:async(e,t)=>{const a={signal:t};return(await ka(nr.URL+"/search",e,a)).data},location:async e=>(await ka(nr.URL+"/"+e+"/location")).data,loadById:async e=>(await ka(nr.URL+"/"+e)).data,load:async e=>(await ka(nr.URL+e)).data,save:async e=>(await xa(nr.URL,e)).data};function or(e,t,a,l,n,o){return{clearAllQuickReplyContent:()=>(a.value="-1",({commentList:e})=>{e.forEach((e=>{e.quickCommentReply&&(e.quickCommentReply=void 0);const t=n.value.get(e.id);t&&t.commands&&t.commands.clearContent()}))}),handleCommentReplyClick:(e,{isLastBreadcrumb:t})=>{a.value!==e.id?(a.value=e.id,c((()=>{var a,l;if(t&&!e.fixed){const t=n.value.get(e.id);if(t&&(!e.quickCommentReply||!(null==(l=null==(a=e.quickCommentReply.content)?void 0:a[0])?void 0:l.content))){const a={type:"doc",content:[{type:"paragraph",content:[{type:"mention",attrs:{id:e.publisher,label:e.publisher,avatar:e.publisherAvatar||""}},{type:"text",text:" "}]}]};t.commands.setContent(a)}}}))):a.value="-1"},debouncedQuickReplyComment:(t,a)=>{fa(`comment-quick-reply-${t.id}`,(()=>{((t,{isLastBreadcrumb:a,onSuccess:l})=>{if(!Eo(e.value,t.id))return;const i=n.value.get(t.id),r={value:t.quickCommentReply},s=Ao(i,r,"啥也没有可不能发送哦~");if(!s.isValid)return;t.quickCommentReply=s.content||r.value,_o(e.value,!0,t.id);const c=Hn.toJsonString(t.quickCommentReply);nr.save({content:c,articleId:o(),parentCommentId:a&&!t.fixed?t.parentCommentId:t.id}).then((e=>{ba.success("发送成功"),(null==e?void 0:e.data)&&l&&l(e.data),i&&i.commands&&i.commands.clearContent(),t.quickCommentReply=void 0})).finally((()=>{e.value.set(t.id,!1)}))})(t,a)}),300)},debouncedSendComment:(e,a)=>{fa("comment-send",(()=>{((e,{lastBreadcrumbComment:a,onSuccess:n})=>{if(!Eo(t))return;const i=null==e?void 0:e.sendTiptapEditorRef,r=Ao(i,{value:l.value},"啥也没有可不能发送哦~");if(!r.isValid)return;l.value=r.content||l.value,_o(t,!0);const s=Hn.toJsonString(l.value),c=a,d=o();nr.save({content:s,articleId:d,parentCommentId:c.id}).then((e=>{ba.success("发送成功");const t=null==e?void 0:e.data;t&&n&&n(String(t)),null==i||i.clearContent(),l.value=void 0})).finally((()=>{t.value=!1}))})(e,a)}),300)}}}function ir(e){const{quickReplyLoading:t,sendCommentLoading:a,commentInputVisible:l,commentReply:o,quickReplyTiptapEditorMap:i,updateEditor:r}=function(){const e=n(new Map),t=n(!1),a=n("-1"),l=n(void 0),o=n(new Map);return{quickReplyLoading:e,sendCommentLoading:t,commentInputVisible:a,commentReply:l,quickReplyTiptapEditorMap:o,updateEditor:(e,t)=>{o.value.set(e,t)}}}(),{interactionBtn:s,favoriteBtn:c}={interactionBtn:(e,t)=>{const a={targetType:0,targetId:e.id,actionType:t};lr.save({...a,type:a.targetType}).then((a=>{const l=null==a?void 0:a.data;if(l){e.likeCount=l.likeCount,e.dislikeCount=l.dislikeCount;const a=1===t;l.cancel?a?(ba.info("赞取消"),e.isLike=!1):(ba.info("踩取消"),e.isDislike=!1):a?(ba.success("赞 :)"),e.isLike=!0):(ba.warning("踩 :("),e.isDislike=!0)}}))},favoriteBtn:e=>{const t={targetType:0,targetId:e.id};ar.save(t).then((t=>{const a=null==t?void 0:t.data;a&&(e.favoriteCount=a.count,a.cancel?(ba.info("取消收藏"),e.isFavorite=!1):(ba.success("已收藏"),e.isFavorite=!0))}))}},{clearAllQuickReplyContent:d,handleCommentReplyClick:u,debouncedQuickReplyComment:m,debouncedSendComment:p}=or(t,a,l,o,i,e);return{quickReplyLoading:t,sendCommentLoading:a,commentInputVisible:l,commentReply:o,quickReplyTiptapEditorMap:i,updateEditor:r,interactionBtn:s,favoriteBtn:c,clearAllQuickReplyContent:d,handleCommentReplyClick:u,debouncedQuickReplyComment:m,debouncedSendComment:p}}function rr(e,t,a,l,o){const i=n(new Map),r=n("0"),s=n(""),d=qn(),u=je(),m=Ne(),p=()=>d.getId,v=e=>{a(),l(e)},h=e=>{c((()=>{const t=i.value.get(e);t&&(t.scrollIntoView({behavior:"smooth",block:"center"}),setTimeout((()=>{t.scrollIntoView({behavior:"smooth",block:"center"}),(e=>{s.value=e,setTimeout((()=>{s.value=""}),1e3)})(e)}),10))}))};return{commentRefs:i,commentScrollTrigger:r,flashCommentId:s,updateCommentRef:(e,t)=>{i.value.set(e,t)},locationComment:(l=p())=>{e.value=!0,l&&(d.setId(l),m.push({params:{...u.params,commentId:l}}),nr.location(l).then((e=>{const a=null==e?void 0:e.data;if(a){const e=a.parents,n=a.comments;v(n),t.resetBreadcrumb();const i=e.length;if(i>0){for(let l=i-1;l>=0;l--){const a=e[l];t.addBreadcrumb(a)}const a=e[0];o(a)}h(l)}})).catch((e=>{e.response&&403===e.response.status&&a()})).finally((()=>{e.value=!1})))},scrollToComment:h,prepareScrollToComment:v,resetScrollTrigger:()=>{r.value=String(Date.now())},getCommentId:p}}function sr(e,t,a){const l=qn(),o=je(),r=function(){const e=n([{id:"",publisher:"评论列表"}]),t=i((()=>3===e.value.length)),a=i((()=>e.value.length-1)),l=i((()=>e.value[e.value.length-1])),o=n(!0);return{breadcrumb:e,isLastBreadcrumb:t,lastBreadcrumbIndex:a,lastBreadcrumbComment:l,showReplyListBtn:o,resetBreadcrumb:()=>{e.value=[{id:"",publisher:"评论列表"}]},addBreadcrumb:t=>{e.value.push(t)},setShowReplyListBtn:()=>{o.value=a.value<2}}}(),c=function(e,t,a,l){const o=n([]),i=n(!1),r=n(!1),s=n(!1),c=n("0"),d=()=>{o.value=[],i.value=!1,r.value=!1},u=e=>{e&&0!==e.length&&e.map((({...e})=>({...e,publishedAt:oi.getRelativeTime(e.publishedAt),exactPublishedAt:oi.toTimeString(e.publishedAt),fixed:!1}))).forEach((e=>{e.contentObj=Hn.toJsonObject(e.content),e.quickCommentReply=void 0,o.value.push(e)}))},m=e=>{o.value.unshift({...e,publishedAt:oi.getRelativeTime(e.publishedAt),exactPublishedAt:oi.toTimeString(e.publishedAt),fixed:!0,contentObj:Hn.toJsonObject(e.content)})},p=async(e,t=!1)=>{if(l&&l.lastBreadcrumbIndex.value>0&&(0==o.value.length||!o.value[0].fixed))if(t){const t=await nr.loadById(e.id);(null==t?void 0:t.data)&&m(t.data)}else m(e)},v=n=>{if(i.value||r.value)return;i.value=!0;const d=t?t():5;wa("article-comments",(()=>{const t=o.value.length>0?o.value[o.value.length-1].id:"";r.value?i.value=!1:Promise.allSettled([nr.load(`?articleId=${e()}&id=${t}&parentCommentId=${null==n?void 0:n.id}&loadSize=${d}&sortType=${c.value}`).catch((e=>{if(e.response&&403===e.response.status)throw s.value=!1,i.value=!1,e;throw e})),p(n,!0)]).then((e=>{var t,a;if("fulfilled"===e[0].status&&(null==(t=e[0])?void 0:t.value)){const t=(null==(a=e[0])?void 0:a.value).data;if(0==(null==t?void 0:t.length))return void(r.value=!0);t.length<d&&(r.value=!0),u(t),l&&l.setShowReplyListBtn()}else r.value=!0})).catch((e=>{r.value=!1})).finally((()=>{i.value=!1,a&&a()}))}),300)};return{commentList:o,commentLoading:i,commentNoMore:r,hasCommentPermission:s,sortType:c,resetCommentList:d,loadCurrentCommentList:(e=!0)=>{if(!i.value||e){if(r.value&&!e){if(0!==o.value.length)return;r.value=!1}e&&d(),l&&v(l.lastBreadcrumbComment.value)}},loadCommentList:v,addCommentList:u,setFirstFixedComment:p,initCommentPermission:()=>{s.value=!0}}}(e,t,a,r),d=rr(c.commentLoading,r,c.resetCommentList,c.addCommentList,c.setFirstFixedComment),u=c.resetCommentList;return c.resetCommentList=()=>{d.commentRefs.value.clear(),u(),d.resetScrollTrigger()},s(o,(()=>{d.getCommentId()?d.locationComment():c.loadCurrentCommentList()})),s((()=>l.getId),(e=>{e&&d.locationComment(e)})),{...r,...c,...d,commentStore:l}}const cr=Ua(o({__name:"CommentInfo",props:{articleId:{type:Function,required:!0}},emits:["sendEnd","quickReplyEnd"],setup(e,{expose:t,emit:a}){const l=e,o=a,d=n(0),u=n(),g=n(),w=n(),b=i((()=>{var e;return(null==(e=w.value)?void 0:e.commentListContainerRef)||null})),y=tr(i((()=>k.commentList.value)),b),k=sr(l.articleId,(()=>y.calculatedLoadSize.value),(()=>y.markInitialLoadComplete())),x=ir(l.articleId),C=()=>{c((()=>{var e;const t=document.querySelector(".comment-list-container");if(t){const a=window.innerWidth<=768,l="-1"!==x.commentInputVisible.value,n="-1"===x.commentInputVisible.value;a?t.style.paddingBottom=l?"20px":"150px":l?t.style.paddingBottom="1.25rem":n&&(null==(e=g.value)?void 0:e.commentInputWrapperRef)?(d.value=g.value.commentInputWrapperRef.offsetHeight||0,t.style.paddingBottom=`${d.value+12}px`):t.style.paddingBottom="1.25rem"}}))};r((()=>{k.initCommentPermission(),L(),window.addEventListener("resize",L);const e=k.getCommentId();e?k.locationComment(e):k.loadCurrentCommentList()})),M((()=>{window.removeEventListener("resize",L);const e=document.body,t=document.querySelector(".comment-info-container");e.classList.remove("comment-reply-active"),t&&t.classList.remove("has-quick-reply")}));s((()=>x.commentInputVisible.value),(e=>{const t=document.body,a=document.querySelector(".comment-info-container");"-1"!==e?(t.classList.add("comment-reply-active"),a&&a.classList.add("has-quick-reply")):(t.classList.remove("comment-reply-active"),a&&a.classList.remove("has-quick-reply")),C(),setTimeout((()=>{C()}),350)})),s((()=>k.hasCommentPermission.value),(e=>{e&&c(C)})),s(sa,(()=>{c((()=>{document.querySelectorAll(".ProseMirror, .editor-content, .tiptap-editor-wrapper").forEach((e=>{if(e instanceof HTMLElement){const t=e.style.backgroundColor;e.style.backgroundColor="transparent",e.offsetHeight,e.style.backgroundColor=t}}))}))})),s((()=>x.commentReply.value),(e=>{var t;if(e&&e.content&&Array.isArray(e.content)){(null==(t=e.content)?void 0:t.some((e=>!(!e.content||!Array.isArray(e.content))&&e.content.some((e=>!("text"!==e.type||!e.text||!e.text.trim())||"text"!==e.type)))))||(x.commentReply.value=void 0)}}));const L=()=>{c((()=>{var e;if(u.value&&(null==(e=g.value)?void 0:e.commentInputWrapperRef)){const e=u.value.offsetWidth;g.value.commentInputWrapperRef.style.width=`${e}px`,C()}}))},S=e=>{k.resetCommentList(),y.resetToInitialLoad(),e!==k.lastBreadcrumbIndex.value&&k.breadcrumb.value.splice(e+1);x.clearAllQuickReplyContent()({commentList:k.commentList.value}),k.loadCommentList(k.breadcrumb.value[e])},T=e=>{k.resetCommentList(),y.resetToInitialLoad();x.clearAllQuickReplyContent()({commentList:k.commentList.value}),k.addBreadcrumb(e),k.loadCommentList(e)},R=e=>{x.handleCommentReplyClick(e,{isLastBreadcrumb:k.isLastBreadcrumb.value})},A=e=>{x.debouncedQuickReplyComment(e,{isLastBreadcrumb:k.isLastBreadcrumb.value,onSuccess:e=>{k.locationComment(e),x.commentInputVisible.value="-1"}}),o("quickReplyEnd")},E=()=>{x.debouncedSendComment(g.value,{lastBreadcrumbComment:k.lastBreadcrumbComment.value,onSuccess:e=>{k.locationComment(e)}}),o("sendEnd")},_=e=>{k.sortType.value=e,k.resetCommentList(),y.resetToInitialLoad(),k.loadCurrentCommentList()};return t({loadCurrentCommentList:k.loadCurrentCommentList}),(e,t)=>(m(),f("div",{ref_key:"commentInfoRef",ref:u,class:"comment-info-container"},[v(Fi,{breadcrumb:h(k).breadcrumb.value,"model-value":h(k).sortType.value,"onUpdate:modelValue":_,onBreadcrumbClick:S,onLocationComment:h(k).locationComment},null,8,["breadcrumb","model-value","onLocationComment"]),v(Qi,{ref_key:"commentListRef",ref:w,"comment-list":h(k).commentList.value,"flash-comment-id":h(k).flashCommentId.value,"show-reply-list-btn":h(k).showReplyListBtn.value,"comment-input-visible":h(x).commentInputVisible.value,"comment-scroll-trigger":h(k).commentScrollTrigger.value,"comment-loading":h(k).commentLoading.value,"comment-no-more":h(k).commentNoMore.value,"has-comment-permission":h(k).hasCommentPermission.value,"quick-reply-loading":h(x).quickReplyLoading.value,onLoadMoreComments:t[0]||(t[0]=e=>h(k).loadCurrentCommentList(!1)),onShowReplyList:T,onHandleCommentReplyClick:R,onInteractionBtn:h(x).interactionBtn,onFavoriteBtn:h(x).favoriteBtn,onQuickReplyComment:A,onUpdateEditor:h(x).updateEditor,onUpdateCommentRef:h(k).updateCommentRef},null,8,["comment-list","flash-comment-id","show-reply-list-btn","comment-input-visible","comment-scroll-trigger","comment-loading","comment-no-more","has-comment-permission","quick-reply-loading","onInteractionBtn","onFavoriteBtn","onUpdateEditor","onUpdateCommentRef"]),v(F,{name:"slide-up",appear:""},{default:p((()=>[D(v(er,{"comment-reply":h(x).commentReply.value,"onUpdate:commentReply":t[1]||(t[1]=e=>h(x).commentReply.value=e),"send-comment-loading":h(x).sendCommentLoading.value,onSendComment:E,ref_key:"commentMainInputRef",ref:g},null,8,["comment-reply","send-comment-loading"]),[[$,h(k).hasCommentPermission.value&&"-1"==h(x).commentInputVisible.value]])])),_:1})],512))}}),[["__scopeId","data-v-f96c628a"]]);function dr(){const e=Nn(),t=je(),a=n({}),l=n(!0),o=()=>e.getId,i=()=>{l.value=!0;const e=o();e&&jn.detail(e).then((e=>{const t=null==e?void 0:e.data;t&&(a.value=function(e){return{id:e.id,title:e.title,tags:e.tag?e.tag.split(","):[],tag:e.tag||"",operationLevel:e.operationLevel,publishedScope:e.publishedScope,content:e.content,contentObj:Hn.toJsonObject(e.content),publisher:e.publisher,publisherAvatar:e.publisherAvatar||"",isOwner:e.isOwner,ipLocation:e.ipLocation,publishedAt:e.publishedAt?oi.getRelativeTime(e.publishedAt):"",likeCount:e.likeCount,isLike:e.isLike,dislikeCount:e.dislikeCount,isDislike:e.isDislike,favoriteCount:e.favoriteCount,isFavorite:e.isFavorite,commentCount:e.commentCount,lastModified:e.lastModified?oi.getRelativeTime(e.lastModified):"",exactPublishedAt:e.publishedAt?oi.toTimeString(e.publishedAt):"",exactLastModified:e.lastModified?oi.toTimeString(e.lastModified):"",shareUsers:e.shareUsers}}(t),l.value=!1),Zt.debug("article detail: ",a.value)})).catch((e=>{l.value=!1,e.response&&403===e.response.status?(ba.error("哎呀，您没有权限查看这篇文章"),Ic.push("/")):ba.error("加载文章失败，请稍后重试")}))};return{article:a,articleLoading:l,getArticleId:o,loadArticleDetail:i,loadArticleDetailCount:()=>{const e=o();e&&jn.detail(e).then((e=>{const t=null==e?void 0:e.data;t&&(a.value.likeCount=t.likeCount,a.value.dislikeCount=0,a.value.favoriteCount=0,a.value.commentCount=t.commentCount)}))},backHome:()=>{Ic.push("/")},initialize:()=>{r((()=>{i()})),s(t,((e,t)=>{var a;e.params.articleId!==(null==(a=null==t?void 0:t.params)?void 0:a.articleId)&&i()})),s(sa,(()=>{c((()=>{if(!l.value){const e=document.querySelector(".article-content .ProseMirror");e instanceof HTMLElement&&(e.classList.add("theme-priority"),e.offsetHeight)}}))}))}}}const ur={class:"article-layout"},mr={key:0,class:"article-info-container"},pr={class:"article-header"},vr={class:"article-header-content-wrapper"},hr={class:"article-header-content"},gr={class:"article-tag-container"},fr={class:"flex-column-start"},wr={class:"action-buttons-container"},br={class:"edit-button-container"},yr={class:"interaction-container"},kr={class:"comment-count-container"},xr={class:"article-content"},Cr={style:{"padding-right":"1rem"}},Lr=Ua(o({__name:"Article",props:{articleId:{default:void 0}},emits:["article-edit-success","back-to-home"],setup(e,{expose:t,emit:a}){const l=a,o=dr(),i=(r=o.article,{interactionBtn:(e,t)=>{const a={targetType:1,targetId:e,actionType:t};lr.save({...a,type:a.targetType}).then((e=>{const a=null==e?void 0:e.data;if(a){const e=a;r.value.likeCount=e.likeCount||0,r.value.dislikeCount=e.dislikeCount||0;const l=1===t;e.cancel?l?(ba.info("赞取消"),r.value.isLike=!1):(ba.info("踩取消"),r.value.isDislike=!1):l?(ba.success("赞 :)"),r.value.isLike=!0):(ba.warning("踩 :("),r.value.isDislike=!0)}}))},favoriteBtn:e=>{const t={targetType:1,targetId:e};ar.save(t).then((e=>{const t=null==e?void 0:e.data;if(t){const e=t;r.value.favoriteCount=e.count||0,e.cancel?(ba.info("取消收藏"),r.value.isFavorite=!1):(ba.success("已收藏"),r.value.isFavorite=!0)}}))}});var r;const s=function(e){return{toggleTimeFormat:t=>{"publish"===t?void 0===e.value.showExactPublishTime?e.value.showExactPublishTime=!0:e.value.showExactPublishTime=!e.value.showExactPublishTime:void 0===e.value.showExactModifyTime?e.value.showExactModifyTime=!0:e.value.showExactModifyTime=!e.value.showExactModifyTime}}}(o.article),c=n(),d=n();o.initialize();const b=()=>{c.value&&o.article.value&&c.value.openEditArticleDialog(o.article.value)},y=()=>{o.loadArticleDetail(),d.value&&d.value.loadCurrentCommentList(),o.article.value&&l("article-edit-success",o.article.value)};return t({reloadArticleDetail:async()=>{await o.loadArticleDetail()},getCurrentArticle:()=>o.article.value}),(e,t)=>(m(),f("div",ur,[v(Qo,{show:h(o).articleLoading.value},null,8,["show"]),h(o).articleLoading.value?C("",!0):(m(),f("div",mr,[g("div",pr,[g("div",vr,[g("div",hr,[g("h2",null,w(h(o).article.value.title),1),g("div",gr,[(m(!0),f(L,null,S(h(o).article.value.tags,(e=>(m(),u(h(Me),{class:"article-tag",key:e,type:"primary"},{default:p((()=>[E(w(e),1)])),_:2},1024)))),128))])])]),g("div",fr,[v(h(he),{type:"info",class:"display-block time-clickable",onClick:t[0]||(t[0]=e=>h(s).toggleTimeFormat("publish"))},{default:p((()=>[E(" 发布时间："+w(h(o).article.value.showExactPublishTime?h(o).article.value.exactPublishedAt:h(o).article.value.publishedAt),1)])),_:1}),v(h(he),{type:"info",class:"display-block time-clickable",onClick:t[1]||(t[1]=e=>h(s).toggleTimeFormat("modify"))},{default:p((()=>[E(" 最近修改："+w(h(o).article.value.showExactModifyTime?h(o).article.value.exactLastModified:h(o).article.value.lastModified),1)])),_:1}),v(h(he),{type:"info",class:"display-block"},{default:p((()=>[E(" 拥有者："+w(h(o).article.value.publisher)+" | 等级："+w(h(o).article.value.operationLevel)+" | ip: "+w(h(o).article.value.ipLocation),1)])),_:1})]),g("div",wr,[g("div",br,[h(o).article.value.isOwner?(m(),u(h(fn),{key:0,class:"cursor-pointer",size:28,onClick:b})):C("",!0),v(h(Wl),{class:"cursor-pointer",size:28,onClick:h(o).backHome},null,8,["onClick"])]),g("div",yr,[v(h(Jl),{color:h(o).article.value.isLike?"var(--blue)":"",size:20,class:"cursor-pointer",onClick:t[2]||(t[2]=e=>h(i).interactionBtn(h(o).article.value.id,1))},null,8,["color"]),E(" "+w(h(o).article.value.likeCount)+" ",1),v(h(Kl),{color:h(o).article.value.isDislike?"var(--blue)":"",size:20,class:"cursor-pointer",onClick:t[3]||(t[3]=e=>h(i).interactionBtn(h(o).article.value.id,0))},null,8,["color"]),E(" "+w(h(o).article.value.dislikeCount)+" ",1),v(h(wn),{color:h(o).article.value.isFavorite?"var(--blue)":"",size:20,class:"cursor-pointer",onClick:t[4]||(t[4]=e=>h(i).favoriteBtn(h(o).article.value.id))},null,8,["color"]),E(" "+w(h(o).article.value.favoriteCount),1)]),g("div",kr,[v(h(Xl),{size:20}),E(w(h(o).article.value.commentCount),1)])])]),g("div",xr,[v(h(ae),null,{default:p((()=>[g("div",Cr,[v(Lo,{modelValue:h(o).article.value.contentObj,"onUpdate:modelValue":t[5]||(t[5]=e=>h(o).article.value.contentObj=e),editable:!1,"file-bucket":h($o),"all-extensions":!0,"character-limit":h(yo),"use-thumbnail":!0},null,8,["modelValue","file-bucket","character-limit"])])])),_:1})])])),v(Ho,{ref_key:"articleModalRef",ref:c,onSuccess:y},null,512),v(cr,{ref_key:"commentInfoRef",ref:d,articleId:h(o).getArticleId,onQuickReplyEnd:h(o).loadArticleDetailCount,onSendEnd:h(o).loadArticleDetailCount},null,8,["articleId","onQuickReplyEnd","onSendEnd"])]))}}),[["__scopeId","data-v-c7492ac5"]]),Sr={},Tr=function(e,t,a){let l=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const a=document.querySelector("meta[property=csp-nonce]"),n=(null==a?void 0:a.nonce)||(null==a?void 0:a.getAttribute("nonce"));l=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in Sr)return;Sr[e]=!0;const t=e.endsWith(".css"),a=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${a}`))return;const l=document.createElement("link");return l.rel=t?"stylesheet":"modulepreload",t||(l.as="script"),l.crossOrigin="",l.href=e,n&&l.setAttribute("nonce",n),document.head.appendChild(l),t?new Promise(((t,a)=>{l.addEventListener("load",t),l.addEventListener("error",(()=>a(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return l.then((t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)}))},Mr={class:"article-header"},Rr={class:"flex-between-center"},Ar={class:"article-content"},Er=Ua(o({__name:"ArticleCard",props:{article:{},index:{},cardColor:{},isDragging:{type:Boolean},draggedArticle:{},dragOverCardId:{},dragOverPosition:{},isSingleCardRow:{type:Boolean},dragStyle:{}},emits:["toggleScope","startLongPress","cancelLongPress","download","setEditor"],setup(e,{emit:t}){const a=e,l=t,n=i((()=>{var e;const t=a.isDragging&&(null==(e=a.draggedArticle)?void 0:e.id)===a.article.id,l=a.isDragging&&a.dragOverCardId===a.article.id;return{dragging:t,"drag-over-before":l&&"before"===a.dragOverPosition&&!a.isSingleCardRow,"drag-over-after":l&&"after"===a.dragOverPosition&&!a.isSingleCardRow,"drag-over-before-vertical":l&&"before"===a.dragOverPosition&&a.isSingleCardRow,"drag-over-after-vertical":l&&"after"===a.dragOverPosition&&a.isSingleCardRow}})),o=i((()=>{var e;return{backgroundColor:a.cardColor,...a.isDragging&&(null==(e=a.draggedArticle)?void 0:e.id)===a.article.id?a.dragStyle:{}}})),r=i((()=>{const e=a.article.publishedScope===To.PERSONAL;return a.article.isOwner?`点击切换为${e?"公开":"个人"}可见`:(e?"个人":"公开")+"可见"})),s=()=>{const e=Ic.resolve({name:"Article",params:{articleId:a.article.id}}),t=window.open(e.href,"_blank");null==t||t.focus()},c=e=>{l("startLongPress",e,a.article,e.currentTarget)},d=e=>{l("startLongPress",e,a.article,e.currentTarget)};return(e,t)=>(m(),u(h(Re),{class:b(["card-item cursor-pointer",n.value]),"data-article-id":e.article.id,onClick:B(s,["ctrl"]),"header-style":"padding-bottom:0.25rem;border-bottom: var(--border-1);",style:k(o.value)},{header:p((()=>[g("div",Mr,[e.article.isOwner?(m(),u(h(le),{key:0},{trigger:p((()=>[g("div",{class:"scope-icon-wrapper clickable",onClick:t[0]||(t[0]=B((t=>e.$emit("toggleScope",e.article)),["stop"]))},[(m(),u(I(e.article.publishedScope==h(To).PERSONAL?h(tn):h(an)),{size:18}))])])),default:p((()=>[E(" "+w(r.value),1)])),_:1})):C("",!0),g("div",{class:"article-title",onClick:B(s,["stop"])},w(e.article.title),1)])])),"header-extra":p((()=>[v(h(se),{round:"",size:45,src:e.article.publisherAvatar,"object-fit":"cover",class:"article-avatar",onMousedown:B(c,["stop"]),onTouchstart:B(d,["stop"]),onMouseup:t[1]||(t[1]=B((t=>e.$emit("cancelLongPress")),["stop"])),onMouseleave:t[2]||(t[2]=B((t=>e.$emit("cancelLongPress")),["stop"])),onTouchcancel:t[3]||(t[3]=B((t=>e.$emit("cancelLongPress")),["stop"])),onContextmenu:t[4]||(t[4]=B((()=>{}),["prevent"]))},null,8,["src"])])),default:p((()=>[g("div",Rr,[g("div",null,[(m(!0),f(L,null,S(e.article.tags,(e=>(m(),u(h(Me),{type:"primary",class:"card-tag",key:e},{default:p((()=>[E(w(e),1)])),_:2},1024)))),128))]),g("div",null,[v(h(hn),{size:24,class:"cursor-pointer",onClick:t[5]||(t[5]=B((t=>e.$emit("download",e.article.id)),["stop"]))})])]),g("div",Ar,[v(h(ae),{style:{"padding-right":"0.5rem"}},{default:p((()=>[v(Lo,{ref:t=>t&&e.$emit("setEditor",e.article.id,t),modelValue:e.article.contentObj,"onUpdate:modelValue":t[6]||(t[6]=t=>e.article.contentObj=t),editable:!1,"file-bucket":h($o),"all-extensions":!0,"character-limit":h(yo)},null,8,["modelValue","file-bucket","character-limit"])])),_:1})])])),_:1},8,["class","data-article-id","style"]))}}),[["__scopeId","data-v-6444c80f"]]),_r={class:"trash-bin-text"},Ir=Ua(o({__name:"TrashBin",props:{visible:{type:Boolean},isActive:{type:Boolean}},setup:e=>(e,t)=>(m(),u(F,{name:"trash-bin-fade"},{default:p((()=>[e.visible?(m(),f("div",{key:0,class:b(["trash-bin",{"trash-bin-active":e.isActive}])},[(m(),f("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",width:"48",height:"48",style:k({color:e.isActive?"#ff4444":"#666666"})},t[0]||(t[0]=[g("path",{fill:"currentColor",d:"M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"},null,-1)]),4)),g("span",_r,w(e.isActive?"释放删除":"拖拽到此处删除"),1)],2)):C("",!0)])),_:1}))}),[["__scopeId","data-v-4a042d79"]]);function zr(e,t,a){const l=e.cloneNode(!0);!function(e,t){const a=window.getComputedStyle(e);Pr(e,t,a);const l=e.querySelectorAll("*"),n=t.querySelectorAll("*");for(let o=0;o<l.length&&o<n.length;o++){const e=l[o];Pr(e,n[o],window.getComputedStyle(e))}}(e,l);const n=document.createElement("div");return n.style.cssText=`\n    position: fixed;\n    left: ${a.x}px;\n    top: ${a.y}px;\n    width: ${.5*t.width}px;\n    height: ${.5*t.height}px;\n    transform: translate(-50%, -50%) scale(1);\n    opacity: 0.8;\n    z-index: 9999;\n    pointer-events: none !important;\n    transition: none;\n    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\n    border-radius: 0.5rem;\n    overflow: hidden;\n    transform-origin: center center;\n  `,l.style.cssText=`\n    width: ${t.width}px;\n    height: ${t.height}px;\n    transform: scale(0.5);\n    transform-origin: top left;\n    margin: 0;\n    position: relative;\n    pointer-events: none !important;\n  `,function(e){e.style.pointerEvents="none !important",e.style.userSelect="none !important",e.style.setProperty("-webkit-user-select","none","important"),e.style.setProperty("-moz-user-select","none","important"),e.style.setProperty("-ms-user-select","none","important");const t=["onclick","onmousedown","onmouseup","ontouchstart","ontouchend","onscroll","onwheel"];t.forEach((t=>e.removeAttribute(t)));e.querySelectorAll("*").forEach((e=>{const a=e;a.style.pointerEvents="none !important",a.style.userSelect="none !important",a.style.setProperty("-webkit-user-select","none","important"),a.style.setProperty("-moz-user-select","none","important"),a.style.setProperty("-ms-user-select","none","important"),t.forEach((e=>a.removeAttribute(e)))}));e.querySelectorAll('.n-scrollbar, .article-content, [style*="overflow"]').forEach((e=>{const t=e;t.style.overflow="hidden !important",t.style.pointerEvents="none !important",t.classList.contains("article-content")&&(t.style.height="auto !important",t.style.maxHeight="none !important")}));e.querySelectorAll(".n-scrollbar-rail").forEach((e=>e.remove()));e.querySelectorAll(".n-scrollbar-content").forEach((e=>{const t=e;t.style.overflow="visible !important",t.style.height="auto !important",t.style.maxHeight="none !important",t.style.pointerEvents="none !important"}));e.querySelectorAll("iframe, script, noscript, object, embed").forEach((e=>e.remove()))}(l),n.appendChild(l),document.body.appendChild(n),Zt.debug("DOM克隆元素已创建"),n}function Br(e){e&&e.parentNode&&(e.parentNode.removeChild(e),Zt.debug("克隆元素已清理"))}function Pr(e,t,a){let l="";["background","background-color","background-image","background-size","background-position","color","font-family","font-size","font-weight","font-style","border","border-radius","border-color","border-width","border-style","padding","margin","width","height","max-width","max-height","min-width","min-height","display","position","top","left","right","bottom","flex","flex-direction","flex-wrap","justify-content","align-items","text-align","text-decoration","text-transform","line-height","opacity","visibility","box-shadow","text-shadow","transform","transition"].forEach((e=>{const t=a.getPropertyValue(e);t&&"none"!==t&&"auto"!==t&&"initial"!==t&&(l+=`${e}: ${t} !important; `)})),l+="\n    pointer-events: none !important;\n    user-select: none !important;\n  ",t.classList.contains("article-content")?l+="\n      overflow: hidden !important;\n      height: auto !important;\n      max-height: none !important;\n    ":t.classList.contains("n-scrollbar-content")?l+="\n      overflow: visible !important;\n      height: auto !important;\n      max-height: none !important;\n    ":l+="\n      overflow: hidden !important;\n    ",l&&(t.style.cssText=(t.style.cssText||"")+l)}function Dr(e,t,a,l,n,o,i,r,s,c,d,u,m){const p=e=>{if(e instanceof MouseEvent)return{clientX:e.clientX,clientY:e.clientY};if(e instanceof TouchEvent){const t=e.touches.length>0?e.touches[0]:e.changedTouches[0];if(t)return{clientX:t.clientX,clientY:t.clientY}}return{clientX:0,clientY:0}},v=()=>{const e=document.querySelectorAll(".card-item:not(.dragging)");n.value=null,o.value=null,i.value=!1,e.forEach((e=>{var a,l;const s=e.getBoundingClientRect(),{x:c,y:d}=r.value;if(c>=s.left&&c<=s.right&&d>=s.top&&d<=s.bottom){const r=e.dataset.articleId;if(r&&r!==(null==(a=t.value)?void 0:a.id)){n.value=r;const t=(e=>{const t=document.querySelectorAll(".card-item:not(.dragging)"),a=e.getBoundingClientRect(),l=document.querySelector(".article-container");if((l?l.getBoundingClientRect().width:window.innerWidth)<=768)return!0;let n=0;return t.forEach((e=>{const t=e.getBoundingClientRect();!(t.bottom<=a.top||t.top>=a.bottom)&&n++})),1===n})(e);i.value=t,Zt.debug("拖拽悬停检测:",{articleId:r,isOnlyCard:t,containerWidth:null==(l=document.querySelector(".article-container"))?void 0:l.getBoundingClientRect().width}),t?(o.value=d<s.top+s.height/2?"before":"after",Zt.debug("垂直插入位置:",o.value)):(o.value=c<s.left+s.width/2?"before":"after",Zt.debug("水平插入位置:",o.value))}}}))};let h=0;const g=()=>{var e;const a=Date.now();a-h>50&&(h=a,(null==(e=t.value)?void 0:e.isOwner)&&(()=>{const e=document.querySelector(".trash-bin");if(!e)return;const t=e.getBoundingClientRect(),{x:a,y:n}=r.value;l.value=a>=t.left&&a<=t.right&&n>=t.top&&n<=t.bottom})(),v())},f=t=>{if(!e.value)return;t.preventDefault();const{clientX:a,clientY:l}=p(t);r.value={x:a,y:l},c.value&&(c.value.style.left=`${a}px`,c.value.style.top=`${l}px`),g()},w=()=>{var a,i,r,p;e.value&&!s.value&&(s.value=!0,Zt.debug("拖拽结束"),d.value&&(d.value(),d.value=null),l.value&&(null==(a=t.value)?void 0:a.isOwner)?(Zt.debug("触发删除操作"),null==(i=m.onDelete)||i.call(m,t.value)):n.value&&o.value&&t.value&&(Zt.debug("触发重新排序操作"),null==(r=m.onReorder)||r.call(m,t.value.id,n.value,o.value)),c.value&&(Br(c.value),c.value=null),u(),null==(p=m.onDragEnd)||p.call(m),setTimeout((()=>{s.value=!1}),100))};return{startDragging:(l,n,o)=>{var i;e.value=!0,t.value=n,n.isOwner&&(a.value=!0,Zt.debug("显示垃圾篓 - 文章拥有者"));const{clientX:s,clientY:u}=p(l);r.value={x:s,y:u};const v=o.closest(".card-item");if(v){const e=v.getBoundingClientRect();c.value=zr(v,e,r.value)}d.value=function(e){const{onMove:t,onEnd:a}=e,l=e=>{t(e)},n=()=>{a(),o()};document.addEventListener("mousemove",l,{passive:!1}),document.addEventListener("mouseup",n),document.addEventListener("touchmove",l,{passive:!1}),document.addEventListener("touchend",n),document.addEventListener("touchcancel",n),Zt.debug("事件监听器已添加");const o=()=>{document.removeEventListener("mousemove",l),document.removeEventListener("mouseup",n),document.removeEventListener("touchmove",l),document.removeEventListener("touchend",n),document.removeEventListener("touchcancel",n),Zt.debug("事件监听器已清理")};return o}({onMove:f,onEnd:w}),null==(i=m.onDragStart)||i.call(m,n)},handleDragMove:f,handleDragEnd:w}}function $r(e={}){const{isDragging:t,draggedArticle:a,showTrashBin:l,isOverTrashBin:o,dragOverCardId:r,dragOverPosition:s,isSingleCardRow:c,dragPosition:d,dragStyle:u,isEnding:m,clonedElement:p,eventHandlers:v,forceReset:h,resetDragState:g}=function(){const e=n(!1),t=n(null),a=n(!1),l=n(!1),o=n(null),r=n(null),s=n(!1),c=n({x:0,y:0}),d=n(null),u=n(null),m=n(!1),p=i((()=>({}))),v=()=>{e.value=!1,t.value=null,a.value=!1,l.value=!1,o.value=null,r.value=null,s.value=!1};return z((()=>{d.value&&Br(d.value),u.value&&u.value()})),{isDragging:e,draggedArticle:t,showTrashBin:a,isOverTrashBin:l,dragOverCardId:o,dragOverPosition:r,isSingleCardRow:s,dragPosition:c,dragStyle:p,isEnding:m,clonedElement:d,eventHandlers:u,forceReset:()=>{Zt.debug("强制重置拖拽状态"),u.value&&(u.value(),u.value=null),d.value&&(Br(d.value),d.value=null),v(),m.value=!1},resetDragState:v}}(),{isLongPressActive:f,startLongPress:w,cancelLongPress:b}=function(){let e=null,t={x:0,y:0};const a=n(!1),l=e=>{if(e instanceof MouseEvent)return{clientX:e.clientX,clientY:e.clientY};if(e instanceof TouchEvent){const t=e.touches.length>0?e.touches[0]:e.changedTouches[0];if(t)return{clientX:t.clientX,clientY:t.clientY}}return{clientX:0,clientY:0}},o=()=>{e&&(clearTimeout(e),e=null),a.value=!1};return{isLongPressActive:a,startLongPress:(n,i,r,s)=>{Zt.debug("开始长按检测:",i.title),n.preventDefault(),n.stopPropagation(),o();const{clientX:c,clientY:d}=l(n);t={x:c,y:d},a.value=!0;const u=e=>{if(!a.value)return;const{clientX:n,clientY:i}=l(e);Math.sqrt(Math.pow(n-t.x,2)+Math.pow(i-t.y,2))>10&&(Zt.debug("移动距离超过阈值，取消长按"),o(),p())},m=()=>{o(),p()},p=()=>{a.value=!1,document.removeEventListener("mousemove",u),document.removeEventListener("touchmove",u),document.removeEventListener("mouseup",m),document.removeEventListener("touchend",m),document.removeEventListener("touchcancel",m)};document.addEventListener("mousemove",u,{passive:!1}),document.addEventListener("touchmove",u,{passive:!1}),document.addEventListener("mouseup",m),document.addEventListener("touchend",m),document.addEventListener("touchcancel",m),e=setTimeout((()=>{a.value&&(Zt.debug("长按触发，开始拖拽:",i.title),p(),s(n,i,r))}),500)},cancelLongPress:()=>{Zt.debug("取消长按"),a.value&&(Zt.debug("取消长按检测"),o())},cleanup:o}}(),{startDragging:y}=Dr(t,a,l,o,r,s,c,d,m,p,v,g,e);return{isDragging:t,draggedArticle:a,showTrashBin:l,isOverTrashBin:o,dragPosition:d,dragStyle:u,dragOverCardId:r,dragOverPosition:s,isSingleCardRow:c,isLongPressActive:f,startLongPress:(e,t,a)=>{w(e,t,a,y)},cancelLongPress:b,forceReset:h}}function Ur(e,t,a,l,n,o){const i=(i=!1,r,s)=>{Zt.debug("loadArticles called:",{loadMore:i,loading:a.value,noMore:l.value,searchCondition:e,forceLoadSize:s});const d=void 0!==s?s:o.value;if(a.value||l.value)return Zt.debug("loadArticles early return:",{loading:a.value,noMore:l.value}),Promise.resolve();a.value=!0;const u=t.value.length>0?t.value[t.value.length-1]:null,m=null==u?void 0:u.id;return new Promise(((o,i)=>{c((()=>{const s={...e.value||{},id:m,loadSize:d};Zt.debug("Making API request with params:",s),jn.search(s,r).then((e=>{if(Zt.debug("API response received:",e),!e||!e.data)return void o();const a=e.data;if(0===a.length)return l.value=!0,void o();a.length<d&&(l.value=!0);if(!(e=>{const a=new Set(t.value.map((e=>e.id))),l=e.filter((e=>!a.has(e.id)));if(0===l.length)return!1;const n=l.map((e=>{var t,a;return{...e,contentObj:Hn.toJsonObject(e.content),publisherAvatar:(a=e.publisherAvatar,Ra.getResourceURL(a)),tags:(null==(t=e.tag)?void 0:t.split(","))||[]}}));return t.value=[...t.value,...n],!0})(a))return l.value=!0,void o();n.value+=a.length})).catch((e=>{"CanceledError"!==e.name&&"canceled"!==e.message?(Zt.error("加载文章失败:",e),l.value=!1,(null==r?void 0:r.aborted)?o():i(e)):o()})).finally((()=>{(null==r?void 0:r.aborted)||(a.value=!1)}))}))}))};return{resetList:()=>{t.value=[],l.value=!1,n.value=0,i()},loadArticles:i}}function Fr(e){const t=function(){const e=n([]),t=n(!1),a=n(!1),l=n(6),o=n(new Map),i=n(0),r=n(1);return{articleList:e,loading:t,noMore:a,cardColSpan:l,articleTiptapEditorMap:o,containerRef:n(null),scrollContainerRef:n(null),currentLoadedArticlesCount:i,cardsPerRow:r}}(),{articleList:a,loading:l,noMore:o,cardColSpan:r,containerRef:c,currentLoadedArticlesCount:d,cardsPerRow:u}=t,m=function(e,t,a,l,n,o){const r=["#ffd6d6","#ffe8d1","#fff8c4","#d5edd7","#d0e8fa","#ded6f2","#ebcfe9","#f8d4de"],c=["#8c3a3a","#7d6339","#75763a","#366d5a","#355678","#534878","#664766","#6a4251"],d=i((()=>sa.value===Qt.DARK)),u=i((()=>{var e,o;const i=t.value,r=(null==(e=a.value)?void 0:e.clientHeight)||0;let s;if(0===l.value)s=Math.ceil(r/470)*i,s=Math.max(i,s);else{const e=Math.ceil(((null==(o=a.value)?void 0:o.clientHeight)||0)/470)*i;i<=2?s=2*i:(s=Math.ceil(e/2),s=Math.ceil(s/i)*i),s=Math.max(i,s)}return Zt.debug("Calculated Load Size:",s,"Cards Per Row:",i,"Current Loaded Articles:",n.value,"Card Height:",470),s}));return s(t,((e,t)=>{if(e!==t){const a=l.value%e;if(0!==a){const l=e-a;Zt.debug(`视口变化，每行卡片数从 ${t} 变为 ${e}，需要加载 ${l} 篇文章以补齐倍数。`),o(!0,void 0,l)}}})),{getCardColor:(e,t)=>{const a=d.value?c:r;return a[t%a.length]},calculatedLoadSize:u,updateColSpan:()=>{const a=window.innerWidth;let l=24,n=1;a>=1680?(l=6,n=4):a>=1260?(l=8,n=3):a>=840?(l=12,n=2):(l=24,n=1),e.value=l,t.value=n}}}(r,u,c,i((()=>a.value.length)),d,Ur(e.searchCondition,a,l,o,d,i((()=>0))).loadArticles),p=Ur(e.searchCondition,a,l,o,d,m.calculatedLoadSize),v=function(e){const t=Ae();return{handleToggleScope:e=>{const a=e.publishedScope===To.PERSONAL?To.PUBLIC:To.PERSONAL;t.warning(zo({title:"切换发布范围",content:`确定要将文章《${e.title}》切换为${Mo[a]}可见吗？`,positiveText:"确定",negativeText:"取消",onPositiveClick:()=>{jn.togglePublishedScope(e.id).then((t=>{200===t.code?(e.publishedScope=a,ba.success(`文章已切换为${Mo[a]}可见`)):ba.error(t.message||"操作失败")}))}}))},handleDeleteArticle:a=>{t.warning(zo({title:"删除文章",content:`确定要删除文章《${a.title}》吗？此操作不可恢复。`,positiveText:"删了",negativeText:"算了",onPositiveClick:()=>{jn.delete(a.id).then((t=>{if(200===t.code){const t=e.value.findIndex((e=>e.id===a.id));t>-1&&e.value.splice(t,1),ba.success("文章已删除")}else ba.error(t.message||"删除失败")})).catch((()=>{ba.error("删除失败，请稍后再试")}))}}))},handleReorderArticles:(t,a,l)=>{const n=e.value.findIndex((e=>e.id===t)),o=e.value.findIndex((e=>e.id===a));if(-1===n||-1===o)return;const[i]=e.value.splice(n,1);let r=o;r="after"===l?n<o?o:o+1:o,e.value.splice(r,0,i),ba.success("移动成功！")}}}(a);return{...t,...m,...p,...v}}const Vr={class:"infinite-load-info"},Or=Ua(o({__name:"ArticleList",props:{searchCondition:{}},emits:["reset"],setup(e,{expose:t,emit:a}){const l=e,{articleList:n,loading:o,noMore:i,cardColSpan:c,articleTiptapEditorMap:d,containerRef:w,scrollContainerRef:b,getCardColor:y,updateColSpan:k,resetList:x,loadArticles:T,handleToggleScope:M,handleDeleteArticle:R,handleReorderArticles:A}=Fr(l),{isDragging:E,draggedArticle:_,showTrashBin:I,isOverTrashBin:z,dragStyle:B,dragOverCardId:P,dragOverPosition:D,isSingleCardRow:$,startLongPress:U,cancelLongPress:F,isLongPressActive:V}=$r({onDragStart:e=>{Zt.debug("开始拖拽文章:",e.title)},onDelete:R,onReorder:A});s(l.searchCondition,((e,t)=>{Zt.debug("搜索条件变化:",{oldCondition:t,newCondition:e,articleListLength:n.value.length});Object.keys(e).some((a=>(["searchKey","tag"].includes(a)||!!["owner","interaction","favorite"].includes(a))&&e[a]!==t[a]))?(Zt.info("搜索条件发生实质性变化，重置文章列表"),x(),T()):Zt.debug("搜索条件未发生实质性变化，不执行重置")}),{deep:!0}),r((()=>{k(),window.addEventListener("resize",O)}));const O=()=>{k()},H=()=>{T(!0)},j=(e,t,a)=>{U(e,t,a)},N=e=>{const t=d.value.get(e);(null==t?void 0:t.editor)&&jn.md(e,t.editor)},q=(e,t)=>{d.value.set(e,t)};t({loadArticles:T,resetList:x});const Y=()=>{},W=()=>{},J=()=>{V&&(Zt.debug("容器触摸取消 - 取消长按检测"),F())};return(e,t)=>(m(),f("div",{class:"article-container",ref_key:"containerRef",ref:w,onTouchstart:Y,onTouchend:W,onTouchcancel:J},[v(h(Ce),{onLoad:H,distance:100,class:"infinite-scroll-container",ref_key:"scrollContainerRef",ref:b},{default:p((()=>[v(h(Ee),{gutter:20,style:{width:"100%","box-sizing":"border-box",margin:"0 auto",padding:"0 0.25rem",flex:"1","overflow-y":"auto"}},{default:p((()=>[(m(!0),f(L,null,S(h(n),((e,t)=>(m(),u(h(_e),{key:e.id,span:h(c)},{default:p((()=>[v(Er,{article:e,index:t,"card-color":h(y)(e.id,t),"is-dragging":h(E),"dragged-article":h(_)||void 0,"drag-over-card-id":h(P)||void 0,"drag-over-position":h(D)||void 0,"is-single-card-row":h($),"drag-style":h(B),onToggleScope:h(M),onStartLongPress:j,onCancelLongPress:h(F),onDownload:N,onSetEditor:q},null,8,["article","index","card-color","is-dragging","dragged-article","drag-over-card-id","drag-over-position","is-single-card-row","drag-style","onToggleScope","onCancelLongPress"])])),_:2},1032,["span"])))),128))])),_:1}),g("div",Vr,[h(o)?(m(),u(h(Le),{key:0,class:"display-flex"})):C("",!0),h(i)?(m(),u(h(Se),{key:1,description:"没有更多文章了..."})):C("",!0)])])),_:1},512),v(Ir,{visible:h(I),"is-active":h(z)},null,8,["visible","is-active"])],544))}}),[["__scopeId","data-v-f5916f28"]]);const Hr=o({__name:"Danmaku",props:{danmus:{type:Array,required:!0,default:()=>[]},channels:{type:Number,default:0},autoplay:{type:Boolean,default:!0},loop:{type:Boolean,default:!1},useSlot:{type:Boolean,default:!1},debounce:{type:Number,default:100},speeds:{type:Number,default:200},randomChannel:{type:Boolean,default:!1},fontSize:{type:Number,default:18},top:{type:Number,default:4},right:{type:Number,default:0},isSuspend:{type:Boolean,default:!1},extraStyle:{type:String,default:""}},emits:["list-end","play-end","dm-over","dm-out","update:danmus"],setup(e,{expose:t,emit:a}){const l=e,o=a,s=n(document.createElement("div")),c=n(document.createElement("div")),d=n(0),u=n(0),p=n(0),v=n(0),h=n(48),w=n(0),y=n(!1),k=n(!1),x=A({});const C=function(e,t,a="modelValue"){return i({get:()=>e[a],set:e=>{t(`update:${a}`,e)}})}(l,o,"danmus"),L=A({channels:i((()=>l.channels||v.value)),autoplay:i((()=>l.autoplay)),loop:i((()=>l.loop)),useSlot:i((()=>l.useSlot)),debounce:i((()=>l.debounce)),randomChannel:i((()=>l.randomChannel))}),S=A({height:i((()=>h.value)),fontSize:i((()=>l.fontSize)),speeds:i((()=>{const e=l.speeds,t=d.value;if(t<320)return.25*e;if(t<960)return.5*e;if(t>=1920)return e;return e*(.5+(t-960)/960*.5)})),top:i((()=>l.top)),right:i((()=>l.right))}),{initCore:T,resize:E}=function(e,t,a,l,n){function o(){if(a.value=e.value.offsetWidth,l.value=e.value.offsetHeight,0===a.value||0===l.value)throw new Error("获取不到容器宽高")}return{initCore:o,resize:function(){o();const e=t.value.getElementsByClassName("dm");for(let t=0;t<e.length;t++){const l=e[t],o=null!==l.querySelector("img");let i=l.offsetWidth;o&&(i+=100);const r=a.value+i;l.style.setProperty("--dm-scroll-width",`-${r}px`),l.style.left=`${a.value}px`,l.style.animationDuration=r/n.speeds+"s"}}}}(s,c,d,u,S),{draw:I,insert:z,add:B,push:P}=function(e,t,a,l,n,o,i,r,s,c,d,u,m){function p(o){try{if(o&&o.content){let e=o.content;if("string"==typeof e)try{e=JSON.parse(e)}catch(p){Zt.warn("弹幕JSON解析失败，使用原始文本:",p)}f({id:o.commentId||Date.now().toString(),content:e})}const g=r.loop?t.value%e.value.length:t.value,w=o||e.value[g];let b=document.createElement("div");r.useSlot&&m?b=v(w,g).$el:(b.innerHTML=w,b.setAttribute("style",u),b.style.fontSize=`${i.fontSize}px`,b.style.lineHeight="3rem"),b.classList.add("dm"),n.value.appendChild(b),b.style.opacity="0";const y=b.offsetHeight;let k=b.offsetWidth;const x=null!==b.querySelector("img");x&&(k+=150),c.value||(c.value=48),r.channels||(s.value=Math.floor(l.value/(i.height+i.top)));let C=h(b);if(C>=0){const o=i.height,s=()=>{C*(o+i.top)+y>=l.value&&(C--,s())};s(),Zt.debug("danmaku height top: ",o,i.top),b.classList.add("move"),b.dataset.index=`${g}`,b.dataset.channel=C.toString(),b.style.opacity="1";const c=C*(o+i.top)+"px";b.style.top=c,b.style.left=`${a.value}px`;const u=()=>{const e=x?Math.max(b.offsetWidth+100,k):b.offsetWidth,t=a.value+e,l=t/i.speeds;b.style.animationDuration=`${l}s`,b.style.setProperty("--dm-scroll-width",`-${t}px`)};if(x?requestAnimationFrame((()=>{requestAnimationFrame(u)})):u(),b.addEventListener("animationend",(()=>{Number(b.dataset.index)!==e.value.length-1||r.loop||d("play-end",b.dataset.index),n.value&&n.value.removeChild(b)})),t.value++,x){const e=b.querySelectorAll("img");let t=0;const l=e.length,n=()=>{if(t++,t===l){const e=b.offsetWidth+50,t=a.value+e,l=t/i.speeds;b.style.animationDuration=`${l}s`,b.style.setProperty("--dm-scroll-width",`-${t}px`)}};e.forEach((e=>{e.complete?n():(e.addEventListener("load",n,{once:!0}),e.addEventListener("error",n,{once:!0}))}))}}else n.value.removeChild(b)}catch(g){Zt.error("添加弹幕时发生错误:",g)}}function v(e,t){return H({render:()=>R("div",{},[m&&m({danmu:e,index:t})])}).mount(document.createElement("div"))}function h(e){let t=[...Array(r.channels).keys()];r.randomChannel&&(t=t.sort((()=>.5-Math.random())));for(const a of t){const t=o[a];if(!t||!t.length)return o[a]=[e],e.addEventListener("animationend",(()=>o[a].splice(0,1))),a%r.channels;for(let l=0;l<t.length;l++){const n=g(t[l])-10;if(n<=.75*(e.offsetWidth-t[l].offsetWidth)||n<=0)break;if(l===t.length-1)return o[a].push(e),e.addEventListener("animationend",(()=>o[a].splice(0,1))),a%r.channels}}return-1}function g(e){const t=e.offsetWidth||parseInt(e.style.width),a=e.getBoundingClientRect().right||n.value.getBoundingClientRect().right+t;return n.value.getBoundingClientRect().right-a}function f(a){if(t.value===e.value.length)return e.value.push(a),e.value.length-1;{const l=t.value%e.value.length;return e.value.splice(l,0,a),l+1}}return{draw:function(){if(e.value.length)if(t.value>e.value.length-1){const e=n.value.children.length;r.loop&&(e<t.value&&(d("list-end"),t.value=0),p())}else p()},insert:p,add:f,push:function(t){return e.value.push(t),e.value.length-1},getChannelIndex:h,getDanRight:g,getSlotComponent:v}}(C,w,d,u,c,x,S,L,v,h,o,l.extraStyle,j().dm),{play:D,clear:$,stop:U,pause:F,show:V,hide:O,getPlayState:N}=function(e,t,a,l,n,o,i,r,s){function c(){clearInterval(i.value),i.value=0}function d(){c(),a.value=0}return{play:function(){if(l.value=!1,!i.value){const e=s instanceof Object?s.value:s;i.value=window.setInterval((()=>r()),e)}},clearTimer:c,clear:d,stop:function(){Object.assign(o,{}),e.value.innerHTML="",l.value=!0,n.value=!1,d()},pause:function(){l.value=!0},show:function(){n.value=!1},hide:function(){n.value=!0},getPlayState:function(){return!l.value}}}(c,0,w,k,y,x,p,I,L.debounce),{initSuspendEvents:q}=function(e,t){return{initSuspendEvents:function(){let a=[];e.value.addEventListener("mouseover",(e=>{let l=e.target;l.className.includes("dm")||(l=l.closest(".dm")||l),l.className.includes("dm")&&(a.includes(l)||(t("dm-over",{el:l}),l.classList.add("pause"),a.push(l)))})),e.value.addEventListener("mouseout",(e=>{let l=e.target;l.className.includes("dm")||(l=l.closest(".dm")||l),l.className.includes("dm")&&(t("dm-out",{el:l}),l.classList.remove("pause"),a.forEach((e=>{e.classList.remove("pause")})),a=[])}))}}}(c,o);function Y(){T(),l.isSuspend&&q(),L.autoplay&&D()}return r((()=>{Y()})),M((()=>{$()})),t({container:s,dmContainer:c,hidden:y,paused:k,danmuList:C,getPlayState:N,resize:E,play:D,pause:F,stop:U,show:V,hide:O,reset:function(){h.value=0,Y()},add:B,push:P,insert:z}),(e,t)=>(m(),f("div",{ref_key:"container",ref:s,class:"vue-danmaku"},[g("div",{ref_key:"dmContainer",ref:c,class:b(["danmus",{show:!y.value},{paused:k.value}])},null,2),_(e.$slots,"default")],512))}}),jr=500,Nr=.5,qr=1,Yr={minScale:.5,maxScale:3};function Wr(e,t,a,l){a("image-preview-open");const{modal:n,previewImg:o}=function(){const e=document.createElement("div");e.classList.add("modal-overlay");const t=document.createElement("img");return t.alt="图片预览",e.appendChild(t),document.body.appendChild(e),e.classList.add("modal-overlay-active"),{modal:e,previewImg:t}}(),i=function(e,t){return{isOriginal:"true"===e.dataset.isOriginal,thumbnailSrc:e.dataset.thumbnailSrc||t,isExternal:t.startsWith("http://")||t.startsWith("https://")}}(e,t);i.isExternal||!i.thumbnailSrc.includes(Aa)||i.isOriginal?(o.src=e.src,o.style.opacity=String(qr)):function(e,t,a,l){e.src=l.src,e.style.opacity=String(Nr);const n=document.createElement("div");n.classList.add("loading-spinner"),t.appendChild(n);const o=new Image,i=Date.now();o.onload=()=>{const t=Date.now()-i,a=()=>{n.style.display="none",e.src=o.src,e.style.opacity=String(qr),e.dataset.originalFullUrl=o.src};t<jr?setTimeout(a,jr-t):a()},o.onerror=()=>{n.style.display="none",e.style.opacity=String(qr)},o.src=Ra.getResourceURL(a.replace(Aa,""))}(o,n,t,e);const r=il(o,Yr,(()=>s()),n),s=function(e,t,a,l,n,o,i,r){return()=>{e.classList.remove("modal-overlay-active"),e.addEventListener("transitionend",(()=>{e.classList.contains("modal-overlay-active")||(!n.isExternal&&t.dataset.originalFullUrl&&n.thumbnailSrc.includes(Aa)&&!n.isOriginal&&(a.src=t.dataset.originalFullUrl,a.dataset.isOriginal="true",a.dataset.originalSrc=l.replace(Aa,"")),document.body.removeChild(e),i(),r.cleanup(),o("image-preview-close"))}),{once:!0})}}(n,o,e,t,i,a,al(n,(()=>s()),r.handleWheelZoom),r);r.initialize()}function Jr(e){var t,a,l,n;if(!e)return"";let o="";try{if("text"===e.type&&e.text){let l=e.text;if(e.marks&&e.marks.length>0)for(const n of e.marks)"bold"===n.type?l=`<strong>${l}</strong>`:"italic"===n.type?l=`<em>${l}</em>`:"underline"===n.type?l=`<u>${l}</u>`:"strike"===n.type?l=`<s>${l}</s>`:"code"===n.type?l=`<code>${l}</code>`:"link"===n.type&&(null==(t=n.attrs)?void 0:t.href)?l=`<a href="${n.attrs.href}" target="_blank">${l}</a>`:"textStyle"===n.type&&(null==(a=n.attrs)?void 0:a.color)&&(l=`<span style="color: ${n.attrs.color}">${l}</span>`);o+=l}else if("mention"===e.type&&(null==(l=e.attrs)?void 0:l.label))try{const t=e.attrs.avatar?Ra.getResourceURL(e.attrs.avatar):"",a=['data-type="mention"',`data-id="${e.attrs.id||""}"`,`data-label="${e.attrs.label}"`,`data-avatar="${e.attrs.avatar||""}"`].join(" "),l=`<span class="mention-name">@${e.attrs.label}</span>`;o+=`<span class="mention" ${a} contenteditable="false">${l}${t?`<img src="${t}" alt="${e.attrs.label}" class="mention-avatar" loading="eager" decoding="async" referrerpolicy="no-referrer" onerror="this.style.display='none';" />`:""}</span>`}catch(i){o+=`<span class="mention" data-type="mention" data-label="${e.attrs.label}"><span class="mention-name">@${e.attrs.label}</span></span>`}else if("image"===e.type&&(null==(n=e.attrs)?void 0:n.src))try{const t=e.attrs.src;if(t.startsWith("http://")||t.startsWith("https://"))o+=`<img src="${t}" class="danmaku-image"\n                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"\n                     data-original-src="${t}"\n                     data-is-original="true"\n                     onerror="this.replaceWith(document.createTextNode('[图片]'));" />`;else{let e,a;if(t.includes(Aa))e=Ra.getResourceURL(t),a=t;else{const l=t.split("/"),n=l.pop();a=`${l.join("/")}${Aa}/${n}`,e=Ra.getResourceURL(a)}const l=Ra.getResourceURL(t.replace(Aa,""));o+=`<img src="${e}" class="danmaku-image"\n                     loading="eager" decoding="async" alt="图片" referrerpolicy="no-referrer"\n                     data-original-src="${t}"\n                     data-thumbnail-src="${a}"\n                     data-is-original="false"\n                     onerror="if (!this.dataset.tried) { this.dataset.tried = 'true'; this.src = '${l}'; this.dataset.isOriginal = 'true'; }\n                     else { this.replaceWith(document.createTextNode('[图片]')); }" />`}}catch(i){o+='<span class="image-placeholder">[图片]</span>'}else if("hardBreak"===e.type)o+=" ";else if(e.type&&["paragraph","heading","blockquote"].includes(e.type)){if(e.content&&Array.isArray(e.content)){o+=e.content.map((e=>Jr(e))).join("")+" "}}else e.content&&Array.isArray(e.content)&&(o+=e.content.map((e=>Jr(e))).join(""))}catch(i){if("text"===e.type&&e.text)return e.text;if("image"===e.type)return"[图片]"}return o}function Kr(e){try{return e&&"object"==typeof e?e.content&&Array.isArray(e.content)&&0!==e.content.length?Jr(e):"<span>[空内容]</span>":"<span>[无法显示内容]</span>"}catch(t){return"<span>[渲染失败]</span>"}}const Xr=["innerHTML"],Gr=o({__name:"DanmakuRenderer",props:{content:{type:Object,required:!0}},emits:["image-preview-open","image-preview-close"],setup(e,{emit:t}){const a=e,l=t,{content:o}=N(a),s=n(null),c=i((()=>Kr(o.value)));return r((()=>{const e=e=>{const t=e.target;if("IMG"===t.tagName&&t.classList.contains("danmaku-image")){const a=t,n=a.dataset.originalSrc;n&&(e.stopPropagation(),e.preventDefault(),Wr(a,n,l))}};s.value&&(s.value.addEventListener("click",e),s.value._danmakuImageClickHandler=e)})),z((()=>{s.value&&s.value._danmakuImageClickHandler&&(s.value.removeEventListener("click",s.value._danmakuImageClickHandler),delete s.value._danmakuImageClickHandler)})),(e,t)=>(m(),f("div",{class:"danmaku-renderer",ref_key:"rendererRef",ref:s,innerHTML:c.value},null,8,Xr))}}),Zr="/topic/comments";const Qr={class:"comment-container"},es={key:0,class:"danmaku-empty-hint"},ts=["onDblclick"],as={class:"comment-danmaku-publisher"},ls={class:"comment-danmaku-content"},ns=Ua(o({__name:"CommentDanmaku",props:{searchCondition:{type:Object,required:!0},loop:{type:Boolean,default:!1},pause:{type:Boolean,default:!1}},emits:["search","update:loop","update:pause"],setup(e,{expose:t,emit:a}){const l=e,{commentList:o,danmakuRef:i,danmakuConfig:c,isSubscribed:d}=function(e,t){const a=n([]),l=n(),o=A({isSuspend:!0,useSlot:!0,speeds:160,debounce:200,top:10,right:0,channels:0,randomChannel:!0,fontSize:14,loop:!1,pause:!1,autoplay:!1}),i=n(!1);return s((()=>e.value),(e=>{o.loop=e})),s((()=>t.value),(e=>{o.pause=e,l.value&&(e?l.value.pause():l.value.play())})),{commentList:a,danmakuRef:l,danmakuConfig:o,isSubscribed:i}}(q(l,"loop"),q(l,"pause")),{clearDanmaku:u,addCommentList:b,handleDanmuClick:k,subscribeComment:x,unsubscribeComment:L,handleImagePreviewOpen:S,handleImagePreviewClose:T,resize:M,play:R,pause:E}=function(e,t,a,l){const n=e=>Ra.getResourceURL(e),o=a=>{const l=ta.parse(a);l&&fa("comment-received:"+l.commentId,(()=>{if(!e.value.some((e=>e.id===l.commentId)))if(l.commentId&&l.articleId&&l.publisher)try{const e={id:l.commentId,articleId:l.articleId,publisher:l.publisher,publisherAvatar:n(l.publisherAvatar||""),content:l.content||"",contentObj:l.content?Hn.toJsonObject(l.content):{type:"doc",content:[{type:"paragraph",content:[]}]}};t.value&&t.value.insert(e)}catch(a){Zt.error("处理弹幕消息时出错:",a)}else Zt.warn("消息缺少必要字段:",l)}))};return{clearDanmaku:()=>{e.value=[],t.value&&t.value.reset()},addCommentList:a=>{if(Zt.debug("addCommentList 被调用:",{inputList:a,inputLength:(null==a?void 0:a.length)||0,currentListLength:e.value.length}),!a||!Array.isArray(a))return void Zt.warn("addCommentList 收到无效的评论列表:",a);const l=new Set(e.value.map((e=>e.id))),o=a.filter((e=>!l.has(e.id)));if(Zt.debug("过滤后的新评论:",{newCommentsLength:o.length,existingIds:Array.from(l)}),0!==o.length)try{const a=o.map((e=>({...e,publisherAvatar:n(e.publisherAvatar||""),content:e.content||"",contentObj:e.content?Hn.toJsonObject(e.content):{type:"doc",content:[{type:"paragraph",content:[]}]}}))),l=10;for(let t=0;t<a.length;t+=l){const n=a.slice(t,t+l);setTimeout((()=>{e.value.push(...n)}),50*t)}e.value.length>200&&(e.value=e.value.slice(-200)),t.value&&t.value.play()}catch(i){Zt.error("处理评论列表时出错:",i)}else Zt.debug("没有新评论需要添加")},handleDanmuClick:e=>{const t=Ic.resolve({name:"Article",params:{articleId:e.articleId,commentId:e.id}}),a=window.open(t.href,"_blank");a&&a.focus()},subscribeComment:()=>{l.value||(mi.subscribe(Zr,o),l.value=!0)},unsubscribeComment:()=>{l.value&&(mi.unsubscribe(Zr),l.value=!1)},handleImagePreviewOpen:()=>{t.value&&t.value.pause()},handleImagePreviewClose:()=>{t.value&&!a.pause&&t.value.play()},resize:()=>{t.value&&t.value.resize()},play:()=>{t.value&&t.value.play()},pause:()=>{t.value&&t.value.pause()}}}(o,i,c,d);return t({get danmakuLoop(){return c.loop},set danmakuLoop(e){c.loop=e},clearDanmaku:u,addCommentList:b,subscribeComment:x,unsubscribeComment:L,resize:M,play:R,pause:E}),r((()=>{x()})),z((()=>{L()})),(e,t)=>(m(),f("div",Qr,[0===h(o).length?(m(),f("div",es,t[1]||(t[1]=[y('<div class="hint-content" data-v-31debac7><h3 data-v-31debac7>🎯 弹幕搜索</h3><p data-v-31debac7>在搜索框中输入关键词，或选择筛选条件来查看相关评论弹幕：</p><ul data-v-31debac7><li data-v-31debac7><strong data-v-31debac7>我的</strong> - 查看我发布的评论</li><li data-v-31debac7><strong data-v-31debac7>互动</strong> - 查看我互动过的评论</li><li data-v-31debac7><strong data-v-31debac7>收藏</strong> - 查看我收藏的评论</li></ul><p class="hint-note" data-v-31debac7>💡 也可以点击标签来筛选特定主题的评论</p></div>',1)]))):C("",!0),v(Hr,Y({ref_key:"danmakuRef",ref:i,class:"comment-danmaku",danmus:h(o),"onUpdate:danmus":t[0]||(t[0]=e=>P(o)?o.value=e:null)},h(c)),{dm:p((({danmu:e})=>[g("span",{class:"comment-danmaku-item cursor-pointer",onDblclick:t=>h(k)(e)},[g("span",as,[v(h(se),{round:"",size:28,src:e.publisherAvatar,"object-fit":"cover",lazy:!0},null,8,["src"]),g("span",null,w(e.publisher)+": ",1)]),g("span",ls,[v(Gr,{content:e.contentObj,onImagePreviewOpen:h(S),onImagePreviewClose:h(T)},null,8,["content","onImagePreviewOpen","onImagePreviewClose"])])],40,ts)])),_:1},16,["danmus"])]))}}),[["__scopeId","data-v-31debac7"]]),os=Ua(o({__name:"CreateButton",emits:["click","long-press"],setup(e,{emit:t}){const a=t,l=n(!1);n(!1);const o=n(null),i=n(null),s=n(!1),c=n(null),d=()=>{s.value||a("click")},p=()=>{w()},v=()=>{y()},g=e=>{e.preventDefault(),w()},f=e=>{e.preventDefault(),y()},w=()=>{s.value=!1,c.value=window.setTimeout((()=>{s.value=!0,a("long-press")}),1e3)},y=()=>{c.value&&(clearTimeout(c.value),c.value=null),setTimeout((()=>{s.value=!1}),100)},k=()=>{l.value||(i.value&&(clearTimeout(i.value),i.value=null),l.value=!0,o.value=window.setTimeout((()=>{l.value=!1,x()}),1500))},x=()=>{i.value&&(clearTimeout(i.value),i.value=null);const e=5e3+1e4*Math.random();i.value=window.setTimeout((()=>{C()}),e)},C=()=>{l.value?x():(l.value=!0,o.value=window.setTimeout((()=>{l.value=!1,x()}),1500))};return r((()=>{x()})),z((()=>{o.value&&(clearTimeout(o.value),o.value=null),i.value&&(clearTimeout(i.value),i.value=null),c.value&&(clearTimeout(c.value),c.value=null)})),(e,t)=>(m(),u(h(mn),{size:36,color:"var(--blue)",onClick:d,onMousedown:p,onMouseup:v,onMouseleave:v,onTouchstart:g,onTouchend:f,class:b(["cursor-pointer create-button",{"is-rotating":l.value,"is-long-pressing":s.value}]),ref:"createButtonRef",onMouseenter:k},null,8,["class"]))}}),[["__scopeId","data-v-6d2d7876"]]),is={class:"search-container"},rs=Ua(o({__name:"SearchBar",props:{placeholder:{type:String,default:"感兴趣的内容"},modelValue:{type:Object,required:!0}},emits:["update:modelValue","search"],setup(e,{emit:t}){const a=e,l=t,o=n({...a.modelValue});s((()=>a.modelValue),(e=>{o.value={...e},d()}),{deep:!0});const i=()=>{l("update:modelValue",{...o.value})},r=n(""),c=[{name:"owner",label:"我的"},{name:"interaction",label:"互动"},{name:"favorite",label:"收藏"}],d=()=>{r.value="";for(const e of c)if(o.value[e.name]){r.value=e.name;break}};d();const u=()=>{i(),g(),l("search")},g=()=>{aa.set(Ci,o.value)};return(t,a)=>(m(),f("div",is,[v(h(ie),{style:{"border-radius":"0.5rem"},value:o.value.searchKey,"onUpdate:value":a[0]||(a[0]=e=>o.value.searchKey=e),size:"large",placeholder:e.placeholder,clearable:"",onInput:u},{suffix:p((()=>[v(h(un),{onClick:u,class:"cursor-pointer",size:20})])),_:1},8,["value","placeholder"]),v(h(Ie),{value:r.value,"justify-content":"space-evenly"},{default:p((()=>[(m(),f(L,null,S(c,((e,t)=>v(h(ze),{key:t,name:e.name,label:e.label,onClick:t=>{return a=e.name,r.value===a?r.value="":r.value=a,o.value[a]=!o.value[a],Object.keys(o.value).forEach((e=>{e!==a&&"searchKey"!==e&&"tag"!==e&&(o.value[e]=!1)})),i(),g(),void l("search");var a}},null,8,["name","label","onClick"]))),64))])),_:1},8,["value"])]))}}),[["__scopeId","data-v-070d2ed0"]]),ss={key:0,class:"tag-bar-container"},cs=Ua(o({__name:"TagBar",props:{modelValue:{type:String,default:""}},emits:["update:modelValue","tagSelected"],setup(e,{expose:t,emit:a}){const l=e,o=a,c=n([]),d=n(l.modelValue),v=n([]),g=i((()=>c.value.find((e=>e.name===d.value))));s((()=>l.modelValue),(e=>{Zt.debug("外部标签值变化:",{oldValue:d.value,newValue:e,hotTags:c.value}),d.value=e}));return r((()=>{(async()=>{try{const e=await jn.getHotTags(10);e.data&&(c.value=e.data,Zt.debug("热门标签加载成功:",c.value))}catch(e){Zt.error("加载热门标签失败:",e)}})()})),t({tagSelectionHistory:v,currentTagInfo:g,hotTags:c}),(e,t)=>c.value.length>0?(m(),f("div",ss,[(m(!0),f(L,null,S(c.value,(e=>(m(),u(h(Me),{key:e.name,type:d.value===e.name?"primary":"default",class:"hot-tag",bordered:!1,onClick:t=>(e=>{const t=d.value;d.value===e?d.value="":d.value=e,v.value.push({timestamp:Date.now(),from:t,to:d.value}),v.value.length>10&&v.value.shift(),Zt.debug("标签选择变化:",{prevTag:t,currentTag:d.value,hotTags:c.value}),o("update:modelValue",d.value),o("tagSelected",d.value)})(e.name)},{default:p((()=>[E(w(e.name)+" ("+w(e.count)+") ",1)])),_:2},1032,["type","onClick"])))),128))])):C("",!0)}}),[["__scopeId","data-v-8a1791fe"]]),ds={class:"toggle-button-container"},us={class:"toggle-card-front"},ms={class:"toggle-card-back"},ps=Ua(o({__name:"ToggleButton",props:{value:{type:Boolean,required:!0}},emits:["update:value","toggle"],setup(e,{emit:t}){const a=e,l=t,o=n(!1),s=n(null),c=n(null),d=i((()=>a.value?"评":"文")),u=i((()=>a.value?"文":"评")),y=()=>{l("update:value",!a.value),l("toggle"),s.value&&(clearTimeout(s.value),s.value=null),o.value=!0,s.value=window.setTimeout((()=>{o.value=!1,C()}),800)},k=()=>{o.value||(c.value&&(clearTimeout(c.value),c.value=null),o.value=!0,s.value=window.setTimeout((()=>{o.value=!1,C()}),800))},x=()=>{o.value||C()},C=()=>{c.value&&(clearTimeout(c.value),c.value=null);const e=8e3+12e3*Math.random();c.value=window.setTimeout((()=>{o.value?C():(o.value=!0,s.value=window.setTimeout((()=>{o.value=!1,C()}),800))}),e)};return r((()=>{C()})),z((()=>{s.value&&(clearTimeout(s.value),s.value=null),c.value&&(clearTimeout(c.value),c.value=null)})),(e,t)=>(m(),f("div",ds,[g("div",{class:b(["toggle-card",{"is-flipping":o.value}]),onMouseenter:k,onMouseleave:x,onClick:y,ref:"toggleButtonRef"},[g("div",us,[v(h(he),{type:"info",class:"cursor-pointer",size:32},{default:p((()=>[E(w(d.value),1)])),_:1})]),g("div",ms,[v(h(he),{type:"info",class:"cursor-pointer",size:32},{default:p((()=>[E(w(u.value),1)])),_:1})])],34)]))}}),[["__scopeId","data-v-8934ce6a"]]),vs={URL:"/core/user-privileges",generateCode:async e=>(await xa(vs.URL+"/code",e)).data,activate:async e=>(await xa(vs.URL+"/activation",e)).data,search:async(e,t)=>(await ka(vs.URL,e,{signal:t})).data},hs={URL:"/core/user-privilege-templates",search:async e=>(await ka(hs.URL,{name:e})).data,save:async e=>(await xa(hs.URL,e)).data},gs={...vs,generatePrivilegeCode:async e=>(await xa("/api/user-privilege/generate-code",e)).data,searchUsers:async e=>(await ka("/api/user/search",{keyword:e})).data,searchTemplates:async e=>(await ka("/api/user-privilege-template/search",{keyword:e})).data},fs={...hs,savePrivilegeTemplate:async e=>(await xa("/api/user-privilege-template/save",e)).data},ws={class:"privilege-modal"},bs={class:"tab-content"},ys={key:0,class:"generated-code-section"},ks={class:"code-display"},xs={class:"modal-actions"},Cs={class:"tab-content"},Ls={class:"modal-actions"},Ss=Ua(o({__name:"PrivilegeModal",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(e,{expose:t,emit:a}){const l=e,o=a,r=Be(),s=i({get:()=>l.modelValue,set:e=>o("update:modelValue",e)}),c=n("code"),d=n(),w=n(),b=A({userId:0,privilegeType:0,templateId:void 0,amount:void 0,expireTime:null,verificationType:0}),y=n(null),k=A({name:"",icon:"",description:"",link:"",amount:0,verificationType:0}),x=n(!1),L=n(!1),S=n(""),T=n([]),M=n(!1),R=n([]),_=n(!1),I={userId:{required:!0,message:"请选择用户",type:"number"},privilegeType:{required:!0,message:"请选择特权类型",type:"number"},templateId:{required:!0,validator:(e,t)=>!(0===b.privilegeType&&!t)||new Error("请选择模板")},amount:{required:!0,validator:(e,t)=>!(1===b.privilegeType&&(!t||t<=0))||new Error("请输入有效的付费面额")},expireTime:{required:!0,message:"请选择过期时间"},verificationType:{required:!0,message:"请选择验证类型",type:"number"}},z={name:{required:!0,message:"请输入特权名称"},icon:{required:!0,message:"请上传特权图标"},description:{required:!0,message:"请输入特权描述"},link:{required:!0,message:"请输入特权链接"},amount:{required:!0,message:"请输入付费面额",type:"number"},verificationType:{required:!0,message:"请选择验证类型",type:"number"}},B=async e=>{if(e){M.value=!0;try{const t=await gs.searchUsers(e);t.success&&t.data&&(T.value=t.data.map((e=>({label:e.username,value:e.id,avatar:e.avatar}))))}catch(t){}finally{M.value=!1}}else T.value=[]},P=async e=>{if(e){_.value=!0;try{const t=await gs.searchTemplates(e);t.success&&t.data&&(R.value=t.data.map((e=>({label:e.name,value:e.id}))))}catch(t){}finally{_.value=!1}}else R.value=[]},D=async()=>{if(d.value){try{await d.value.validate()}catch{return}x.value=!0;try{const e={...b,expireTime:y.value?new Date(y.value).toISOString():null},t=await gs.generatePrivilegeCode(e);t.success&&t.data?(S.value=t.data,r.success("激活码生成成功！")):r.error("激活码生成失败")}catch(e){r.error("生成激活码失败，请稍后重试")}finally{x.value=!1}}},$=async()=>{if(w.value){try{await w.value.validate()}catch{return}L.value=!0;try{(await fs.savePrivilegeTemplate(k)).success?(r.success("模板创建成功！"),s.value=!1,o("success"),O()):r.error("模板创建失败")}catch(e){r.error("保存模板失败，请稍后重试")}finally{L.value=!1}}},U=e=>{if(e.fileList.length>0){const t=e.fileList[0];t.url&&(k.icon=t.url)}else k.icon=""},F=async()=>{try{await navigator.clipboard.writeText(S.value),r.success("激活码已复制到剪贴板")}catch(e){r.error("复制失败，请手动复制")}},V=()=>{s.value=!1,O()},O=()=>{Object.assign(b,{userId:0,privilegeType:0,templateId:void 0,amount:void 0,expireTime:null,verificationType:0}),y.value=null,Object.assign(k,{name:"",icon:"",description:"",link:"",amount:0,verificationType:0}),S.value="",c.value="code"};return t({resetForms:O}),(e,t)=>(m(),u(h(re),{show:s.value,"onUpdate:show":t[12]||(t[12]=e=>s.value=e),preset:"dialog",title:"特权管理","mask-closable":!1,style:{width:"600px"}},{default:p((()=>[g("div",ws,[v(h(Ie),{value:c.value,"onUpdate:value":t[11]||(t[11]=e=>c.value=e),type:"line",animated:""},{default:p((()=>[v(h(Pe),{name:"code",tab:"激活码"},{default:p((()=>[g("div",bs,[v(h(ce),{ref_key:"codeFormRef",ref:d,model:b,rules:I,"label-placement":"left","label-width":"100px"},{default:p((()=>[v(h(de),{label:"用户",path:"userId"},{default:p((()=>[v(h(te),{value:b.userId,"onUpdate:value":t[0]||(t[0]=e=>b.userId=e),placeholder:"搜索用户名",filterable:"",remote:"",options:T.value,loading:M.value,onSearch:B,clearable:""},null,8,["value","options","loading"])])),_:1}),v(h(de),{label:"特权类型",path:"privilegeType"},{default:p((()=>[v(h(pe),{value:b.privilegeType,"onUpdate:value":t[1]||(t[1]=e=>b.privilegeType=e)},{default:p((()=>[v(h(De),{value:0},{default:p((()=>t[13]||(t[13]=[E("模板")]))),_:1}),v(h(De),{value:1},{default:p((()=>t[14]||(t[14]=[E("付费")]))),_:1})])),_:1},8,["value"])])),_:1}),0===b.privilegeType?(m(),u(h(de),{key:0,label:"模板",path:"templateId"},{default:p((()=>[v(h(te),{value:b.templateId,"onUpdate:value":t[2]||(t[2]=e=>b.templateId=e),placeholder:"搜索模板名称",filterable:"",remote:"",options:R.value,loading:_.value,onSearch:P,clearable:""},null,8,["value","options","loading"])])),_:1})):C("",!0),1===b.privilegeType?(m(),u(h(de),{key:1,label:"付费面额",path:"amount"},{default:p((()=>[v(h($e),{value:b.amount,"onUpdate:value":t[3]||(t[3]=e=>b.amount=e),placeholder:"请输入付费面额",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1})):C("",!0),v(h(de),{label:"过期时间",path:"expireTime"},{default:p((()=>[v(h(Ue),{value:y.value,"onUpdate:value":t[4]||(t[4]=e=>y.value=e),type:"datetime",placeholder:"选择过期时间",style:{width:"100%"}},null,8,["value"])])),_:1}),v(h(de),{label:"验证类型",path:"verificationType"},{default:p((()=>[v(h(pe),{value:b.verificationType,"onUpdate:value":t[5]||(t[5]=e=>b.verificationType=e)},{default:p((()=>[v(h(De),{value:0},{default:p((()=>t[15]||(t[15]=[E("短信验证")]))),_:1}),v(h(De),{value:1},{default:p((()=>t[16]||(t[16]=[E("二维码验证")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1},8,["model"]),S.value?(m(),f("div",ys,[v(h(Fe),null,{default:p((()=>t[17]||(t[17]=[E("生成的激活码")]))),_:1}),g("div",ks,[v(h(ie),{value:S.value,readonly:"",placeholder:"激活码将在这里显示"},{suffix:p((()=>[v(h(ne),{text:"",onClick:F},{icon:p((()=>[v(h(Nl))])),_:1})])),_:1},8,["value"])])])):C("",!0),g("div",xs,[v(h(ne),{onClick:V,disabled:x.value},{default:p((()=>t[18]||(t[18]=[E("取消")]))),_:1},8,["disabled"]),v(h(ne),{type:"primary",onClick:D,loading:x.value},{default:p((()=>t[19]||(t[19]=[E(" CODE ")]))),_:1},8,["loading"])])])])),_:1}),v(h(Pe),{name:"template",tab:"模板创建"},{default:p((()=>[g("div",Cs,[v(h(ce),{ref_key:"templateFormRef",ref:w,model:k,rules:z,"label-placement":"left","label-width":"100px"},{default:p((()=>[v(h(de),{label:"特权名称",path:"name"},{default:p((()=>[v(h(ie),{value:k.name,"onUpdate:value":t[6]||(t[6]=e=>k.name=e),placeholder:"请输入特权名称"},null,8,["value"])])),_:1}),v(h(de),{label:"特权图标",path:"icon"},{default:p((()=>[v(h(Ve),{"default-file-list":[],max:1,"list-type":"image-card",onChange:U},{default:p((()=>t[20]||(t[20]=[E(" 上传图标 ")]))),_:1})])),_:1}),v(h(de),{label:"特权描述",path:"description"},{default:p((()=>[v(h(ie),{value:k.description,"onUpdate:value":t[7]||(t[7]=e=>k.description=e),type:"textarea",placeholder:"请输入特权描述",rows:3},null,8,["value"])])),_:1}),v(h(de),{label:"特权链接",path:"link"},{default:p((()=>[v(h(ie),{value:k.link,"onUpdate:value":t[8]||(t[8]=e=>k.link=e),placeholder:"请输入特权链接"},null,8,["value"])])),_:1}),v(h(de),{label:"付费面额",path:"amount"},{default:p((()=>[v(h($e),{value:k.amount,"onUpdate:value":t[9]||(t[9]=e=>k.amount=e),placeholder:"请输入付费面额",min:0,precision:2,style:{width:"100%"}},null,8,["value"])])),_:1}),v(h(de),{label:"验证类型",path:"verificationType"},{default:p((()=>[v(h(pe),{value:k.verificationType,"onUpdate:value":t[10]||(t[10]=e=>k.verificationType=e)},{default:p((()=>[v(h(De),{value:0},{default:p((()=>t[21]||(t[21]=[E("短信验证")]))),_:1}),v(h(De),{value:1},{default:p((()=>t[22]||(t[22]=[E("二维码验证")]))),_:1})])),_:1},8,["value"])])),_:1})])),_:1},8,["model"]),g("div",Ls,[v(h(ne),{onClick:V,disabled:L.value},{default:p((()=>t[23]||(t[23]=[E("取消")]))),_:1},8,["disabled"]),v(h(ne),{type:"primary",onClick:$,loading:L.value},{default:p((()=>t[24]||(t[24]=[E(" 创建 ")]))),_:1},8,["loading"])])])])),_:1})])),_:1},8,["value"])])])),_:1},8,["show"]))}}),[["__scopeId","data-v-f7045a43"]]),Ts=Object.freeze(Object.defineProperty({__proto__:null,default:Ss},Symbol.toStringTag,{value:"Module"}));function Ms(){const e=Be(),t=n(!1),a=n(!1),l=n(null),o=n([]),r=n({searchKey:"",owner:!1,interaction:!1,favorite:!1,tag:""}),s=i((()=>Object.entries(r.value).some((([e,t])=>"tag"===e?!!t:"boolean"==typeof t?t:"searchKey"===e&&!!t)))),{searchComments:c,searchArticles:d}=function(e,t,a,l,n){const o=Be();return{searchComments:async i=>{var r;if(Zt.debug("searchComments 被调用:",{hasSearchCondition:l.value,searchCondition:a.value,commentDanmakuRef:!!i}),!l.value)return i&&(i.clearDanmaku(),i.danmakuLoop=!1),void Zt.debug("没有搜索条件，清空弹幕");t.value=!0,e.value=!0;try{const e=await nr.search(a.value,null==(r=n.value)?void 0:r.signal);if(Zt.debug("搜索评论API响应:",e),i){const t=Array.isArray(e.data)?e.data:[];Zt.debug("准备添加评论到弹幕:",{count:t.length,comments:t}),i.addCommentList(t)}}catch(s){"CanceledError"!==s.name&&"canceled"!==s.message&&o.error("加载评论失败，请稍后重试")}finally{e.value=!1,t.value=!1,n.value=null}},searchArticles:async(l,i=!1)=>{if(Zt.debug("searchArticles called:",{loadMore:i,hasRef:!!l,isSearching:t.value,isLoading:e.value,searchCondition:a.value}),!i)if(l){t.value=!0,e.value=!0;try{l.resetList(),Zt.debug("loadArticles completed")}catch(r){Zt.error("searchArticles error:",r),"CanceledError"!==r.name&&"canceled"!==r.message&&o.error("加载文章失败，请稍后重试")}finally{e.value=!1,t.value=!1,n.value=null}}else Zt.warn("articleListRef.value is null/undefined")}}}(t,a,r,s,l),u=()=>{aa.set(Ci,r.value),Zt.debug("保存搜索条件:",r.value)},m=(t,n,i,s=!1)=>{a.value?Zt.warn("搜索被阻止：正在进行其他搜索"):(l.value&&(l.value.abort(),l.value=null),l.value=new AbortController,o.value.push({timestamp:Date.now(),condition:{...r.value},type:t?"article":"comment"}),o.value.length>10&&o.value.shift(),Zt.debug("触发搜索:",{isCardVisible:t,condition:r.value,loadMore:s}),fa("unified_search",(()=>{a.value=!0;try{t?n?d(n,s):(Zt.warn("articleListRef.value is null/undefined"),a.value=!1):i?c(i):(Zt.warn("commentDanmakuRef.value is null/undefined"),a.value=!1)}catch(l){Zt.error("搜索过程中发生错误:",l),e.error("搜索失败，请稍后重试")}finally{setTimeout((()=>{a.value=!1}),500)}}),200))};return{isLoading:t,isSearching:a,searchCondition:r,hasSearchCondition:s,searchHistory:o,getSearchPlaceholder:e=>e?"感兴趣的文章":"有意思的评论",loadSearchCondition:()=>{const e=aa.get(Ci);e&&(r.value=e,Zt.debug("加载搜索条件:",e))},saveSearchCondition:u,search:m,handleTagSelected:(e,t,a,l)=>{r.value.tag=e,Zt.debug("标签选择:",{tagName:e,isCardVisible:t,currentCondition:r.value}),u(),m(t,a,l)},cleanup:()=>{l.value&&(l.value.abort(),l.value=null),o.value=[],Zt.debug("清理搜索状态")}}}const Rs={beforeEnter:e=>{e instanceof HTMLElement&&(e.style.opacity="0",e.style.transform="translateY(2px)")},enter:(e,t)=>{e instanceof HTMLElement?(e.offsetHeight,e.style.transition="opacity 0.2s ease, transform 0.2s ease",e.style.opacity="1",e.style.transform="translateY(0)",setTimeout(t,200)):t()},leave:(e,t)=>{e instanceof HTMLElement?(e.style.transition="opacity 0.2s ease, transform 0.2s ease",e.style.opacity="0",e.style.transform="translateY(-2px)",setTimeout(t,200)):t()}},As={class:"home-layout"},Es={class:"home-layout-top"},_s={class:"left-controls-container"},Is={class:"control-item"},zs={class:"control-item"},Bs={class:"middle-controls-container"},Ps={class:"tag-bar-wrapper"},Ds={class:"home-layout-content"},$s=Ua(o({__name:"Home",props:{initialSearchCondition:{default:void 0},autoLoad:{type:Boolean,default:!0}},emits:["search-condition-change","view-mode-change","article-create-success","privilege-create-success"],setup(e,{expose:t,emit:a}){const l=W((()=>Tr((()=>import("./ActivationCodeModal-BlDwYmT6.js")),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58])))),o=e,i=a,d=Ms(),w=function(){const e=n(!1),t=n(!1);return{danmakuLoop:e,danmakuPause:t,handleDanmakuPauseChange:(e,t)=>{t&&(e?t.pause():t.play())},resetDanmakuState:()=>{e.value=!1,t.value=!1},handleDanmakuSubscription:(e,t)=>{e.value&&(t?e.value.subscribeComment():(e.value.unsubscribeComment(),e.value.clearDanmaku()))},handleDanmakuResize:(e,t)=>{!t&&e.value&&e.value.resize()}}}(),b=function(){const e=n(!0),t=n(0),a=n(null),l=n(null),o=n(null),i=async(e,t=1e3)=>{const a=Date.now();for(;!e()&&Date.now()-a<t;)await new Promise((e=>setTimeout(e,10)));return!!e()};return{isCardVisible:e,onlineCount:t,articleModalRef:a,articleListRef:l,commentDanmakuRef:o,initializeState:()=>{mi.connect();const t=aa.get(xi);null!=t&&(e.value=Boolean(t))},toggleCardVisibility:(t,a,n)=>{aa.set(xi,Boolean(e.value)),e.value?(n(o,!1),t(),c((async()=>{await i((()=>l.value))&&a()}))):(n(o,!0),c((async()=>{await i((()=>o.value))&&a()})))},resetArticleList:()=>{l.value&&l.value.resetList()},openCreateArticleDialog:()=>{var e;null==(e=a.value)||e.openCreateArticleDialog()},createResizeCallback:t=>()=>{t(o,e.value)},cleanup:e=>{window.removeEventListener("resize",e)}}}(),y=n(null),k=n(null),x=n(!1),C=n(!1);s(y,(e=>{b.articleListRef.value=e}),{immediate:!0}),s(k,(e=>{b.commentDanmakuRef.value=e}),{immediate:!0});const L=b.createResizeCallback(w.handleDanmakuResize);r((async()=>{b.initializeState(),o.initialSearchCondition?(d.searchCondition.value={...d.searchCondition.value,...o.initialSearchCondition},d.saveSearchCondition()):d.loadSearchCondition(),await c(),o.autoLoad&&setTimeout((async()=>{await S()}),50),window.addEventListener("resize",L)})),z((()=>{d.cleanup(),b.cleanup(L)}));const S=async(e=!1)=>{if(Zt.debug("执行搜索操作:",{isCardVisible:b.isCardVisible.value,articleListRef:b.articleListRef.value,commentDanmakuRef:b.commentDanmakuRef.value,loadMore:e}),await c(),!b.isCardVisible.value){let e=0;const t=20;for(;!b.commentDanmakuRef.value&&e<t;)await new Promise((e=>setTimeout(e,100))),e++,Zt.debug(`等待弹幕组件准备就绪，重试次数: ${e}`);if(!b.commentDanmakuRef.value)return void Zt.warn("弹幕组件在等待时间内未能准备就绪，跳过搜索")}d.search(b.isCardVisible.value,b.articleListRef.value,b.commentDanmakuRef.value,e),i("search-condition-change",d.searchCondition.value)},T=()=>{b.toggleCardVisibility(b.resetArticleList,S,w.handleDanmakuSubscription),i("view-mode-change",b.isCardVisible.value)},M=()=>{x.value=!0},R=async()=>{b.resetArticleList(),await S(),i("privilege-create-success")},A=()=>{C.value=!0},_=()=>{};return t({performSearch:async(e=!1)=>{await S(e)},resetArticleList:()=>{b.resetArticleList()},toggleViewMode:()=>{T()},getCurrentSearchCondition:()=>d.searchCondition.value}),(e,t)=>(m(),f("div",As,[g("div",Es,[v(F,{name:"fade-slide"},{default:p((()=>[D(g("div",_s,[g("div",Is,[v(h(he),{type:"info",class:"control-label"},{default:p((()=>t[9]||(t[9]=[E("循环：")]))),_:1}),v(h(Oe),{value:h(w).danmakuLoop.value,"onUpdate:value":t[0]||(t[0]=e=>h(w).danmakuLoop.value=e),size:"small"},null,8,["value"])]),g("div",zs,[v(h(he),{type:"info",class:"control-label"},{default:p((()=>t[10]||(t[10]=[E("暂停：")]))),_:1}),v(h(Oe),{value:h(w).danmakuPause.value,"onUpdate:value":[t[1]||(t[1]=e=>h(w).danmakuPause.value=e),t[2]||(t[2]=e=>h(w).handleDanmakuPauseChange(e,h(b).commentDanmakuRef.value))],size:"small"},null,8,["value"])])],512),[[$,!h(b).isCardVisible.value]])])),_:1}),g("div",Bs,[v(ps,{value:h(b).isCardVisible.value,"onUpdate:value":t[3]||(t[3]=e=>h(b).isCardVisible.value=e),onToggle:T},null,8,["value"]),v(rs,{modelValue:h(d).searchCondition.value,"onUpdate:modelValue":t[4]||(t[4]=e=>h(d).searchCondition.value=e),placeholder:h(d).getSearchPlaceholder(h(b).isCardVisible.value),onSearch:S},null,8,["modelValue","placeholder"]),v(os,{onClick:M,onLongPress:A})]),v(Ss,{modelValue:x.value,"onUpdate:modelValue":t[5]||(t[5]=e=>x.value=e),onSuccess:R},null,8,["modelValue"]),v(h(l),{modelValue:C.value,"onUpdate:modelValue":t[6]||(t[6]=e=>C.value=e),onSuccess:_},null,8,["modelValue"]),v(Pi)]),g("div",Ps,[v(cs,{modelValue:h(d).searchCondition.value.tag,"onUpdate:modelValue":t[7]||(t[7]=e=>h(d).searchCondition.value.tag=e),onTagSelected:t[8]||(t[8]=e=>h(d).handleTagSelected(e,h(b).isCardVisible.value,h(b).articleListRef.value,h(b).commentDanmakuRef.value))},null,8,["modelValue"])]),g("div",Ds,[v(F,{onBeforeEnter:h(Rs).beforeEnter,onEnter:h(Rs).enter,onLeave:h(Rs).leave,duration:{enter:200,leave:200},mode:"out-in"},{default:p((()=>[h(b).isCardVisible.value?(m(),u(Or,{key:"article","search-condition":h(d).searchCondition,ref_key:"articleListRef",ref:y,onReset:h(b).resetArticleList},null,8,["search-condition","onReset"])):(m(),u(ns,{key:"comment","search-condition":h(d).searchCondition.value,loop:h(w).danmakuLoop.value,pause:h(w).danmakuPause.value,ref_key:"commentDanmakuRef",ref:k},null,8,["search-condition","loop","pause"]))])),_:1},8,["onBeforeEnter","onEnter","onLeave"])])]))}}),[["__scopeId","data-v-db2411d0"]]),Us="/assets/logo-CJOwX2dT.png",Fs=o({__name:"TurnstileVerification",props:{sitekey:{default:()=>Jt.turnstile.siteKey},theme:{default:"auto"},size:{default:"normal"},retry:{default:"auto"},retryInterval:{default:8e3},refreshExpired:{default:"auto"},language:{default:"auto"},execution:{default:"render"},appearance:{default:"always"},responseField:{type:Boolean,default:!0},responseFieldName:{default:"cf-turnstile-response"},cData:{default:""}},emits:["success","error","expired"],setup(e,{expose:t,emit:a}){const l=e,o=a,i=n();let c=null;const d=async()=>{try{if(await(()=>{const e="https://challenges.cloudflare.com/turnstile/v0/api.js";return window.__TURNSTILE_SCRIPT_LOADED__&&window.turnstile?Promise.resolve():window.__TURNSTILE_SCRIPT_LOADING__?new Promise((e=>{const t=setInterval((()=>{window.__TURNSTILE_SCRIPT_LOADED__&&window.turnstile&&(clearInterval(t),e())}),100)})):window.turnstile?(window.__TURNSTILE_SCRIPT_LOADED__=!0,Promise.resolve()):document.querySelector(`script[src="${e}"]`)?(window.__TURNSTILE_SCRIPT_LOADING__=!0,new Promise((e=>{const t=setInterval((()=>{window.turnstile&&(clearInterval(t),window.__TURNSTILE_SCRIPT_LOADING__=!1,window.__TURNSTILE_SCRIPT_LOADED__=!0,e())}),100)}))):(window.__TURNSTILE_SCRIPT_LOADING__=!0,new Promise(((t,a)=>{const l=document.createElement("script");l.src=e,l.async=!0,l.defer=!0,l.onload=()=>{window.__TURNSTILE_SCRIPT_LOADING__=!1,window.__TURNSTILE_SCRIPT_LOADED__=!0,Zt.debug("Turnstile script loaded successfully"),t()},l.onerror=()=>{window.__TURNSTILE_SCRIPT_LOADING__=!1,Zt.error("Failed to load Turnstile script"),a(new Error("Failed to load Turnstile script"))},document.head.appendChild(l)})))})(),!window.turnstile||!i.value)return void Zt.error("Turnstile not available or container not found");c&&(window.turnstile.remove(c),c=null);const e={sitekey:l.sitekey,callback:e=>{Zt.debug("Turnstile verification successful"),o("success",e)},"error-callback":()=>{Zt.error("Turnstile verification failed"),o("error")},"expired-callback":()=>{Zt.warn("Turnstile verification expired"),o("expired")},theme:l.theme,size:l.size,retry:l.retry,"retry-interval":l.retryInterval,"refresh-expired":l.refreshExpired,language:l.language,execution:l.execution,appearance:l.appearance,"response-field":l.responseField,"response-field-name":l.responseFieldName,cData:l.cData};c=window.turnstile.render(i.value,e),Zt.debug("Turnstile widget rendered with ID:",c)}catch(e){Zt.error("Error rendering Turnstile:",e),o("error")}},u=()=>{window.turnstile&&c&&(window.turnstile.remove(c),c=null,Zt.debug("Turnstile widget removed"))};return t({reset:()=>{window.turnstile&&c&&(window.turnstile.reset(c),Zt.debug("Turnstile widget reset"))},remove:u,getResponse:()=>window.turnstile&&c?window.turnstile.getResponse(c):"",isExpired:()=>!(!window.turnstile||!c)&&window.turnstile.isExpired(c)}),s((()=>l.sitekey),(()=>{l.sitekey&&d()})),r((()=>{l.sitekey&&d()})),z((()=>{u()})),(e,t)=>(m(),f("div",{ref_key:"turnstileContainer",ref:i,class:"turnstile-container"},null,512))}});var Vs=(e=>(e.LOGIN="login",e.REGISTER="register",e.FORGOT="forgot",e))(Vs||{});const Os={class:"email-code-container"},Hs=Ua(o({__name:"EmailCodeInput",props:{value:{},emailCodeState:{},disabled:{type:Boolean,default:!1},buttonType:{default:"info"}},emits:["update:value","send-code","keyup.enter"],setup(e,{emit:t}){const a=e,l=t,n=i({get:()=>a.value,set:e=>l("update:value",e)});return(e,t)=>(m(),f("div",Os,[v(h(ie),{class:"login-form-ipt email-code-input",value:n.value,"onUpdate:value":t[0]||(t[0]=e=>n.value=e),placeholder:"请输入验证码",maxlength:"6",onKeyup:t[1]||(t[1]=V((t=>e.$emit("keyup.enter")),["enter"]))},null,8,["value"]),v(h(ne),{class:"send-code-btn",disabled:e.emailCodeState.sendCodeDisabled||e.disabled,onClick:t[2]||(t[2]=t=>e.$emit("send-code")),text:"",type:e.buttonType,size:"small"},{default:p((()=>[E(w(e.emailCodeState.sendCodeText),1)])),_:1},8,["disabled","type"])]))}}),[["__scopeId","data-v-31eb16cb"]]),js={class:"login-mode-switch"},Ns=Ua(o({__name:"LoginModeSwitch",props:{loginMode:{}},emits:["update:login-mode"],setup(e,{emit:t}){const a=e,l=t,n=i({get:()=>a.loginMode,set:e=>l("update:login-mode",e)});return(e,t)=>(m(),f("div",js,[v(h(Ie),{value:n.value,"onUpdate:value":t[0]||(t[0]=e=>n.value=e),type:"segment",size:"small",class:"login-tabs"},{default:p((()=>[v(h(Pe),{name:"phone",tab:"手机号登录"}),v(h(Pe),{name:"email",tab:"邮箱登录"})])),_:1},8,["value"])]))}}),[["__scopeId","data-v-80bb11d5"]]),qs={class:"forgot-password-link"},Ys={class:"login-form-btn"},Ws=Ua(o({__name:"LoginForm",props:{form:{},loginMode:{},emailCodeState:{},isValidEmail:{type:Boolean},isTurnstileVerified:{type:Boolean},loading:{type:Boolean}},emits:["update:form","update:login-mode","login","flip-card","show-forgot-password","send-email-code","turnstile-success","turnstile-error"],setup(e,{expose:t,emit:a}){const l=e,o=a,r=n(null),c=n(),d=i({get:()=>l.form,set:e=>o("update:form",e)}),w=i({get:()=>l.loginMode,set:e=>o("update:login-mode",e)}),b=i((()=>{const e={};return"phone"===l.loginMode?(e.phone=[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:"blur"}],e.password=[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少需要6个字符",trigger:"blur"}]):"email"===l.loginMode&&(e.email=[{required:!0,message:"邮箱不能为空",trigger:"blur"},{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],e.emailCode=[{required:!0,message:"请输入邮箱验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"验证码格式不正确",trigger:"blur"}]),e})),y=e=>{d.value.phone=e.replace(/\D/g,"")};s((()=>l.loginMode),(()=>{r.value&&r.value.restoreValidation()}));return t({validate:e=>{var t;return null==(t=r.value)?void 0:t.validate(e)},restoreValidation:()=>{var e;null==(e=r.value)||e.restoreValidation()},resetTurnstile:()=>{var e;null==(e=c.value)||e.reset()}}),(e,t)=>(m(),u(h(ce),{"label-placement":"left",model:d.value,rules:b.value,ref_key:"formRef",ref:r,"label-width":90,class:"login-form"},{default:p((()=>[v(Ns,{"login-mode":w.value,"onUpdate:loginMode":t[0]||(t[0]=e=>w.value=e)},null,8,["login-mode"]),"phone"===w.value?(m(),f(L,{key:0},[v(h(de),{label:"手机号",path:"phone"},{default:p((()=>[v(h(ie),{class:"login-form-ipt",value:d.value.phone,"onUpdate:value":t[1]||(t[1]=e=>d.value.phone=e),placeholder:"请输入手机号",maxlength:"11",onInput:y,onKeyup:t[2]||(t[2]=V((t=>e.$emit("login")),["enter"]))},null,8,["value"])])),_:1}),v(h(de),{label:"密码",path:"password"},{default:p((()=>[v(h(ie),{class:"login-form-ipt",value:d.value.password,"onUpdate:value":t[3]||(t[3]=e=>d.value.password=e),type:"password",placeholder:"请输入密码","show-password-on":"click",onKeyup:t[4]||(t[4]=V((t=>e.$emit("login")),["enter"]))},null,8,["value"])])),_:1})],64)):C("",!0),"email"===w.value?(m(),f(L,{key:1},[v(h(de),{label:"邮箱",path:"email"},{default:p((()=>[v(h(ie),{class:"login-form-ipt",value:d.value.email,"onUpdate:value":t[5]||(t[5]=e=>d.value.email=e),placeholder:"请输入邮箱地址",onKeyup:t[6]||(t[6]=V((t=>e.$emit("login")),["enter"]))},null,8,["value"])])),_:1}),v(h(de),{label:"验证码",path:"emailCode"},{default:p((()=>[v(Hs,{value:d.value.emailCode,"onUpdate:value":t[7]||(t[7]=e=>d.value.emailCode=e),"email-code-state":e.emailCodeState,disabled:!e.isValidEmail||!e.isTurnstileVerified,onSendCode:t[8]||(t[8]=t=>e.$emit("send-email-code",h(Vs).LOGIN)),onKeyup:t[9]||(t[9]=V((t=>e.$emit("login")),["enter"]))},null,8,["value","email-code-state","disabled"])])),_:1})],64)):C("",!0),v(Fs,{ref_key:"turnstileRef",ref:c,onSuccess:t[10]||(t[10]=t=>e.$emit("turnstile-success",t)),onError:t[11]||(t[11]=t=>e.$emit("turnstile-error"))},null,512),g("div",qs,[v(h(ne),{text:"",type:"tertiary",onClick:t[12]||(t[12]=t=>e.$emit("show-forgot-password")),size:"small"},{default:p((()=>t[15]||(t[15]=[E(" 忘记密码？ ")]))),_:1})]),g("div",Ys,[v(h(ne),{class:"login-btn",type:"info",onClick:t[13]||(t[13]=t=>e.$emit("login")),loading:e.loading},{default:p((()=>t[16]||(t[16]=[E(" 登录 ")]))),_:1},8,["loading"]),v(h(ne),{class:"flip-btn",onClick:t[14]||(t[14]=t=>e.$emit("flip-card"))},{default:p((()=>t[17]||(t[17]=[E("注册")]))),_:1})])])),_:1},8,["model","rules"]))}}),[["__scopeId","data-v-3402c077"]]),Js={class:"register-form-btn"},Ks=Ua(o({__name:"RegisterForm",props:{form:{},emailCodeState:{},isValidEmail:{type:Boolean},isTurnstileVerified:{type:Boolean}},emits:["update:form","register","flip-card","send-email-code","turnstile-success","turnstile-error"],setup(e,{expose:t,emit:a}){const l=e,o=a,r=n(null),s=n(),c=i({get:()=>l.form,set:e=>o("update:form",e)}),d={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:10,message:"用户名长度在 3 到 10 个字符之间",trigger:"blur"}],phone:[{required:!0,message:"手机号不能为空",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入有效的手机号",trigger:"blur"}],email:[{required:!0,message:"邮箱不能为空",trigger:"blur"},{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],emailCode:[{required:!0,message:"请输入邮箱验证码",trigger:"blur"},{pattern:/^\d{6}$/,message:"验证码格式不正确",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,message:"密码长度至少需要6个字符",trigger:"blur"}],reenteredPassword:[{required:!0,message:"请再次输入密码",trigger:["input","blur"]},{validator:(e,t)=>!!l.form.password&&l.form.password.startsWith(t)&&l.form.password.length>=t.length,message:"两次密码输入不一致",trigger:"input"},{validator:(e,t)=>t===l.form.password,message:"两次密码输入不一致",trigger:["blur","password-input"]}],job:[{required:!0,message:"请输入职业",trigger:"blur"},{min:2,max:8,message:"职业名长度在 2 到 8 个字符之间",trigger:"blur"}]},f=e=>c.value.phone=e.replace(/\D/g,"");return t({validate:e=>{var t;return null==(t=r.value)?void 0:t.validate(e)},restoreValidation:()=>{var e;null==(e=r.value)||e.restoreValidation()},resetTurnstile:()=>{var e;null==(e=s.value)||e.reset()}}),(e,t)=>(m(),u(h(ce),{"label-placement":"left",model:c.value,rules:d,ref_key:"formRef",ref:r,"label-width":100,class:"register-form"},{default:p((()=>[v(h(de),{label:"用户名",path:"username"},{default:p((()=>[v(h(ie),{class:"register-form-ipt",value:c.value.username,"onUpdate:value":t[0]||(t[0]=e=>c.value.username=e),placeholder:"请输入用户名",onKeyup:t[1]||(t[1]=V((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),v(h(de),{label:"手机号",path:"phone"},{default:p((()=>[v(h(ie),{class:"register-form-ipt",maxlength:"11",value:c.value.phone,"onUpdate:value":t[2]||(t[2]=e=>c.value.phone=e),onInput:f,placeholder:"请输入手机号",onKeyup:t[3]||(t[3]=V((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),v(h(de),{label:"邮箱",path:"email"},{default:p((()=>[v(h(ie),{class:"register-form-ipt",value:c.value.email,"onUpdate:value":t[4]||(t[4]=e=>c.value.email=e),placeholder:"请输入邮箱地址",onKeyup:t[5]||(t[5]=V((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),v(h(de),{label:"邮箱验证码",path:"emailCode"},{default:p((()=>[v(Hs,{value:c.value.emailCode,"onUpdate:value":t[6]||(t[6]=e=>c.value.emailCode=e),"email-code-state":e.emailCodeState,disabled:!e.isValidEmail||!e.isTurnstileVerified,"button-type":"primary",onSendCode:t[7]||(t[7]=t=>e.$emit("send-email-code",h(Vs).REGISTER)),onKeyup:t[8]||(t[8]=V((t=>e.$emit("register")),["enter"]))},null,8,["value","email-code-state","disabled"])])),_:1}),v(h(de),{label:"密码",path:"password"},{default:p((()=>[v(h(ie),{class:"register-form-ipt",value:c.value.password,"onUpdate:value":t[9]||(t[9]=e=>c.value.password=e),type:"password",placeholder:"请输入密码","show-password-on":"click",onKeyup:t[10]||(t[10]=V((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),v(h(de),{label:"确认密码",path:"reenteredPassword"},{default:p((()=>[v(h(ie),{class:"register-form-ipt",value:c.value.reenteredPassword,"onUpdate:value":t[11]||(t[11]=e=>c.value.reenteredPassword=e),type:"password",disabled:!c.value.password,placeholder:"请确认密码","show-password-on":"click",onKeyup:t[12]||(t[12]=V((t=>e.$emit("register")),["enter"]))},null,8,["value","disabled"])])),_:1}),v(h(de),{label:"职业",path:"job"},{default:p((()=>[v(h(ie),{class:"register-form-ipt",value:c.value.job,"onUpdate:value":t[13]||(t[13]=e=>c.value.job=e),placeholder:"请输入职业",onKeyup:t[14]||(t[14]=V((t=>e.$emit("register")),["enter"]))},null,8,["value"])])),_:1}),v(Fs,{ref_key:"turnstileRef",ref:s,onSuccess:t[15]||(t[15]=t=>e.$emit("turnstile-success",t)),onError:t[16]||(t[16]=t=>e.$emit("turnstile-error"))},null,512),g("div",Js,[v(h(ne),{class:"login-btn",type:"info",onClick:t[17]||(t[17]=t=>e.$emit("register"))},{default:p((()=>t[19]||(t[19]=[E("注册并登录")]))),_:1}),v(h(ne),{class:"flip-btn",onClick:t[18]||(t[18]=t=>e.$emit("flip-card"))},{default:p((()=>t[20]||(t[20]=[E("登录")]))),_:1})])])),_:1},8,["model"]))}}),[["__scopeId","data-v-a88dce1c"]]);const Xs={class:"static-elements"},Gs={key:0,class:"clouds-container"},Zs={key:1,class:"stars-container"},Qs={class:"dynamic-elements"},ec={key:0,class:"dandelions-container"},tc={key:1,class:"fireflies-container"},ac=Ua(o({__name:"BackgroundAnimation",props:{particleCount:{type:Number,default:30,description:"动态元素的数量（蒲公英/萤火虫）"},cloudCount:{type:Number,default:9,description:"云朵数量"},enableClouds:{type:Boolean,default:!0,description:"是否显示云朵"},enableDandelions:{type:Boolean,default:!0,description:"是否显示蒲公英（浅色模式）"},enableStars:{type:Boolean,default:!0,description:"是否显示星星（暗色模式）"},enableFireflies:{type:Boolean,default:!0,description:"是否显示萤火虫（暗色模式）"},customLightGradient:{type:String,default:"",description:"自定义浅色主题背景渐变"},customDarkGradient:{type:String,default:"",description:"自定义暗色主题背景渐变"},zIndex:{type:Number,default:0,description:"背景层级（z-index）"}},emits:["theme-change"],setup(e,{expose:t,emit:a}){const l=e,n=a,{isDarkTheme:o,backgroundStyle:c}=function(e){const t=i((()=>sa.value===Qt.DARK)),a=i((()=>t.value?{background:(null==e?void 0:e.customDarkGradient)||"linear-gradient(to top, #0a0a0f, #121218 60%, #1c1c26 100%)",zIndex:(null==e?void 0:e.zIndex)||0}:{background:(null==e?void 0:e.customLightGradient)||"linear-gradient(to top, var(--creamy-white-3), var(--creamy-white-2) 70%, rgba(232, 240, 242, 0.8) 100%)",zIndex:(null==e?void 0:e.zIndex)||0}));return{isDarkTheme:t,backgroundStyle:a}}({customLightGradient:l.customLightGradient,customDarkGradient:l.customDarkGradient,zIndex:l.zIndex}),{getCloudStyle:d,generateCloudPseudoElementsCSS:u}={getCloudStyle:e=>{const t=e%5;let a,l,n,o,i,r,s;0===t?(a=Math.floor(150*Math.random())+350,l=Math.floor(100*Math.random())+200,n=15,o=.65,i=[{top:"25%",left:"-10%",width:"60%",height:"60%",borderRadius:"70% 60% 65% 75%"},{top:"10%",left:"30%",width:"70%",height:"70%",borderRadius:"65% 75% 60% 70%"},{top:"35%",left:"75%",width:"45%",height:"45%",borderRadius:"65% 55% 70% 60%"},{top:"60%",left:"25%",width:"55%",height:"55%",borderRadius:"70% 65% 75% 60%"},{top:"45%",left:"52%",width:"48%",height:"48%",borderRadius:"60% 75% 65% 70%"}]):1===t?(a=Math.floor(200*Math.random())+400,l=Math.floor(80*Math.random())+140,n=18,o=.6,i=[{top:"30%",left:"5%",width:"50%",height:"55%",borderRadius:"80% 70% 75% 65%"},{top:"20%",left:"40%",width:"60%",height:"70%",borderRadius:"75% 80% 65% 70%"},{top:"35%",left:"60%",width:"40%",height:"60%",borderRadius:"70% 65% 80% 75%"},{top:"25%",left:"80%",width:"35%",height:"65%",borderRadius:"65% 75% 70% 80%"}]):2===t?(a=Math.floor(120*Math.random())+280,l=Math.floor(90*Math.random())+160,n=14,o=.7,i=[{top:"20%",left:"10%",width:"55%",height:"55%",borderRadius:"65% 70% 60% 75%"},{top:"15%",left:"45%",width:"65%",height:"65%",borderRadius:"75% 65% 70% 60%"},{top:"50%",left:"25%",width:"50%",height:"50%",borderRadius:"60% 75% 65% 70%"}]):3===t?(a=Math.floor(140*Math.random())+300,l=Math.floor(120*Math.random())+180,n=16,o=.63,i=[{top:"10%",left:"5%",width:"40%",height:"40%",borderRadius:"75% 65% 70% 60%"},{top:"5%",left:"35%",width:"45%",height:"45%",borderRadius:"70% 60% 75% 65%"},{top:"15%",left:"70%",width:"35%",height:"35%",borderRadius:"65% 70% 60% 75%"},{top:"50%",left:"10%",width:"38%",height:"38%",borderRadius:"70% 75% 65% 60%"},{top:"45%",left:"40%",width:"42%",height:"42%",borderRadius:"65% 60% 75% 70%"},{top:"40%",left:"75%",width:"30%",height:"30%",borderRadius:"75% 65% 60% 70%"}]):(a=Math.floor(180*Math.random())+320,l=Math.floor(70*Math.random())+130,n=13,o=.66,i=[{top:"25%",left:"0%",width:"45%",height:"45%",borderRadius:"65% 70% 75% 60%"},{top:"20%",left:"35%",width:"50%",height:"50%",borderRadius:"75% 65% 60% 70%"},{top:"30%",left:"65%",width:"40%",height:"40%",borderRadius:"70% 60% 75% 65%"}]),0===t?(r=40,s=65):1===t?(r=10,s=30):2===t?(r=25,s=50):3===t?(r=35,s=60):(r=5,s=25);const c=a/window.innerWidth*100,d=Math.random()*(100-c),u=r+Math.random()*(s-r);let m,p;return m=0===t||3===t?8*Math.random()-4:1===t?2*Math.random()-1:4*Math.random()-2,p=4===t||1===t?6:0===t||3===t?5:4,{width:`${a}px`,height:`${l}px`,left:`${d}%`,top:`${u}%`,opacity:o,filter:`blur(${n}px)`,transform:`rotate(${m}deg)`,zIndex:p,"--cloud-type":t,"--pseudo-elements":JSON.stringify(i),border:"none",outline:"none",boxShadow:"none",backgroundColor:"transparent"}},generateCloudPseudoElementsCSS:()=>{const e=document.createElement("style");return document.querySelectorAll(".cloud").forEach(((t,a)=>{const l=t,n=l.style.getPropertyValue("--pseudo-elements");if(n)try{const t=JSON.parse(n),o=`cloud-${a}`;l.classList.add(o);let i="";t.forEach(((e,t)=>{const a=(.2*Math.random()+.4).toFixed(2),l=Math.floor(10*Math.random())+12;if(Math.floor(15*Math.random()),0===t)i+=`.${o}::before { \n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left}; \n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.8) 0%,\n                  rgba(255, 255, 255, ${a}) 40%,\n                  rgba(255, 255, 255, 0.15) 75%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${l}px);\n                box-shadow: none;\n                opacity: ${a};\n                border: none;\n                outline: none;\n              }\n`;else if(1===t)i+=`.${o}::after { \n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left}; \n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.75) 0%,\n                  rgba(255, 255, 255, ${a}) 35%,\n                  rgba(255, 255, 255, 0.1) 70%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${l}px);\n                box-shadow: none;\n                opacity: ${a};\n                border: none;\n                outline: none;\n              }\n`;else{const a=(.2*Math.random()+.35).toFixed(2),l=Math.floor(8*Math.random())+10;i+=`.${o}::before { \n                content: ''; \n                position: absolute;\n                width: ${e.width}; \n                height: ${e.height}; \n                top: ${e.top}; \n                left: ${e.left};\n                border-radius: ${e.borderRadius};\n                background: radial-gradient(\n                  circle at center,\n                  rgba(255, 255, 255, 0.7) 0%,\n                  rgba(255, 255, 255, ${a}) 30%,\n                  rgba(255, 255, 255, 0.08) 65%,\n                  rgba(255, 255, 255, 0) 100%\n                );\n                filter: blur(${l}px);\n                box-shadow: none;\n                opacity: ${a};\n                z-index: ${t};\n                border: none;\n                outline: none;\n              }\n`}})),e.textContent+=i}catch(o){}})),document.head.appendChild(e),e}},{getStarStyle:p}={getStarStyle:e=>{const t=Math.floor(2*Math.random())+1;return{width:`${t}px`,height:`${t}px`,left:`${Math.floor(100*Math.random())}%`,top:`${Math.floor(100*Math.random())}%`,opacity:.5*Math.random()+.3,animation:e%5==0?"twinkle 3s infinite":"none"}}},{getDandelionSeedStyle:v}={getDandelionSeedStyle:e=>{const t=Math.floor(3*Math.random())+2,a=Math.floor(100*Math.random()),l=Math.floor(10*Math.random())-5,n=80+Math.floor(40*Math.random()),o=Math.floor(10*Math.random())+5,i=Math.floor(40*Math.random())-20,r=Math.floor(20*Math.random())+20;return{width:`${t}px`,height:`${t}px`,left:`${a}%`,bottom:`${l}%`,animationDelay:`${Math.floor(15*Math.random())}s`,filter:`blur(${1.5*Math.random()+.5}px)`,"--seed-type":e%3,"--animation-duration":`${r}s`,"--float-height":`${n}vh`,"--float-side":`${i}vw`,"--float-side-wave":`${o}vw`,"--rotation":Math.floor(360*Math.random())-180+"deg","--max-opacity":.2*Math.random()+.7,"--fluff-count":"1","--core-size":`${t}px`}}},{getFireflyStyle:w}={getFireflyStyle:e=>{const t=Math.floor(4*Math.random())+3,a=Math.floor(100*Math.random()),l=Math.floor(70*Math.random())+10,n=Math.floor(3*Math.random())+1,o=Math.floor(5*Math.random())+3,i=Math.floor(4*Math.random())+2,r=2*n+o+i,s=Math.floor(8*Math.random())+3,c=Math.floor(10*Math.random()),d=(30*Math.random()+10)*(Math.random()>.5?1:-1),u=(30*Math.random()+10)*(Math.random()>.5?1:-1),m=["#80ff72","#c4ff0e","#e8ff75","#00ffaa"],p=m[e%m.length],v=Math.floor(2*Math.random())+1;return{width:`${t}px`,height:`${t}px`,left:`${a}%`,top:`${l}%`,backgroundColor:p,"--glow-color":p,"--glow-size":`${Math.floor(6*Math.random())+7}px`,"--pulse-duration":`${v}s`,"--move-x":`${d}px`,"--move-y":`${u}px`,"--appear-duration":`${n}s`,"--move-duration":`${o}s`,"--pause-duration":`${i}s`,"--total-duration":`${r+s}s`,"--cycle-delay":`${s}s`,animationDelay:`${c}s`}}};return s(o,(e=>{n("theme-change",e?"dark":"light")})),r((()=>{u()})),t({isDarkTheme:o}),(t,a)=>(m(),f("div",{class:b(["background-animation",{"dark-theme":h(o)}]),style:k(h(c))},[g("div",Xs,[!h(o)&&e.enableClouds?(m(),f("div",Gs,[(m(!0),f(L,null,S(Math.ceil(e.cloudCount),(e=>(m(),f("div",{key:`cloud-${e}`,class:"cloud",style:k(h(d)(e))},null,4)))),128))])):C("",!0),h(o)&&e.enableStars?(m(),f("div",Zs,[(m(!0),f(L,null,S(e.particleCount,(e=>(m(),f("div",{key:`star-${e}`,class:"star",style:k(h(p)(e))},null,4)))),128))])):C("",!0)]),g("div",Qs,[!h(o)&&e.enableDandelions?(m(),f("div",ec,[(m(!0),f(L,null,S(e.particleCount,(e=>(m(),f("div",{key:`dandelion-${e}`,class:"dandelion-seed",style:k(h(v)(e))},a[0]||(a[0]=[g("div",{class:"main-stem"},null,-1)]),4)))),128))])):C("",!0),h(o)&&e.enableFireflies?(m(),f("div",tc,[(m(!0),f(L,null,S(e.particleCount,(e=>(m(),f("div",{key:`firefly-${e}`,class:"firefly",style:k(h(w)(e))},null,4)))),128))])):C("",!0)])],6))}}),[["__scopeId","data-v-a753bb5a"]]),lc={class:"email-code-container"},nc={class:"turnstile-section"},oc={class:"forgot-password-actions"},ic=Ua(o({__name:"ForgotPasswordModal",props:{show:{type:Boolean}},emits:["update:show","success"],setup(e,{emit:t}){const a=e,l=t,o=n(),r=n(),c=n(!1),d=n(!1),f=n("发送验证码"),b=n(0),y=n({email:"",emailCode:"",newPassword:"",confirmPassword:""}),k=i({get:()=>a.show,set:e=>l("update:show",e)}),x=i((()=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(y.value.email))),C=n(""),L=i((()=>!!C.value)),S={email:[{required:!0,message:"邮箱不能为空",trigger:"blur"},{pattern:/^[^\s@]+@[^\s@]+\.[^\s@]+$/,message:"请输入有效的邮箱地址",trigger:"blur"}],emailCode:[{required:!0,message:"验证码不能为空",trigger:"blur"},{min:6,max:6,message:"验证码长度为6位",trigger:"blur"}],newPassword:[{required:!0,message:"新密码不能为空",trigger:"blur"},{min:6,message:"密码长度至少为 6 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认新密码",trigger:"blur"},{validator:(e,t)=>t===y.value.newPassword,message:"两次密码输入不一致",trigger:["blur","password-input"]}]},T=e=>{Zt.debug("Turnstile 验证成功:",e),C.value=e},M=()=>{Zt.debug("Turnstile 验证失败"),C.value=""},R=async()=>{var e;if(y.value.email)if(x.value)if(L.value)try{d.value=!0;const e=await bi.sendEmailCode({email:y.value.email,type:Vs.FORGOT});e.success?(ba.success("验证码发送成功，请查收邮件"),A()):(ba.error(e.message||"验证码发送失败"),d.value=!1)}catch{C.value="",null==(e=r.value)||e.reset(),d.value=!1}else ba.warning("请先通过验证哦~");else ba.warning("请输入有效的邮箱地址");else ba.warning("请先输入邮箱地址")},A=()=>{b.value=60,f.value=`${b.value}s后重发`;const e=setInterval((()=>{b.value--,b.value>0?f.value=`${b.value}s后重发`:(f.value="发送验证码",d.value=!1,clearInterval(e))}),1e3)},_=()=>{o.value.validate((e=>{if(!e){const e=C.value;if(!e)return void ba.warning("请先通过验证哦~");c.value=!0,bi.resetPassword({email:y.value.email,emailCode:y.value.emailCode,newPassword:y.value.newPassword,cftt:e}).then((e=>{e.success?(ba.success("密码重置成功，请使用新密码登录"),I(),l("success")):ba.error(e.message||"密码重置失败")})).catch((()=>{var e;C.value="",null==(e=r.value)||e.reset()})).finally((()=>{c.value=!1}))}}))},I=()=>{k.value=!1,y.value={email:"",emailCode:"",newPassword:"",confirmPassword:""},C.value=""};return s(k,(e=>{e||(d.value=!1,f.value="发送验证码",b.value=0)})),(e,t)=>(m(),u(h(re),{show:k.value,"onUpdate:show":t[4]||(t[4]=e=>k.value=e),preset:"dialog",title:"忘记密码"},{header:p((()=>t[5]||(t[5]=[g("div",{class:"forgot-password-header"},[g("span",null,"重置密码")],-1)]))),action:p((()=>[g("div",oc,[v(h(ne),{onClick:I},{default:p((()=>t[6]||(t[6]=[E("取消")]))),_:1}),v(h(ne),{type:"primary",onClick:_,loading:c.value},{default:p((()=>t[7]||(t[7]=[E(" 重置密码 ")]))),_:1},8,["loading"])])])),default:p((()=>[v(h(ce),{model:y.value,rules:S,ref_key:"formRef",ref:o,"label-placement":"left","label-width":100},{default:p((()=>[v(h(de),{label:"邮箱",path:"email"},{default:p((()=>[v(h(ie),{value:y.value.email,"onUpdate:value":t[0]||(t[0]=e=>y.value.email=e),placeholder:"请输入注册时的邮箱地址"},null,8,["value"])])),_:1}),v(h(de),{label:"验证码",path:"emailCode"},{default:p((()=>[g("div",lc,[v(h(ie),{value:y.value.emailCode,"onUpdate:value":t[1]||(t[1]=e=>y.value.emailCode=e),placeholder:"请输入邮箱验证码",maxlength:"6"},null,8,["value"]),v(h(ne),{disabled:d.value||!x.value||!L.value,onClick:R,text:"",type:"primary",size:"small",class:"send-code-btn"},{default:p((()=>[E(w(f.value),1)])),_:1},8,["disabled"])])])),_:1}),v(h(de),{label:"新密码",path:"newPassword"},{default:p((()=>[v(h(ie),{value:y.value.newPassword,"onUpdate:value":t[2]||(t[2]=e=>y.value.newPassword=e),type:"password",placeholder:"请输入新密码","show-password-on":"click"},null,8,["value"])])),_:1}),v(h(de),{label:"确认密码",path:"confirmPassword"},{default:p((()=>[v(h(ie),{value:y.value.confirmPassword,"onUpdate:value":t[3]||(t[3]=e=>y.value.confirmPassword=e),type:"password",placeholder:"请再次输入新密码","show-password-on":"click"},null,8,["value"])])),_:1}),g("div",nc,[v(Fs,{ref_key:"turnstileRef",ref:r,onSuccess:T,onError:M},null,512)])])),_:1},8,["model"])])),_:1},8,["show"]))}}),[["__scopeId","data-v-9d1dce3c"]]),rc={class:"layout-container"},sc={class:"header-container"},cc={class:"card-container"},dc={class:"footer-container"},uc=Ua(o({__name:"Login",props:{defaultLoginMode:{default:"phone"},showRegisterForm:{type:Boolean,default:!1}},emits:["login-success","register-success","forgot-password-success"],setup(e,{expose:t,emit:a}){const l=e,o=a,r=n({isFlipped:l.showRegisterForm,loginMode:l.defaultLoginMode,loginLoading:!1,showForgotPassword:!1}),c=n({phone:"",email:"",emailCode:"",password:""}),d=n({username:"",phone:"",email:"",emailCode:"",password:"",reenteredPassword:"",job:""}),u=n({sendCodeDisabled:!1,sendCodeText:"发送验证码",countdown:0}),w=n({loginTurnstileToken:"",registerTurnstileToken:""}),b=n(),y=n(),x=()=>{r.value.isFlipped=!r.value.isFlipped},C=e=>{Zt.debug("Turnstile 验证成功:",e),r.value.isFlipped?w.value.registerTurnstileToken=e:w.value.loginTurnstileToken=e},L=()=>{Zt.debug("Turnstile 验证失败"),r.value.isFlipped?w.value.registerTurnstileToken="":w.value.loginTurnstileToken="",ba.warning("验证失败，请重试")},S=i((()=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c.value.email))),T=i((()=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(d.value.email))),M=i((()=>!!w.value.loginTurnstileToken)),R=i((()=>!!w.value.registerTurnstileToken));s((()=>r.value.loginMode),(()=>{c.value={phone:"",email:"",emailCode:"",password:""},u.value={sendCodeDisabled:!1,sendCodeText:"发送验证码",countdown:0}}));const A=async e=>{var t,a;let l="",n=!1;if(e===Vs.LOGIN?(l=c.value.email,n=M.value):e===Vs.REGISTER&&(l=d.value.email,n=R.value),l)if(n){if(!u.value.sendCodeDisabled)try{u.value.sendCodeDisabled=!0;const t=await bi.sendEmailCode({email:l,type:e});t.success?(ba.success("验证码发送成功，请查收邮件"),E()):(ba.error(t.message||"验证码发送失败"),u.value.sendCodeDisabled=!1)}catch{e===Vs.LOGIN?(w.value.loginTurnstileToken="",null==(t=b.value)||t.resetTurnstile()):(w.value.registerTurnstileToken="",null==(a=y.value)||a.resetTurnstile()),u.value.sendCodeDisabled=!1}}else ba.warning("请先通过验证哦~");else ba.warning("请先输入邮箱地址")},E=()=>{u.value.countdown=60,u.value.sendCodeText=`${u.value.countdown}s后重发`;const e=setInterval((()=>{u.value.countdown--,u.value.countdown>0?u.value.sendCodeText=`${u.value.countdown}s后重发`:(u.value.sendCodeText="发送验证码",u.value.sendCodeDisabled=!1,clearInterval(e))}),1e3)},_=()=>{const e=b.value;e&&e.validate((e=>{if(!e){const e=w.value.loginTurnstileToken;if(!e)return void ba.warning("请先通过验证哦~");r.value.loginLoading=!0;let t={cftt:e};"phone"===r.value.loginMode?t={cftt:e,phone:c.value.phone,password:c.value.password}:"email"===r.value.loginMode&&(t={cftt:e,email:c.value.email,emailCode:c.value.emailCode}),bi.login(t).then((e=>{(null==e?void 0:e.data)&&(aa.setLoginUser(e.data),aa.set(xi,!0),Ic.push("/"),o("login-success",e.data))})).catch((()=>{var e;w.value.loginTurnstileToken="",null==(e=b.value)||e.resetTurnstile()})).finally((()=>{r.value.loginLoading=!1}))}}))},I=()=>{const e=y.value;e&&e.validate((e=>{if(!e){const e=w.value.registerTurnstileToken;if(!e)return void ba.warning("请先通过验证哦~");bi.register({...d.value,cftt:e,confirmPassword:d.value.reenteredPassword}).then((e=>{e.data&&(aa.setLoginUser(e.data),aa.set(xi,!0),Ic.push("/"),o("register-success",e.data))})).catch((()=>{var e;w.value.registerTurnstileToken="",null==(e=y.value)||e.resetTurnstile()}))}}))},z=()=>{r.value.loginMode="phone",o("forgot-password-success")};s((()=>r.value.loginMode),(()=>{var e;const t=b.value;t&&t.restoreValidation(),w.value.loginTurnstileToken="",null==(e=b.value)||e.resetTurnstile()}));const B=i((()=>({width:"23rem",transition:"transform 0.6s, box-shadow 0.3s",transformStyle:"preserve-3d",transform:r.value.isFlipped?"rotateY(180deg)":"none",backgroundColor:"var(--creamy-white-1)",boxShadow:"0 8px 30px rgba(0, 0, 0, 0.12)",backdropFilter:"blur(5px)",border:"1px solid rgba(255, 255, 255, 0.2)",opacity:.8})));return t({switchToLogin:()=>{r.value.isFlipped=!1},switchToRegister:()=>{r.value.isFlipped=!0},resetAllForms:()=>{var e,t,a,l;c.value={phone:"",email:"",emailCode:"",password:""},d.value={username:"",phone:"",email:"",emailCode:"",password:"",reenteredPassword:"",job:""},u.value={sendCodeDisabled:!1,sendCodeText:"发送验证码",countdown:0},w.value={loginTurnstileToken:"",registerTurnstileToken:""},null==(e=b.value)||e.restoreValidation(),null==(t=y.value)||t.restoreValidation(),null==(a=b.value)||a.resetTurnstile(),null==(l=y.value)||l.resetTurnstile()}}),(e,t)=>(m(),f("div",rc,[v(ac,{particleCount:40}),g("div",sc,[v(h(He),{width:"200","preview-disabled":"",src:h(Us),loading:!0,"fallback-src":h(Us)},null,8,["src","fallback-src"])]),g("div",cc,[v(h(Re),{hoverable:"",style:k(B.value)},{default:p((()=>[D(v(Ws,{form:c.value,"onUpdate:form":t[0]||(t[0]=e=>c.value=e),"login-mode":r.value.loginMode,"onUpdate:loginMode":t[1]||(t[1]=e=>r.value.loginMode=e),"email-code-state":u.value,"is-valid-email":S.value,"is-turnstile-verified":M.value,loading:r.value.loginLoading,onLogin:_,onFlipCard:x,onShowForgotPassword:t[2]||(t[2]=e=>r.value.showForgotPassword=!0),onSendEmailCode:A,onTurnstileSuccess:C,onTurnstileError:L,ref_key:"loginFormRef",ref:b},null,8,["form","login-mode","email-code-state","is-valid-email","is-turnstile-verified","loading"]),[[$,!r.value.isFlipped]]),D(v(Ks,{form:d.value,"onUpdate:form":t[3]||(t[3]=e=>d.value=e),"email-code-state":u.value,"is-valid-email":T.value,"is-turnstile-verified":R.value,onRegister:I,onFlipCard:x,onSendEmailCode:A,onTurnstileSuccess:C,onTurnstileError:L,ref_key:"registerFormRef",ref:y},null,8,["form","email-code-state","is-valid-email","is-turnstile-verified"]),[[$,r.value.isFlipped]])])),_:1},8,["style"])]),g("div",dc,[v(ki)]),v(h(ic),{show:r.value.showForgotPassword,"onUpdate:show":t[4]||(t[4]=e=>r.value.showForgotPassword=e),onSuccess:z},null,8,["show"])]))}}),[["__scopeId","data-v-5753cc41"]]),mc=Ua(o({__name:"CreatePrivilegeButton",emits:["click"],setup(e,{emit:t}){const a=t,l=n(!1),o=n(null),i=n(null),s=()=>{a("click")},c=()=>{l.value||(i.value&&(clearTimeout(i.value),i.value=null),l.value=!0,o.value=window.setTimeout((()=>{l.value=!1,p()}),1500))},d=()=>{l.value||p()},p=()=>{i.value&&(clearTimeout(i.value),i.value=null);const e=5e3+1e4*Math.random();i.value=window.setTimeout((()=>{v()}),e)},v=()=>{l.value?p():(l.value=!0,o.value=window.setTimeout((()=>{l.value=!1,p()}),1500))};return r((()=>{p()})),z((()=>{o.value&&(clearTimeout(o.value),o.value=null),i.value&&(clearTimeout(i.value),i.value=null)})),(e,t)=>(m(),u(h(mn),{size:36,color:"var(--blue)",onClick:s,class:b(["cursor-pointer create-privilege-button",{"is-rotating":l.value}]),ref:"createButtonRef",onMouseenter:c,onMouseleave:d},null,8,["class"]))}}),[["__scopeId","data-v-942c6528"]]);const pc=0,vc=1,hc={[pc]:"短信验证",[vc]:"二维码验证"};function gc(e){const t=e-Date.now();if(t<=0)return"已过期";if(t<6e4)return"即将过期";if(t<36e5)return`剩余${Math.floor(t/6e4)}分钟`;if(t<864e5)return`剩余${Math.floor(t/36e5)}小时`;if(t<2592e6)return`剩余${Math.floor(t/864e5)}天`;if(t<31104e6){return`剩余${Math.floor(t/2592e6)}个月`}return`剩余${Math.floor(t/31104e6)}年`}const fc={class:"article-header"},wc=["onClick"],bc={class:"flex-between-center"},yc={class:"privilege-expire-time"},kc={class:"article-content"},xc={class:"privilege-description"},Cc={class:"infinite-load-info"},Lc=Ua(o({__name:"PrivilegeList",props:{searchCondition:{}},emits:["reset"],setup(e,{expose:t,emit:a}){const l=e,o=a,d=Be(),b=n([]),y=n(!1),x=n(!1),T=n(!0),M=n(void 0),R=n(),A=n(),{currentTheme:_}=function(){const e=n(sa.value),t=[{name:Qt.LIGHT,displayName:"浅色主题",description:"明亮清新的浅色界面",primaryColor:"#1890ff",isDark:!1},{name:Qt.DARK,displayName:"深色主题",description:"护眼舒适的深色界面",primaryColor:"#177ddc",isDark:!0}],a=i((()=>e.value===Qt.DARK)),l=i((()=>e.value===Qt.LIGHT)),o=i((()=>t)),r=i((()=>t.find((t=>t.name===e.value)))),c=t=>{e.value=t,t!==sa.value&&da()};return s(sa,(t=>{e.value=t}),{immediate:!0}),{currentTheme:e,isDarkTheme:a,isLightTheme:l,availableThemes:o,currentThemeConfig:r,toggleTheme:()=>{const e=a.value?Qt.LIGHT:Qt.DARK;c(e)},setTheme:c,setDarkTheme:()=>{c(Qt.DARK)},setLightTheme:()=>{c(Qt.LIGHT)},setSystemTheme:()=>{if("undefined"!=typeof window&&window.matchMedia){const e=window.matchMedia("(prefers-color-scheme: dark)").matches;c(e?Qt.DARK:Qt.LIGHT)}},watchSystemTheme:()=>{if("undefined"==typeof window||!window.matchMedia)return()=>{};const e=window.matchMedia("(prefers-color-scheme: dark)"),t=e=>{c(e.matches?Qt.DARK:Qt.LIGHT)};return e.addEventListener("change",t),()=>{e.removeEventListener("change",t)}}}}(),I=["rgba(255, 182, 193, 0.3)","rgba(173, 216, 230, 0.3)","rgba(144, 238, 144, 0.3)","rgba(255, 218, 185, 0.3)","rgba(221, 160, 221, 0.3)","rgba(255, 255, 224, 0.3)"],P=["rgba(139, 69, 19, 0.3)","rgba(25, 25, 112, 0.3)","rgba(0, 100, 0, 0.3)","rgba(128, 0, 128, 0.3)","rgba(165, 42, 42, 0.3)","rgba(184, 134, 11, 0.3)"],D=i((()=>_.value===Qt.DARK)),$=(e,t)=>{const a=D.value?P:I;return a[t%a.length]},U=n(24),F=n(1),V=()=>{const e=window.innerWidth;let t=24,a=1;e>=2400?(t=3,a=8):e>=1800?(t=4,a=6):e>=1400?(t=6,a=4):e>=1e3?(t=8,a=3):e>=600?(t=12,a=2):(t=24,a=1),U.value=t,F.value=a},O={toggleTimeFormat:e=>{void 0===e.showExactExpireTime?e.showExactExpireTime=!0:e.showExactExpireTime=!e.showExactExpireTime}},H=e=>{e.link&&window.open(e.link,"_blank")},j=async()=>{!T.value||y.value||x.value||await N(!0)},N=async(e=!1)=>{if(!(y.value||e&&x.value)){e?x.value=!0:(y.value=!0,b.value=[],M.value=void 0,T.value=!0);try{const t={...l.searchCondition.value,id:e?M.value:void 0,loadSize:20},a=await gs.search(t);if(a.success&&a.data){const t=a.data.map((e=>{return{...e,exactExpireTime:(t=e.expireTime,oi.toTimeString(t.toString(),"YYYY-MM-DD HH:mm:ss")),relativeExpireTime:gc(e.expireTime)};var t}));e?b.value.push(...t):b.value=t,t.length>0&&(M.value=t[t.length-1].id),T.value=20===t.length}else d.error("获取特权列表失败")}catch(t){d.error("搜索失败，请稍后重试")}finally{y.value=!1,x.value=!1}}};return r((async()=>{await c(),V(),window.addEventListener("resize",V)})),z((()=>{window.removeEventListener("resize",V)})),t({search:N,reset:()=>{b.value=[],M.value=void 0,T.value=!0,o("reset")},getLoadingState:()=>y.value||x.value}),(e,t)=>(m(),f("div",{class:"article-container",ref_key:"containerRef",ref:R},[v(h(Ce),{onLoad:j,distance:100,class:"infinite-scroll-container",ref_key:"scrollContainerRef",ref:A},{default:p((()=>[v(h(Ee),{gutter:20,style:{width:"100%","box-sizing":"border-box",margin:"0 auto",padding:"0 0.25rem",flex:"1","overflow-y":"auto"}},{default:p((()=>[(m(!0),f(L,null,S(b.value,((e,t)=>(m(),u(h(_e),{key:e.id,span:U.value},{default:p((()=>[v(h(Re),{class:"card-item cursor-pointer privilege-card compact-card",onClick:t=>H(e),"header-style":"padding-bottom:0.125rem;border-bottom: var(--border-1);",style:k({backgroundColor:$(e.id.toString(),t)})},{header:p((()=>[g("div",fc,[g("div",{class:"article-title",onClick:B((t=>H(e)),["stop"])},w(e.name),9,wc)])])),"header-extra":p((()=>[v(h(se),{round:"",size:32,src:e.icon||"/default-privilege-icon.png","object-fit":"cover",class:"article-avatar"},null,8,["src"])])),default:p((()=>[g("div",bc,[g("div",null,[v(h(Me),{type:0===e.verificationType?"info":"success",class:"card-tag"},{default:p((()=>{return[E(w((t=e.verificationType,hc[t]||"未知")),1)];var t})),_:2},1032,["type"])]),g("div",yc,[v(h(Me),{type:"warning",size:"small",class:"time-clickable",onClick:B((t=>h(O).toggleTimeFormat(e)),["stop"])},{default:p((()=>[E(w(e.showExactExpireTime?e.exactExpireTime:e.relativeExpireTime),1)])),_:2},1032,["onClick"])])]),g("div",kc,[v(h(ae),{style:{"padding-right":"0.5rem"}},{default:p((()=>[g("div",xc,w(e.description),1)])),_:2},1024)])])),_:2},1032,["onClick","style"])])),_:2},1032,["span"])))),128))])),_:1}),g("div",Cc,[y.value?(m(),u(h(Le),{key:0,class:"display-flex"})):C("",!0),0!==b.value.length||y.value?C("",!0):(m(),u(h(Se),{key:1,description:"暂无特权数据"})),!T.value&&b.value.length>0?(m(),u(h(Se),{key:2,description:"没有更多特权了..."})):C("",!0)])])),_:1},512)],512))}}),[["__scopeId","data-v-e90fdcab"]]),Sc="privilege_search_condition";const Tc={class:"home-layout"},Mc={class:"home-layout-top"},Rc={class:"middle-controls-container"},Ac={class:"tag-bar-wrapper"},Ec={class:"home-layout-content"},_c=Ua(o({__name:"Privilege",props:{initialSearchCondition:{default:void 0},autoLoad:{type:Boolean,default:!0}},emits:["search-condition-change","privilege-create-success"],setup(e,{expose:t,emit:a}){const l=W((()=>Tr((()=>Promise.resolve().then((()=>Ts))),void 0))),o=e,d=a,u=function(){const e=Be(),t=n(!1),a=n(!1),l=n(null),o=n({searchKey:"",owner:!1,interaction:!1,favorite:!1,tag:""}),r=i((()=>""!==o.value.searchKey.trim()||o.value.owner||o.value.interaction||o.value.favorite||!!o.value.tag&&""!==o.value.tag.trim())),s=async(n,o=!1)=>{if(n){l.value&&l.value.abort(),l.value=new AbortController,t.value=!0,a.value=!0;try{await n.search(o)}catch(i){i instanceof Error&&"AbortError"!==i.name&&e.error("搜索失败，请稍后重试")}finally{t.value=!1,a.value=!1,l.value=null}}},c=()=>{aa.set(Sc,o.value)};return{isLoading:t,isSearching:a,searchCondition:o,hasSearchCondition:r,search:s,handleTagSelected:async(e,t)=>{o.value.tag=e,c(),await s(t)},saveSearchCondition:c,loadSearchCondition:()=>{const e=aa.get(Sc);e&&(o.value={...o.value,...e})},cleanup:()=>{l.value&&(l.value.abort(),l.value=null)}}}(),p=function(){const e=n(null),t=n(null);return{privilegeModalRef:e,privilegeListRef:t,initializeState:()=>{},openCreatePrivilegeDialog:()=>{e.value&&e.value.open()},resetPrivilegeList:()=>{t.value&&t.value.reset()},cleanup:()=>{}}}(),w=n(null),b=n(null),y=n(!1);s(w,(e=>{p.privilegeModalRef.value=e}),{immediate:!0}),s(b,(e=>{p.privilegeListRef.value=e}),{immediate:!0}),r((async()=>{p.initializeState(),o.initialSearchCondition?(u.searchCondition.value={...u.searchCondition.value,...o.initialSearchCondition},u.saveSearchCondition()):u.loadSearchCondition(),await c(),o.autoLoad&&setTimeout((async()=>{await k()}),50)})),z((()=>{u.cleanup(),p.cleanup()}));const k=async(e=!1)=>{Zt.debug("执行特权搜索操作:",{privilegeListRef:p.privilegeListRef.value,loadMore:e}),await c(),u.search(p.privilegeListRef.value,e),d("search-condition-change",u.searchCondition.value)},x=async()=>{p.resetPrivilegeList(),await k(),d("privilege-create-success")};return t({performSearch:async(e=!1)=>{await k(e)},resetPrivilegeList:()=>{p.resetPrivilegeList()},getCurrentSearchCondition:()=>u.searchCondition.value}),(e,t)=>(m(),f("div",Tc,[g("div",Mc,[g("div",Rc,[v(rs,{modelValue:h(u).searchCondition.value,"onUpdate:modelValue":t[0]||(t[0]=e=>h(u).searchCondition.value=e),placeholder:"搜索特权内容",onSearch:k},null,8,["modelValue"]),v(mc,{onClick:h(p).openCreatePrivilegeDialog},null,8,["onClick"])]),v(h(l),{ref_key:"privilegeModalRef",ref:w,modelValue:y.value,"onUpdate:modelValue":t[1]||(t[1]=e=>y.value=e),onSuccess:x},null,8,["modelValue"]),v(Pi)]),g("div",Ac,[v(cs,{modelValue:h(u).searchCondition.value.tag,"onUpdate:modelValue":t[2]||(t[2]=e=>h(u).searchCondition.value.tag=e),onTagSelected:t[3]||(t[3]=e=>h(u).handleTagSelected(e,h(p).privilegeListRef.value))},null,8,["modelValue"])]),g("div",Ec,[v(Lc,{"search-condition":h(u).searchCondition,ref_key:"privilegeListRef",ref:b,onReset:h(p).resetPrivilegeList},null,8,["search-condition","onReset"])])]))}}),[["__scopeId","data-v-257a4cf7"]]),Ic=qe({history:Ye("/"),routes:[{path:"/login",name:"Login",component:uc,meta:{requiresAuth:!1}},{path:"/",name:"Home",component:$s,meta:{requiresAuth:!0}},{path:"/article/:articleId/:commentId?",name:"Article",component:Lr,meta:{requiresAuth:!0}},{path:"/privilege",name:"Privilege",component:_c,meta:{requiresAuth:!0}}]}),zc="Shenmo";Ic.beforeEach(((e,t,a)=>{var l;document.title=`${zc} - ${e.name}`;const n=function(e){const t=document.cookie.split("; ");for(const a of t){const t=a.split("=");if(t[0]===e)return t[1]}return null}("wentk"),o=Nn(),i=qn();if(o.setId(""),i.setId(""),(null==(l=e.meta)?void 0:l.requiresAuth)&&!n)a({name:"Login"});else if("Login"===e.name&&n)a({name:"Home"});else{if("Article"===e.name){const{articleId:t,commentId:a}=e.params;o.setId(String(t)),null!=a&&i.setId(String(a)),jn.title(o.getId).then((e=>{document.title=`${zc} - ${e.data}`}))}a()}}));H(pa).use(Ic).use(l()).mount("#app");export{Ua as _,gs as e};
